{"cells": [{"cell_type": "markdown", "id": "5abe2121-5381-46d7-a849-66f921883972", "metadata": {}, "source": ["# Lang<PERSON>hain 核心模块：Data Conneciton - Vector Stores\n", "\n", "存储和搜索非结构化数据最常见的方法之一是将其嵌入并存储生成的嵌入向量，然后在查询时将非结构化查询进行嵌入，并检索与嵌入查询“最相似”的嵌入向量。\n", "\n", "向量存储库负责为您存储已经过嵌入处理的数据并执行向量搜索。\n", "\n", "\n", "![](https://python.langchain.com/assets/images/vector_stores-125d1675d58cfb46ce9054c9019fea72.jpg)\n", "\n", "\n", "下面以 `Chroma` 为例展示功能和用法"]}, {"cell_type": "code", "execution_count": 1, "id": "f994c4f8-58cf-4d34-b58f-205b42535177", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: chromadb in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (0.3.25)\n", "Requirement already satisfied: onnxruntime>=1.14.1 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from chromadb) (1.15.0)\n", "Requirement already satisfied: typing-extensions>=4.5.0 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from chromadb) (4.6.2)\n", "Requirement already satisfied: requests>=2.28 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from chromadb) (2.31.0)\n", "Requirement already satisfied: uvicorn[standard]>=0.18.3 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from chromadb) (0.22.0)\n", "Requirement already satisfied: tqdm>=4.65.0 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from chromadb) (4.65.0)\n", "Requirement already satisfied: pydantic>=1.9 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from chromadb) (1.10.8)\n", "Requirement already satisfied: tokenizers>=0.13.2 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from chromadb) (0.13.3)\n", "Requirement already satisfied: duckdb>=0.7.1 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from chromadb) (0.8.0)\n", "Requirement already satisfied: pandas>=1.3 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from chromadb) (2.1.3)\n", "Requirement already satisfied: hnswlib>=0.7 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from chromadb) (0.7.0)\n", "Requirement already satisfied: clickhouse-connect>=0.5.7 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from chromadb) (0.5.25)\n", "Requirement already satisfied: numpy>=1.21.6 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from chromadb) (1.26.2)\n", "Requirement already satisfied: posthog>=2.4.0 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from chromadb) (3.0.1)\n", "Requirement already satisfied: overrides>=7.3.1 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from chromadb) (7.3.1)\n", "Requirement already satisfied: fastapi>=0.85.1 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from chromadb) (0.100.1)\n", "Requirement already satisfied: pytz in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from clickhouse-connect>=0.5.7->chromadb) (2023.3)\n", "Requirement already satisfied: certifi in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from clickhouse-connect>=0.5.7->chromadb) (2023.5.7)\n", "Requirement already satisfied: zstandard in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from clickhouse-connect>=0.5.7->chromadb) (0.21.0)\n", "Requirement already satisfied: lz4 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from clickhouse-connect>=0.5.7->chromadb) (4.3.2)\n", "Requirement already satisfied: urllib3>=1.26 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from clickhouse-connect>=0.5.7->chromadb) (1.26.16)\n", "Requirement already satisfied: starlette<0.28.0,>=0.27.0 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from fastapi>=0.85.1->chromadb) (0.27.0)\n", "Requirement already satisfied: sympy in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from onnxruntime>=1.14.1->chromadb) (1.12)\n", "Requirement already satisfied: flatbuffers in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from onnxruntime>=1.14.1->chromadb) (23.5.26)\n", "Requirement already satisfied: packaging in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from onnxruntime>=1.14.1->chromadb) (23.1)\n", "Requirement already satisfied: protobuf in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from onnxruntime>=1.14.1->chromadb) (3.20.3)\n", "Requirement already satisfied: coloredlogs in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from onnxruntime>=1.14.1->chromadb) (15.0.1)\n", "Requirement already satisfied: tzdata>=2022.1 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from pandas>=1.3->chromadb) (2023.3)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from pandas>=1.3->chromadb) (2.8.2)\n", "Requirement already satisfied: monotonic>=1.5 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from posthog>=2.4.0->chromadb) (1.6)\n", "Requirement already satisfied: six>=1.5 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from posthog>=2.4.0->chromadb) (1.16.0)\n", "Requirement already satisfied: backoff>=1.10.0 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from posthog>=2.4.0->chromadb) (2.2.1)\n", "Requirement already satisfied: idna<4,>=2.5 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from requests>=2.28->chromadb) (3.4)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from requests>=2.28->chromadb) (3.1.0)\n", "Requirement already satisfied: click>=7.0 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from uvicorn[standard]>=0.18.3->chromadb) (8.1.3)\n", "Requirement already satisfied: h11>=0.8 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from uvicorn[standard]>=0.18.3->chromadb) (0.14.0)\n", "Requirement already satisfied: websockets>=10.4 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from uvicorn[standard]>=0.18.3->chromadb) (10.4)\n", "Requirement already satisfied: httptools>=0.5.0 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from uvicorn[standard]>=0.18.3->chromadb) (0.5.0)\n", "Requirement already satisfied: pyyaml>=5.1 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from uvicorn[standard]>=0.18.3->chromadb) (6.0)\n", "Requirement already satisfied: watchfiles>=0.13 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from uvicorn[standard]>=0.18.3->chromadb) (0.19.0)\n", "Requirement already satisfied: python-dotenv>=0.13 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from uvicorn[standard]>=0.18.3->chromadb) (1.0.0)\n", "Requirement already satisfied: uvloop!=0.15.0,!=0.15.1,>=0.14.0 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from uvicorn[standard]>=0.18.3->chromadb) (0.17.0)\n", "Requirement already satisfied: anyio<5,>=3.4.0 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from starlette<0.28.0,>=0.27.0->fastapi>=0.85.1->chromadb) (3.6.2)\n", "Requirement already satisfied: humanfriendly>=9.1 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from coloredlogs->onnxruntime>=1.14.1->chromadb) (10.0)\n", "Requirement already satisfied: mpmath>=0.19 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from sympy->onnxruntime>=1.14.1->chromadb) (1.3.0)\n", "Requirement already satisfied: sniffio>=1.1 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from anyio<5,>=3.4.0->starlette<0.28.0,>=0.27.0->fastapi>=0.85.1->chromadb) (1.3.0)\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv\u001b[0m\u001b[33m\n", "\u001b[0m"]}], "source": ["# 安装必要依赖包\n", "!pip install chromadb"]}, {"cell_type": "markdown", "id": "b68fdbcb-b60d-441f-91fc-d8cac24ba3e1", "metadata": {}, "source": ["## 使用 Chroma 作为向量数据库，实现语义搜索\n"]}, {"cell_type": "code", "execution_count": 2, "id": "8dadd89b-6a13-4391-9102-acde028b61d5", "metadata": {}, "outputs": [], "source": ["from langchain.document_loaders import TextLoader\n", "from langchain.embeddings.openai import OpenAIEmbeddings\n", "from langchain.text_splitter import CharacterTextSplitter\n", "from langchain.vectorstores import Chroma\n", "\n", "# 加载长文本\n", "raw_documents = TextLoader('../tests/state_of_the_union.txt').load()"]}, {"cell_type": "code", "execution_count": 3, "id": "24f9c721-dfd3-4632-a89e-92d2fa9b3594", "metadata": {}, "outputs": [], "source": ["# 实例化文本分割器\n", "text_splitter = CharacterTextSplitter(chunk_size=200, chunk_overlap=0)"]}, {"cell_type": "code", "execution_count": 4, "id": "67076c7d-54cc-47a9-a5bd-85570355a7d2", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Created a chunk of size 215, which is longer than the specified 200\n", "Created a chunk of size 232, which is longer than the specified 200\n", "Created a chunk of size 242, which is longer than the specified 200\n", "Created a chunk of size 219, which is longer than the specified 200\n", "Created a chunk of size 304, which is longer than the specified 200\n", "Created a chunk of size 205, which is longer than the specified 200\n", "Created a chunk of size 332, which is longer than the specified 200\n", "Created a chunk of size 215, which is longer than the specified 200\n", "Created a chunk of size 203, which is longer than the specified 200\n", "Created a chunk of size 281, which is longer than the specified 200\n", "Created a chunk of size 201, which is longer than the specified 200\n", "Created a chunk of size 250, which is longer than the specified 200\n", "Created a chunk of size 325, which is longer than the specified 200\n", "Created a chunk of size 242, which is longer than the specified 200\n"]}], "source": ["# 分割文本\n", "documents = text_splitter.split_documents(raw_documents)"]}, {"cell_type": "code", "execution_count": 5, "id": "19d367b7-442e-4f14-9c12-62764456d595", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}}, "outputs": [{"data": {"text/plain": ["[Document(page_content='<PERSON>am Speaker, <PERSON>am Vice President, our First Lady and Second Gentleman. Members of Congress and the Cabinet. Justices of the Supreme Court. My fellow Americans.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Last year COVID-19 kept us apart. This year we are finally together again. \\n\\nTonight, we meet as Democrats Republicans and Independents. But most importantly as Americans.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='With a duty to one another to the American people to the Constitution. \\n\\nAnd with an unwavering resolve that freedom will always triumph over tyranny.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Six days ago, Russia’s <PERSON> sought to shake the foundations of the free world thinking he could make it bend to his menacing ways. But he badly miscalculated.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='He thought he could roll into Ukraine and the world would roll over. Instead he met a wall of strength he never imagined. \\n\\nHe met the Ukrainian people.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='From President <PERSON><PERSON><PERSON><PERSON> to every Ukrainian, their fearlessness, their courage, their determination, inspires the world.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Groups of citizens blocking tanks with their bodies. Everyone from students to retirees teachers turned soldiers defending their homeland.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='In this struggle as President <PERSON><PERSON><PERSON><PERSON> said in his speech to the European Parliament “Light will win over darkness.” The Ukrainian Ambassador to the United States is here tonight.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Let each of us here tonight in this Chamber send an unmistakable signal to Ukraine and to the world.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Please rise if you are able and show that, Yes, we the United States of America stand with the Ukrainian people.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Throughout our history we’ve learned this lesson when dictators do not pay a price for their aggression they cause more chaos.   \\n\\nThey keep moving.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='And the costs and the threats to America and the world keep rising.   \\n\\nThat’s why the NATO Alliance was created to secure peace and stability in Europe after World War 2.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='The United States is a member along with 29 other nations. \\n\\nIt matters. American diplomacy matters. American resolve matters. \\n\\nPutin’s latest attack on Ukraine was premeditated and unprovoked.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='He rejected repeated efforts at diplomacy. \\n\\nHe thought the West and NATO wouldn’t respond. And he thought he could divide us at home. <PERSON> was wrong. We were ready.  Here is what we did.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='We prepared extensively and carefully. \\n\\nWe spent months building a coalition of other freedom-loving nations from Europe and the Americas to Asia and Africa to confront <PERSON>.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='I spent countless hours unifying our European allies. We shared with the world in advance what we knew <PERSON> was planning and precisely how he would try to falsely justify his aggression.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='We countered Russia’s lies with truth.   \\n\\nAnd now that he has acted the free world is holding him accountable.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Along with twenty-seven members of the European Union including France, Germany, Italy, as well as countries like the United Kingdom, Canada, Japan, Korea, Australia, New Zealand, and many others, even Switzerland.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='We are inflicting pain on Russia and supporting the people of Ukraine. <PERSON> is now isolated from the world more than ever.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Together with our allies –we are right now enforcing powerful economic sanctions. \\n\\nWe are cutting off Russia’s largest banks from the international financial system.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Preventing Russia’s central bank from defending the Russian Ruble making <PERSON>’s $630 Billion “war fund” worthless.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='We are choking off Russia’s access to technology that will sap its economic strength and weaken its military for years to come.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Tonight I say to the Russian oligarchs and corrupt leaders who have bilked billions of dollars off this violent regime no more.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='The U.S. Department of Justice is assembling a dedicated task force to go after the crimes of Russian oligarchs.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='We are joining with our European allies to find and seize your yachts your luxury apartments your private jets. We are coming for your ill-begotten gains.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='And tonight I am announcing that we will join our allies in closing off American air space to all Russian flights – further isolating Russia – and adding an additional squeeze –on their economy. The Ruble has lost 30% of its value.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='The Russian stock market has lost 40% of its value and trading remains suspended. Russia’s economy is reeling and Putin alone is to blame.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Together with our allies we are providing support to the Ukrainians in their fight for freedom. Military assistance. Economic assistance. Humanitarian assistance.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='We are giving more than $1 Billion in direct assistance to Ukraine. \\n\\nAnd we will continue to aid the Ukrainian people as they defend their country and to help ease their suffering.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Let me be clear, our forces are not engaged and will not engage in conflict with Russian forces in Ukraine.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Our forces are not going to Europe to fight in Ukraine, but to defend our NATO Allies – in the event that <PERSON> decides to keep moving west.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='For that purpose we’ve mobilized American ground forces, air squadrons, and ship deployments to protect NATO countries including Poland, Romania, Latvia, Lithuania, and Estonia.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='As I have made crystal clear the United States and our Allies will defend every inch of territory of NATO countries with the full force of our collective power.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='And we remain clear-eyed. The Ukrainians are fighting back with pure courage. But the next few days weeks, months, will be hard on them.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='<PERSON> has unleashed violence and chaos.  But while he may make gains on the battlefield – he will pay a continuing high price over the long run.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='And a proud Ukrainian people, who have known 30 years  of independence, have repeatedly shown that they will not tolerate anyone who tries to take their country backwards.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='To all Americans, I will be honest with you, as I’ve always promised. A Russian dictator, invading a foreign country, has costs around the world.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='And I’m taking robust action to make sure the pain of our sanctions  is targeted at Russia’s economy. And I will use every tool at our disposal to protect American businesses and consumers.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Tonight, I can announce that the United States has worked with 30 other countries to release 60 Million barrels of oil from reserves around the world.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='America will lead that effort, releasing 30 Million barrels from our own Strategic Petroleum Reserve. And we stand ready to do more if necessary, unified with our allies.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='These steps will help blunt gas prices here at home. And I know the news about what’s happening can seem alarming. \\n\\nBut I want you to know that we are going to be okay.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='When the history of this era is written <PERSON>’s war on Ukraine will have left Russia weaker and the rest of the world stronger.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='While it shouldn’t have taken something so terrible for people around the world to see what’s at stake now everyone sees it clearly.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='We see the unity among leaders of nations and a more unified Europe a more unified West. And we see unity among the people who are gathering in cities in large crowds around the world even in Russia to demonstrate their support for Ukraine.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='In the battle between democracy and autocracy, democracies are rising to the moment, and the world is clearly choosing the side of peace and security.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='This is a real test. It’s going to take time. So let us continue to draw inspiration from the iron will of the Ukrainian people.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='To our fellow Ukrainian Americans who forge a deep bond that connects our two nations we stand with you.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='<PERSON> may circle Kyiv with tanks, but he will never gain the hearts and souls of the Ukrainian people.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='He will never extinguish their love of freedom. He will never weaken the resolve of the free world.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='We meet tonight in an America that has lived through two of the hardest years this nation has ever faced. \\n\\nThe pandemic has been punishing.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='And so many families are living paycheck to paycheck, struggling to keep up with the rising cost of food, gas, housing, and so much more. \\n\\nI understand.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='I remember when my Dad had to leave our home in Scranton, Pennsylvania to find work. I grew up in a family where if the price of food went up, you felt it.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='That’s why one of the first things I did as President was fight to pass the American Rescue Plan.  \\n\\nBecause people were hurting. We needed to act, and we did.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Few pieces of legislation have done more in a critical moment in our history to lift us out of crisis.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='It fueled our efforts to vaccinate the nation and combat COVID-19. It delivered immediate economic relief for tens of millions of Americans.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Helped put food on their table, keep a roof over their heads, and cut the cost of health insurance. \\n\\nAnd as my Dad used to say, it gave people a little breathing room.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='And unlike the $2 Trillion tax cut passed in the previous administration that benefitted the top 1% of Americans, the American Rescue Plan helped working people—and left no one behind.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='And it worked. It created jobs. Lots of jobs. \\n\\nIn fact—our economy created over 6.5 Million new jobs just last year, more jobs created in one year  \\nthan ever before in the history of America.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Our economy grew at a rate of 5.7% last year, the strongest growth in nearly 40 years, the first step in bringing fundamental change to an economy that hasn’t worked for the working people of this nation for too long.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='For the past 40 years we were told that if we gave tax breaks to those at the very top, the benefits would trickle down to everyone else.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='But that trickle-down theory led to weaker economic growth, lower wages, bigger deficits, and the widest gap between those at the top and everyone else in nearly a century.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Vice President <PERSON> and I ran for office with a new economic vision for America.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Invest in America. Educate Americans. Grow the workforce. Build the economy from the bottom up  \\nand the middle out, not from the top down.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Because we know that when the middle class grows, the poor have a ladder up and the wealthy do very well. \\n\\nAmerica used to have the best roads, bridges, and airports on Earth.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Now our infrastructure is ranked 13th in the world. \\n\\nWe won’t be able to compete for the jobs of the 21st Century if we don’t fix that.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='That’s why it was so important to pass the Bipartisan Infrastructure Law—the most sweeping investment to rebuild America in history.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='This was a bipartisan effort, and I want to thank the members of both parties who worked to make it happen. \\n\\nWe’re done talking about infrastructure weeks.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='We’re going to have an infrastructure decade.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='It is going to transform America and put us on a path to win the economic competition of the 21st Century that we face with the rest of the world—particularly with China.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='As I’ve told <PERSON>, it is never a good bet to bet against the American people.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='We’ll create good jobs for millions of Americans, modernizing roads, airports, ports, and waterways all across America.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='And we’ll do it all to withstand the devastating effects of the climate crisis and promote environmental justice.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='We’ll build a national network of 500,000 electric vehicle charging stations, begin to replace poisonous lead pipes—so every child—and every American—has clean water to drink at home and at school, provide affordable high-speed internet for every American—urban, suburban, rural, and tribal communities.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='4,000 projects have already been announced. \\n\\nAnd tonight, I’m announcing that this year we will start fixing over 65,000 miles of highway and 1,500 bridges in disrepair.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='When we use taxpayer dollars to rebuild America – we are going to Buy American: buy American products to support American jobs.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='The federal government spends about $600 Billion a year to keep the country safe and secure.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='There’s been a law on the books for almost a century \\nto make sure taxpayers’ dollars support American jobs and businesses. \\n\\nEvery Administration says they’ll do it, but we are actually doing it.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='We will buy American to make sure everything from the deck of an aircraft carrier to the steel on highway guardrails are made in America.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='But to compete for the best jobs of the future, we also need to level the playing field with China and other competitors.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='That’s why it is so important to pass the Bipartisan Innovation Act sitting in Congress that will make record investments in emerging technologies and American manufacturing.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Let me give you one example of why it’s so important to pass it. \\n\\nIf you travel 20 miles east of Columbus, Ohio, you’ll find 1,000 empty acres of land.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='It won’t look like much, but if you stop and look closely, you’ll see a “Field of dreams,” the ground on which America’s future will be built.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='This is where Intel, the American company that helped build Silicon Valley, is going to build its $20 billion semiconductor “mega site”.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Up to eight state-of-the-art factories in one place. 10,000 new good-paying jobs.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Some of the most sophisticated manufacturing in the world to make computer chips the size of a fingertip that power the world and our everyday lives.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Smartphones. The Internet. Technology we have yet to invent. \\n\\nBut that’s just the beginning.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Intel’s CEO, <PERSON>, who is here tonight, told me they are ready to increase their investment from  \\n$20 billion to $100 billion.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='That would be one of the biggest investments in manufacturing in American history. \\n\\nAnd all they’re waiting for is for you to pass this bill.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='So let’s not wait any longer. Send it to my desk. I’ll sign it.  \\n\\nAnd we will really take off. \\n\\nAnd Intel is not alone. \\n\\nThere’s something happening in America.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Just look around and you’ll see an amazing story. \\n\\nThe rebirth of the pride that comes from stamping products “Made In America.” The revitalization of American manufacturing.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Companies are choosing to build new factories here, when just a few years ago, they would have built them overseas.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='That’s what is happening. Ford is investing $11 billion to build electric vehicles, creating 11,000 jobs across the country.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='GM is making the largest investment in its history—$7 billion to build electric vehicles, creating 4,000 jobs in Michigan.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='All told, we created 369,000 new manufacturing jobs in America just last year.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Powered by people I’ve met like <PERSON><PERSON><PERSON>, from generations of union steelworkers from Pittsburgh, who’s here with us tonight.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='As Ohio Senator <PERSON><PERSON><PERSON> says, “It’s time to bury the label “Rust Belt.” \\n\\nIt’s time.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='But with all the bright spots in our economy, record job growth and higher wages, too many families are struggling to keep up with the bills.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Inflation is robbing them of the gains they might otherwise feel. \\n\\nI get it. That’s why my top priority is getting prices under control.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Look, our economy roared back faster than most predicted, but the pandemic meant that businesses had a hard time hiring enough workers to keep up production in their factories.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='The pandemic also disrupted global supply chains. \\n\\nWhen factories close, it takes longer to make goods and get them from the warehouse to the store, and prices go up. \\n\\nLook at cars.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Last year, there weren’t enough semiconductors to make all the cars that people wanted to buy. \\n\\nAnd guess what, prices of automobiles went up. \\n\\nSo—we have a choice.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='One way to fight inflation is to drive down wages and make Americans poorer.  \\n\\nI have a better plan to fight inflation. \\n\\nLower your costs, not your wages.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Make more cars and semiconductors in America. \\n\\nMore infrastructure and innovation in America. \\n\\nMore goods moving faster and cheaper in America.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='More jobs where you can earn a good living in America. \\n\\nAnd instead of relying on foreign supply chains, let’s make it in America.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Economists call it “increasing the productive capacity of our economy.” \\n\\nI call it building a better America. \\n\\nMy plan to fight inflation will lower your costs and lower the deficit.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='17 Nobel laureates in economics say my plan will ease long-term inflationary pressures. Top business leaders and most Americans support my plan. And here’s the plan:', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='First – cut the cost of prescription drugs. Just look at insulin. One in ten Americans has diabetes. In Virginia, I met a 13-year-old boy named <PERSON>.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='He and his Dad both have Type 1 diabetes, which means they need insulin every day. Insulin costs about $10 a vial to make.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='But drug companies charge families like <PERSON> and his Dad up to 30 times more. I spoke with <PERSON>’s mom.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Imagine what it’s like to look at your child who needs insulin and have no idea how you’re going to pay for it.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='What it does to your dignity, your ability to look your child in the eye, to be the parent you expect to be. \\n\\n<PERSON><PERSON><PERSON> is here with us tonight. Yesterday was his birthday. Happy birthday, buddy.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='For <PERSON>, and for the 200,000 other young people with Type 1 diabetes, let’s cap the cost of insulin at $35 a month so everyone can afford it.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Drug companies will still do very well. And while we’re at it let Medicare negotiate lower prices for prescription drugs, like the VA already does.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Look, the American Rescue Plan is helping millions of families on Affordable Care Act plans save $2,400 a year on their health care premiums. Let’s close the coverage gap and make those savings permanent.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Second – cut energy costs for families an average of $500 a year by combatting climate change.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Let’s provide investments and tax credits to weatherize your homes and businesses to be energy efficient and you get a tax credit; double America’s clean energy production in solar, wind, and so much more;  lower the price of electric vehicles, saving you another $80 a month because you’ll never have to pay at the gas pump again.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Third – cut the cost of child care. Many families pay up to $14,000 a year for child care per child.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Middle-class and working families shouldn’t have to pay more than 7% of their income for care of young children.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='My plan will cut the cost in half for most families and help parents, including millions of women, who left the workforce during the pandemic because they couldn’t afford child care, to be able to get back to work.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='My plan doesn’t stop there. It also includes home and long-term care. More affordable housing. And Pre-K for every 3- and 4-year-old.  \\n\\nAll of these will lower costs.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='And under my plan, nobody earning less than $400,000 a year will pay an additional penny in new taxes. Nobody.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='The one thing all Americans agree on is that the tax system is not fair. We have to fix it.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='I’m not looking to punish anyone. But let’s make sure corporations and the wealthiest Americans start paying their fair share.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Just last year, 55 Fortune 500 corporations earned $40 billion in profits and paid zero dollars in federal income tax.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='That’s simply not fair. That’s why I’ve proposed a 15% minimum tax rate for corporations.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='We got more than 130 countries to agree on a global minimum tax rate so companies can’t get out of paying their taxes at home by shipping jobs and factories overseas.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='That’s why I’ve proposed closing loopholes so the very wealthy don’t pay a lower tax rate than a teacher or a firefighter.  \\n\\nSo that’s my plan. It will grow the economy and lower costs for families.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='So what are we waiting for? Let’s get this done. And while you’re at it, confirm my nominees to the Federal Reserve, which plays a critical role in fighting inflation.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='My plan will not only lower costs to give families a fair shot, it will lower the deficit.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='The previous Administration not only ballooned the deficit with tax cuts for the very wealthy and corporations, it undermined the watchdogs whose job was to keep pandemic relief funds from being wasted.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='But in my administration, the watchdogs have been welcomed back. \\n\\nWe’re going after the criminals who stole billions in relief money meant for small businesses and millions of Americans.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='And tonight, I’m announcing that the Justice Department will name a chief prosecutor for pandemic fraud.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='By the end of this year, the deficit will be down to less than half what it was before I took office.  \\n\\nThe only president ever to cut the deficit by more than one trillion dollars in a single year.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Lowering your costs also means demanding more competition. \\n\\nI’m a capitalist, but capitalism without competition isn’t capitalism. \\n\\nIt’s exploitation—and it drives up prices.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='When corporations don’t have to compete, their profits go up, your prices go up, and small businesses and family farmers and ranchers go under.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='We see it happening with ocean carriers moving goods in and out of America. \\n\\nDuring the pandemic, these foreign-owned companies raised prices by as much as 1,000% and made record profits.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Tonight, I’m announcing a crackdown on these companies overcharging American businesses and consumers.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='And as Wall Street firms take over more nursing homes, quality in those homes has gone down and costs have gone up.  \\n\\nThat ends on my watch.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Medicare is going to set higher standards for nursing homes and make sure your loved ones get the care they deserve and expect.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='We’ll also cut costs and keep the economy going strong by giving workers a fair shot, provide more training and apprenticeships, hire them based on their skills not degrees.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Let’s pass the Paycheck Fairness Act and paid leave.  \\n\\nRaise the minimum wage to $15 an hour and extend the Child Tax Credit, so no one has to raise a family in poverty.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Let’s increase Pell Grants and increase our historic support of HBCUs, and invest in what <PERSON>—our First Lady who teaches full-time—calls America’s best-kept secret: community colleges.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='And let’s pass the PRO Act when a majority of workers want to form a union—they shouldn’t be stopped.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='When we invest in our workers, when we build the economy from the bottom up and the middle out together, we can do something we haven’t done in a long time: build a better America.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='For more than two years, COVID-19 has impacted every decision in our lives and the life of the nation. \\n\\nAnd I know you’re tired, frustrated, and exhausted. \\n\\nBut I also know this.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Because of the progress we’ve made, because of your resilience and the tools we have, tonight I can say  \\nwe are moving forward safely, back to more normal routines.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='We’ve reached a new moment in the fight against COVID-19, with severe cases down to a level not seen since last July.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Just a few days ago, the Centers for Disease Control and Prevention—the CDC—issued new mask guidelines. \\n\\nUnder these new guidelines, most Americans in most of the country can now be mask free.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='And based on the projections, more of the country will reach that point across the next couple of weeks.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Thanks to the progress we have made this past year, COVID-19 need no longer control our lives.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='I know some are talking about “living with COVID-19”. Tonight – I say that we will never just accept living with COVID-19.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='We will continue to combat the virus as we do other diseases. And because this is a virus that mutates and spreads, we will stay on guard.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Here are four common sense steps as we move forward safely.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='First, stay protected with vaccines and treatments. We know how incredibly effective vaccines are. If you’re vaccinated and boosted you have the highest degree of protection.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='We will never give up on vaccinating more Americans. Now, I know parents with kids under 5 are eager to see a vaccine authorized for their children.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='The scientists are working hard to get that done and we’ll be ready with plenty of vaccines when they do.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='We’re also ready with anti-viral treatments. If you get COVID-19, the Pfizer pill reduces your chances of ending up in the hospital by 90%.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='We’ve ordered more of these pills than anyone in the world. And <PERSON><PERSON>zer is working overtime to get us 1 Million pills this month and more than double that next month.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='And we’re launching the “Test to Treat” initiative so people can get tested at a pharmacy, and if they’re positive, receive antiviral pills on the spot at no cost.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='If you’re immunocompromised or have some other vulnerability, we have treatments and free high-quality masks. \\n\\nWe’re leaving no one behind or ignoring anyone’s needs as we move forward.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='And on testing, we have made hundreds of millions of tests available for you to order for free.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Even if you already ordered free tests tonight, I am announcing that you can order more from covidtests.gov starting next week.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Second – we must prepare for new variants. Over the past year, we’ve gotten much better at detecting new variants.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='If necessary, we’ll be able to deploy new vaccines within 100 days instead of many more months or years.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='And, if Congress provides the funds we need, we’ll have new stockpiles of tests, masks, and pills ready if needed.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='I cannot promise a new variant won’t come. But I can promise you we’ll do everything within our power to be ready if it does.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Third – we can end the shutdown of schools and businesses. We have the tools we need.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='It’s time for Americans to get back to work and fill our great downtowns again.  People working from home can feel safe to begin to return to the office.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='We’re doing that here in the federal government. The vast majority of federal workers will once again work in person. \\n\\nOur schools are open. Let’s keep it that way. Our kids need to be in school.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='And with 75% of adult Americans fully vaccinated and hospitalizations down by 77%, most Americans can remove their masks, return to work, stay in the classroom, and move forward safely.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='We achieved this because we provided free vaccines, treatments, tests, and masks. \\n\\nOf course, continuing this costs money. \\n\\nI will soon send Congress a request.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='The vast majority of Americans have used these tools and may want to again, so I expect Congress to pass it quickly.   \\n\\nFourth, we will continue vaccinating the world.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='We’ve sent 475 Million vaccine doses to 112 countries, more than any other nation. \\n\\nAnd we won’t stop.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='We have lost so much to COVID-19. Time with one another. And worst of all, so much loss of life.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Let’s use this moment to reset. Let’s stop looking at COVID-19 as a partisan dividing line and see it for what it is: A God-awful disease.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Let’s stop seeing each other as enemies, and start seeing each other for who we really are: Fellow Americans.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='We can’t change how divided we’ve been. But we can change how we move forward—on COVID-19 and other issues we must face together.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='I recently visited the New York City Police Department days after the funerals of Officer <PERSON><PERSON><PERSON> and his partner, Officer <PERSON>.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='They were responding to a 9-1-1 call when a man shot and killed them with a stolen gun. \\n\\nOfficer <PERSON> was 27 years old. \\n\\nOfficer <PERSON> was 22.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Both Dominican Americans who’d grown up on the same streets they later chose to patrol as police officers.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='I spoke with their families and told them that we are forever in debt for their sacrifice, and we will carry on their mission to restore the trust and safety every community deserves.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='I’ve worked on these issues a long time.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='I know what works: Investing in crime preventionand community police officers who’ll walk the beat, who’ll know the neighborhood, and who can restore trust and safety.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='So let’s not abandon our streets. Or choose between safety and equal justice. \\n\\nLet’s come together to protect our communities, restore trust, and hold law enforcement accountable.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='That’s why the Justice Department required body cameras, banned chokeholds, and restricted no-knock warrants for its officers.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='That’s why the American Rescue Plan provided $350 Billion that cities, states, and counties can use to hire more police and invest in proven strategies like community violence interruption—trusted messengers breaking the cycle of violence and trauma and giving young people hope.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='We should all agree: The answer is not to Defund the police. The answer is to FUND the police with the resources and training they need to protect our communities.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='I ask Democrats and Republicans alike: Pass my budget and keep our neighborhoods safe.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='And I will keep doing everything in my power to crack down on gun trafficking and ghost guns you can buy online and make at home—they have no serial numbers and can’t be traced.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='And I ask Congress to pass proven measures to reduce gun violence. Pass universal background checks. Why should anyone on a terrorist list be able to purchase a weapon?', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Ban assault weapons and high-capacity magazines. \\n\\nRepeal the liability shield that makes gun manufacturers the only industry in America that can’t be sued.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='These laws don’t infringe on the Second Amendment. They save lives. \\n\\nThe most fundamental right in America is the right to vote – and to have it counted. And it’s under assault.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='In state after state, new laws have been passed, not only to suppress the vote, but to subvert entire elections. \\n\\nWe cannot let this happen.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Tonight. I call on the Senate to: Pass the Freedom to Vote Act. Pass the John Lewis Voting Rights Act. And while you’re at it, pass the Disclose Act so Americans can know who is funding our elections.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Tonight, I’d like to honor someone who has dedicated his life to serve this country: Justice <PERSON>—an Army veteran, Constitutional scholar, and retiring Justice of the United States Supreme Court. Justice <PERSON>, thank you for your service.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='One of the most serious constitutional responsibilities a President has is nominating someone to serve on the United States Supreme Court.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='And I did that 4 days ago, when I nominated Circuit Court of Appeals Judge <PERSON><PERSON><PERSON>. One of our nation’s top legal minds, who will continue <PERSON>’s legacy of excellence.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='A former top litigator in private practice. A former federal public defender. And from a family of public school educators and police officers. A consensus builder. Since she’s been nominated, she’s received a broad range of support—from the Fraternal Order of Police to former judges appointed by Democrats and Republicans.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='And if we are to advance liberty and justice, we need to secure the Border and fix the immigration system.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='We can do both. At our border, we’ve installed new technology like cutting-edge scanners to better detect drug smuggling.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='We’ve set up joint patrols with Mexico and Guatemala to catch more human traffickers.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='We’re putting in place dedicated immigration judges so families fleeing persecution and violence can have their cases heard faster.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='We’re securing commitments and supporting partners in South and Central America to host more refugees and secure their own borders.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='We can do all this while keeping lit the torch of liberty that has led generations of immigrants to this land—my forefathers and so many of yours.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Provide a pathway to citizenship for Dreamers, those on temporary status, farm workers, and essential workers.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Revise our laws so businesses have the workers they need and families don’t wait decades to reunite. \\n\\nIt’s not only the right thing to do—it’s the economically smart thing to do.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='That’s why immigration reform is supported by everyone from labor unions to religious leaders to the U.S. Chamber of Commerce. \\n\\nLet’s get it done once and for all.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Advancing liberty and justice also requires protecting the rights of women. \\n\\nThe constitutional right affirmed in <PERSON> v<PERSON>—standing precedent for half a century—is under attack as never before.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='If we want to go forward—not backward—we must protect access to health care. Preserve a woman’s right to choose. And let’s continue to advance maternal health care in America.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='And for our LGBTQ+ Americans, let’s finally get the bipartisan Equality Act to my desk. The onslaught of state laws targeting transgender Americans and their families is wrong.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='As I said last year, especially to our younger transgender Americans, I will always have your back as your President, so you can be yourself and reach your God-given potential.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='While it often appears that we never agree, that isn’t true. I signed 80 bipartisan bills into law last year. From preventing government shutdowns to protecting Asian-Americans from still-too-common hate crimes to reforming military justice.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='And soon, we’ll strengthen the Violence Against Women Act that I first wrote three decades ago. It is important for us to show the nation that we can come together and do big things.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='So tonight I’m offering a Unity Agenda for the Nation. Four big things we can do together.  \\n\\nFirst, beat the opioid epidemic.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='There is so much we can do. Increase funding for prevention, treatment, harm reduction, and recovery.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Get rid of outdated rules that stop doctors from prescribing treatments. And stop the flow of illicit drugs by working with state and local law enforcement to go after traffickers.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='If you’re suffering from addiction, know you are not alone. I believe in recovery, and I celebrate the 23 million Americans in recovery.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Second, let’s take on mental health. Especially among our children, whose lives and education have been turned upside down.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='The American Rescue Plan gave schools money to hire teachers and help students make up for lost learning.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='I urge every parent to make sure your school does just that. And we can all play a part—sign up to be a tutor or a mentor.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Children were also struggling before the pandemic. Bullying, violence, trauma, and the harms of social media.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='As <PERSON>, who is here with us tonight, has shown, we must hold social media platforms accountable for the national experiment they’re conducting on our children for profit.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='It’s time to strengthen privacy protections, ban targeted advertising to children, demand tech companies stop collecting personal data on our children.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='And let’s get all Americans the mental health services they need. More people they can turn to for help, and full parity between physical and mental health care. \\n\\nThird, support our veterans.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Veterans are the best of us. \\n\\nI’ve always believed that we have a sacred obligation to equip all those we send to war and care for them and their families when they come home.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='My administration is providing assistance with job training and housing, and now helping lower-income veterans get VA care debt-free.  \\n\\nOur troops in Iraq and Afghanistan faced many dangers.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='One was stationed at bases and breathing in toxic smoke from “burn pits” that incinerated wastes of war—medical and hazard material, jet fuel, and more.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='When they came home, many of the world’s fittest and best trained warriors were never the same. \\n\\nHeadaches. Numbness. Dizziness. \\n\\nA cancer that would put them in a flag-draped coffin. \\n\\nI know.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='One of those soldiers was my son Major <PERSON>. \\n\\nWe don’t know for sure if a burn pit was the cause of his brain cancer, or the diseases of so many of our troops.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='But I’m committed to finding out everything we can. \\n\\nCommitted to military families like <PERSON> from Ohio. \\n\\nThe widow of Sergeant First Class <PERSON>.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='He was born a soldier. Army National Guard. Combat medic in Kosovo and Iraq. \\n\\nStationed near Baghdad, just yards from burn pits the size of football fields.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='<PERSON>’s widow <PERSON> is here with us tonight. They loved going to Ohio State football games. He loved building Legos with their daughter.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='But cancer from prolonged exposure to burn pits ravaged <PERSON>’s lungs and body. \\n\\nD<PERSON><PERSON> says <PERSON> was a fighter to the very end. \\n\\nHe didn’t know how to stop fighting, and neither did she.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Through her pain she found purpose to demand we do better. \\n\\nTonight, <PERSON>—we are.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='The VA is pioneering new ways of linking toxic exposures to diseases, already helping more veterans get benefits.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='And tonight, I’m announcing we’re expanding eligibility to veterans suffering from nine respiratory cancers.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='I’m also calling on Congress: pass a law to make sure veterans devastated by toxic exposures in Iraq and Afghanistan finally get the benefits and comprehensive health care they deserve.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='And fourth, let’s end cancer as we know it. \\n\\nThis is personal to me and <PERSON>, to <PERSON><PERSON>, and to so many of you. \\n\\nCancer is the #2 cause of death in America–second only to heart disease.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Last month, I announced our plan to supercharge  \\nthe Cancer Moonshot that President <PERSON> asked me to lead six years ago.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Our goal is to cut the cancer death rate by at least 50% over the next 25 years, turn more cancers from death sentences into treatable diseases.  \\n\\nMore support for patients and families.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='To get there, I call on Congress to fund ARPA-H, the Advanced Research Projects Agency for Health.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='It’s based on DARPA—the Defense Department project that led to the Internet, GPS, and so much more.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='ARPA-H will have a singular purpose—to drive breakthroughs in cancer, Alzheimer’s, diabetes, and more. \\n\\nA unity agenda for the nation. \\n\\nWe can do this.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='My fellow Americans—tonight , we have gathered in a sacred space—the citadel of our democracy.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='In this Capitol, generation after generation, Americans have debated great questions amid great strife, and have done great things.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='We have fought for freedom, expanded liberty, defeated totalitarianism and terror. \\n\\nAnd built the strongest, freest, and most prosperous nation the world has ever known. \\n\\nNow is the hour.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Our moment of responsibility. \\n\\nOur test of resolve and conscience, of history itself. \\n\\nIt is in this moment that our character is formed. Our purpose is found. Our future is forged.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Well I know this nation.  \\n\\nWe will meet the test. \\n\\nTo protect freedom and liberty, to expand fairness and opportunity. \\n\\nWe will save democracy.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='As hard as these times have been, I am more optimistic about America today than I have been my whole life. \\n\\nBecause I see the future that is within our grasp.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='Because I know there is simply nothing beyond our capacity. \\n\\nWe are the only nation on Earth that has always turned every crisis we have faced into an opportunity.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='The only nation that can be defined by a single word: possibilities. \\n\\nSo on this night, in our 245th year as a nation, I have come to report on the State of the Union.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='And my report is this: the State of the Union is strong—because you, the American people, are strong. \\n\\nWe are stronger today than we were a year ago.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='And we will be stronger a year from now than we are today. \\n\\nNow is our moment to meet and overcome the challenges of our time. \\n\\nAnd we will, as one people. \\n\\nOne America.', metadata={'source': '../tests/state_of_the_union.txt'}),\n", " Document(page_content='The United States of America. \\n\\nMay God bless you all. May God protect our troops.', metadata={'source': '../tests/state_of_the_union.txt'})]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["documents"]}, {"cell_type": "code", "execution_count": 6, "id": "79e3c50a-efad-49bb-84fc-f0fb2585b34e", "metadata": {}, "outputs": [], "source": ["embeddings_model = OpenAIEmbeddings()"]}, {"cell_type": "markdown", "id": "d5d9331a-cd90-45b5-a54c-87c0e20f793b", "metadata": {}, "source": ["### 注意：Pandas 相关包首次导入错误后，再次执行即可正确导入"]}, {"cell_type": "code", "execution_count": 8, "id": "da4682d7-0886-46e3-b99d-fc39adf741e5", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["No embedding_function provided, using default embedding function: DefaultEmbeddingFunction https://huggingface.co/sentence-transformers/all-MiniLM-L6-v2\n"]}], "source": ["# 将分割后的文本，使用 OpenAI 嵌入模型获取嵌入向量，并存储在 Chroma 中\n", "db = Chroma.from_documents(documents, embeddings_model)"]}, {"cell_type": "markdown", "id": "da2b840c-4ad8-41b2-b750-988b3dfad8c1", "metadata": {}, "source": ["#### 使用文本进行语义相似度搜索"]}, {"cell_type": "code", "execution_count": 9, "id": "9e85d4c4-ee77-4470-85ec-ce37f359fdc7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["And I did that 4 days ago, when I nominated Circuit Court of Appeals Judge <PERSON><PERSON><PERSON>. One of our nation’s top legal minds, who will continue <PERSON>’s legacy of excellence.\n"]}], "source": ["query = \"What did the president say about <PERSON><PERSON><PERSON>\"\n", "docs = db.similarity_search(query)\n", "print(docs[0].page_content)"]}, {"cell_type": "markdown", "id": "32e3ddc2-9d69-4b72-ace1-800ba94def79", "metadata": {}, "source": ["### 使用嵌入向量进行语义相似度搜索"]}, {"cell_type": "code", "execution_count": 10, "id": "0069d18d-1fb6-40e2-974c-2f49559b8b9a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["And I did that 4 days ago, when I nominated Circuit Court of Appeals Judge <PERSON><PERSON><PERSON>. One of our nation’s top legal minds, who will continue <PERSON>’s legacy of excellence.\n"]}], "source": ["embedding_vector = embeddings_model.embed_query(query)\n", "docs = db.similarity_search_by_vector(embedding_vector)\n", "print(docs[0].page_content)"]}, {"cell_type": "code", "execution_count": null, "id": "25339b8d-ee23-460c-ae4e-97a8f24f6add", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}}, "nbformat": 4, "nbformat_minor": 5}