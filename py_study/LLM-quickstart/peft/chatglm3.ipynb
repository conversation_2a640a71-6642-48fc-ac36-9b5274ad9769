{"cells": [{"cell_type": "code", "execution_count": 1, "id": "323b27c1-0380-4f0e-baca-247a4ad130a4", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[33mDEPRECATION: Loading egg at /root/miniconda3/lib/python3.11/site-packages/huggingface_hub-0.19.4-py3.8.egg is deprecated. pip 24.3 will enforce this behaviour change. A possible replacement is to use pip for package installation.. Discussion can be found at https://github.com/pypa/pip/issues/12330\u001b[0m\u001b[33m\n", "\u001b[0mRequirement already satisfied: protobuf in /root/miniconda3/lib/python3.11/site-packages (4.25.1)\n", "Requirement already satisfied: transformers==4.30.2 in /root/miniconda3/lib/python3.11/site-packages (4.30.2)\n", "Requirement already satisfied: cpm_kernels in /root/miniconda3/lib/python3.11/site-packages (1.0.11)\n", "Requirement already satisfied: gradio in /root/miniconda3/lib/python3.11/site-packages (4.9.0)\n", "Requirement already satisfied: mdtex2html in /root/miniconda3/lib/python3.11/site-packages (1.2.0)\n", "Requirement already satisfied: sentencepiece in /root/miniconda3/lib/python3.11/site-packages (0.1.99)\n", "Requirement already satisfied: accelerate in /root/miniconda3/lib/python3.11/site-packages (0.25.0)\n", "Requirement already satisfied: filelock in /root/miniconda3/lib/python3.11/site-packages (from transformers==4.30.2) (3.13.1)\n", "Requirement already satisfied: huggingface-hub<1.0,>=0.14.1 in /root/miniconda3/lib/python3.11/site-packages/huggingface_hub-0.19.4-py3.8.egg (from transformers==4.30.2) (0.19.4)\n", "Requirement already satisfied: numpy>=1.17 in /root/miniconda3/lib/python3.11/site-packages (from transformers==4.30.2) (1.26.2)\n", "Requirement already satisfied: packaging>=20.0 in /root/miniconda3/lib/python3.11/site-packages (from transformers==4.30.2) (23.1)\n", "Requirement already satisfied: pyyaml>=5.1 in /root/miniconda3/lib/python3.11/site-packages (from transformers==4.30.2) (6.0.1)\n", "Requirement already satisfied: regex!=2019.12.17 in /root/miniconda3/lib/python3.11/site-packages (from transformers==4.30.2) (2023.10.3)\n", "Requirement already satisfied: requests in /root/miniconda3/lib/python3.11/site-packages (from transformers==4.30.2) (2.31.0)\n", "Requirement already satisfied: tokenizers!=0.11.3,<0.14,>=0.11.1 in /root/miniconda3/lib/python3.11/site-packages (from transformers==4.30.2) (0.13.3)\n", "Requirement already satisfied: safetensors>=0.3.1 in /root/miniconda3/lib/python3.11/site-packages (from transformers==4.30.2) (0.4.0)\n", "Requirement already satisfied: tqdm>=4.27 in /root/miniconda3/lib/python3.11/site-packages (from transformers==4.30.2) (4.65.0)\n", "Requirement already satisfied: aiofiles<24.0,>=22.0 in /root/miniconda3/lib/python3.11/site-packages (from gradio) (23.2.1)\n", "Requirement already satisfied: altair<6.0,>=4.2.0 in /root/miniconda3/lib/python3.11/site-packages (from gradio) (5.2.0)\n", "Requirement already satisfied: fastapi in /root/miniconda3/lib/python3.11/site-packages (from gradio) (0.105.0)\n", "Requirement already satisfied: ffmpy in /root/miniconda3/lib/python3.11/site-packages (from gradio) (0.3.1)\n", "Requirement already satisfied: gradio-client==0.7.2 in /root/miniconda3/lib/python3.11/site-packages (from gradio) (0.7.2)\n", "Requirement already satisfied: httpx in /root/miniconda3/lib/python3.11/site-packages (from gradio) (0.25.2)\n", "Requirement already satisfied: importlib-resources<7.0,>=1.3 in /root/miniconda3/lib/python3.11/site-packages (from gradio) (6.1.1)\n", "Requirement already satisfied: jinja2<4.0 in /root/miniconda3/lib/python3.11/site-packages (from gradio) (3.1.2)\n", "Requirement already satisfied: markupsafe~=2.0 in /root/miniconda3/lib/python3.11/site-packages (from gradio) (2.1.3)\n", "Requirement already satisfied: matplotlib~=3.0 in /root/miniconda3/lib/python3.11/site-packages (from gradio) (3.8.2)\n", "Requirement already satisfied: orjson~=3.0 in /root/miniconda3/lib/python3.11/site-packages (from gradio) (3.9.10)\n", "Requirement already satisfied: pandas<3.0,>=1.0 in /root/miniconda3/lib/python3.11/site-packages (from gradio) (2.1.1)\n", "Requirement already satisfied: pillow<11.0,>=8.0 in /root/miniconda3/lib/python3.11/site-packages (from gradio) (10.1.0)\n", "Requirement already satisfied: pydantic>=2.0 in /root/miniconda3/lib/python3.11/site-packages (from gradio) (2.5.2)\n", "Requirement already satisfied: pydub in /root/miniconda3/lib/python3.11/site-packages (from gradio) (0.25.1)\n", "Requirement already satisfied: python-multipart in /root/miniconda3/lib/python3.11/site-packages (from gradio) (0.0.6)\n", "Requirement already satisfied: semantic-version~=2.0 in /root/miniconda3/lib/python3.11/site-packages (from gradio) (2.10.0)\n", "Requirement already satisfied: tomlkit==0.12.0 in /root/miniconda3/lib/python3.11/site-packages (from gradio) (0.12.0)\n", "Requirement already satisfied: typer<1.0,>=0.9 in /root/miniconda3/lib/python3.11/site-packages (from typer[all]<1.0,>=0.9->gradio) (0.9.0)\n", "Requirement already satisfied: typing-extensions~=4.0 in /root/miniconda3/lib/python3.11/site-packages (from gradio) (4.9.0)\n", "Requirement already satisfied: uvicorn>=0.14.0 in /root/miniconda3/lib/python3.11/site-packages (from gradio) (0.24.0.post1)\n", "Requirement already satisfied: fsspec in /root/miniconda3/lib/python3.11/site-packages (from gradio-client==0.7.2->gradio) (2023.10.0)\n", "Requirement already satisfied: websockets<12.0,>=10.0 in /root/miniconda3/lib/python3.11/site-packages (from gradio-client==0.7.2->gradio) (11.0.3)\n", "Requirement already satisfied: markdown in /root/miniconda3/lib/python3.11/site-packages (from mdtex2html) (3.5.1)\n", "Requirement already satisfied: latex2mathml in /root/miniconda3/lib/python3.11/site-packages (from mdtex2html) (3.77.0)\n", "Requirement already satisfied: psutil in /root/miniconda3/lib/python3.11/site-packages (from accelerate) (5.9.5)\n", "Requirement already satisfied: torch>=1.10.0 in /root/miniconda3/lib/python3.11/site-packages (from accelerate) (2.1.1)\n", "Requirement already satisfied: jsonschema>=3.0 in /root/miniconda3/lib/python3.11/site-packages (from altair<6.0,>=4.2.0->gradio) (4.20.0)\n", "Requirement already satisfied: toolz in /root/miniconda3/lib/python3.11/site-packages (from altair<6.0,>=4.2.0->gradio) (0.12.0)\n", "Requirement already satisfied: contourpy>=1.0.1 in /root/miniconda3/lib/python3.11/site-packages (from matplotlib~=3.0->gradio) (1.2.0)\n", "Requirement already satisfied: cycler>=0.10 in /root/miniconda3/lib/python3.11/site-packages (from matplotlib~=3.0->gradio) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /root/miniconda3/lib/python3.11/site-packages (from matplotlib~=3.0->gradio) (4.46.0)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /root/miniconda3/lib/python3.11/site-packages (from matplotlib~=3.0->gradio) (1.4.5)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /root/miniconda3/lib/python3.11/site-packages (from matplotlib~=3.0->gradio) (3.1.1)\n", "Requirement already satisfied: python-dateutil>=2.7 in /root/miniconda3/lib/python3.11/site-packages (from matplotlib~=3.0->gradio) (2.8.2)\n", "Requirement already satisfied: pytz>=2020.1 in /root/miniconda3/lib/python3.11/site-packages (from pandas<3.0,>=1.0->gradio) (2023.3.post1)\n", "Requirement already satisfied: tzdata>=2022.1 in /root/miniconda3/lib/python3.11/site-packages (from pandas<3.0,>=1.0->gradio) (2023.3)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /root/miniconda3/lib/python3.11/site-packages (from pydantic>=2.0->gradio) (0.6.0)\n", "Requirement already satisfied: pydantic-core==2.14.5 in /root/miniconda3/lib/python3.11/site-packages (from pydantic>=2.0->gradio) (2.14.5)\n", "Requirement already satisfied: sympy in /root/miniconda3/lib/python3.11/site-packages (from torch>=1.10.0->accelerate) (1.12)\n", "Requirement already satisfied: networkx in /root/miniconda3/lib/python3.11/site-packages (from torch>=1.10.0->accelerate) (3.2.1)\n", "Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.1.105 in /root/miniconda3/lib/python3.11/site-packages (from torch>=1.10.0->accelerate) (12.1.105)\n", "Requirement already satisfied: nvidia-cuda-runtime-cu12==12.1.105 in /root/miniconda3/lib/python3.11/site-packages (from torch>=1.10.0->accelerate) (12.1.105)\n", "Requirement already satisfied: nvidia-cuda-cupti-cu12==12.1.105 in /root/miniconda3/lib/python3.11/site-packages (from torch>=1.10.0->accelerate) (12.1.105)\n", "Requirement already satisfied: nvidia-cudnn-cu12==8.9.2.26 in /root/miniconda3/lib/python3.11/site-packages (from torch>=1.10.0->accelerate) (8.9.2.26)\n", "Requirement already satisfied: nvidia-cublas-cu12==12.1.3.1 in /root/miniconda3/lib/python3.11/site-packages (from torch>=1.10.0->accelerate) (12.1.3.1)\n", "Requirement already satisfied: nvidia-cufft-cu12==11.0.2.54 in /root/miniconda3/lib/python3.11/site-packages (from torch>=1.10.0->accelerate) (11.0.2.54)\n", "Requirement already satisfied: nvidia-curand-cu12==10.3.2.106 in /root/miniconda3/lib/python3.11/site-packages (from torch>=1.10.0->accelerate) (10.3.2.106)\n", "Requirement already satisfied: nvidia-cusolver-cu12==11.4.5.107 in /root/miniconda3/lib/python3.11/site-packages (from torch>=1.10.0->accelerate) (11.4.5.107)\n", "Requirement already satisfied: nvidia-cusparse-cu12==12.1.0.106 in /root/miniconda3/lib/python3.11/site-packages (from torch>=1.10.0->accelerate) (12.1.0.106)\n", "Requirement already satisfied: nvidia-nccl-cu12==2.18.1 in /root/miniconda3/lib/python3.11/site-packages (from torch>=1.10.0->accelerate) (2.18.1)\n", "Requirement already satisfied: nvidia-nvtx-cu12==12.1.105 in /root/miniconda3/lib/python3.11/site-packages (from torch>=1.10.0->accelerate) (12.1.105)\n", "Requirement already satisfied: triton==2.1.0 in /root/miniconda3/lib/python3.11/site-packages (from torch>=1.10.0->accelerate) (2.1.0)\n", "Requirement already satisfied: nvidia-nvjitlink-cu12 in /root/miniconda3/lib/python3.11/site-packages (from nvidia-cusolver-cu12==11.4.5.107->torch>=1.10.0->accelerate) (12.3.101)\n", "Requirement already satisfied: click<9.0.0,>=7.1.1 in /root/miniconda3/lib/python3.11/site-packages (from typer<1.0,>=0.9->typer[all]<1.0,>=0.9->gradio) (8.1.7)\n", "Requirement already satisfied: colorama<0.5.0,>=0.4.3 in /root/miniconda3/lib/python3.11/site-packages (from typer[all]<1.0,>=0.9->gradio) (0.4.6)\n", "Requirement already satisfied: shellingham<2.0.0,>=1.3.0 in /root/miniconda3/lib/python3.11/site-packages (from typer[all]<1.0,>=0.9->gradio) (1.5.4)\n", "Requirement already satisfied: rich<14.0.0,>=10.11.0 in /root/miniconda3/lib/python3.11/site-packages (from typer[all]<1.0,>=0.9->gradio) (13.7.0)\n", "Requirement already satisfied: h11>=0.8 in /root/miniconda3/lib/python3.11/site-packages (from uvicorn>=0.14.0->gradio) (0.14.0)\n", "Requirement already satisfied: anyio<4.0.0,>=3.7.1 in /root/miniconda3/lib/python3.11/site-packages (from fastapi->gradio) (3.7.1)\n", "Requirement already satisfied: starlette<0.28.0,>=0.27.0 in /root/miniconda3/lib/python3.11/site-packages (from fastapi->gradio) (0.27.0)\n", "Requirement already satisfied: certifi in /root/miniconda3/lib/python3.11/site-packages (from httpx->gradio) (2023.11.17)\n", "Requirement already satisfied: httpcore==1.* in /root/miniconda3/lib/python3.11/site-packages (from httpx->gradio) (1.0.2)\n", "Requirement already satisfied: idna in /root/miniconda3/lib/python3.11/site-packages (from httpx->gradio) (3.4)\n", "Requirement already satisfied: sniffio in /root/miniconda3/lib/python3.11/site-packages (from httpx->gradio) (1.3.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /root/miniconda3/lib/python3.11/site-packages (from requests->transformers==4.30.2) (2.0.4)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /root/miniconda3/lib/python3.11/site-packages (from requests->transformers==4.30.2) (1.26.18)\n", "Requirement already satisfied: attrs>=22.2.0 in /root/miniconda3/lib/python3.11/site-packages (from jsonschema>=3.0->altair<6.0,>=4.2.0->gradio) (23.1.0)\n", "Requirement already satisfied: jsonschema-specifications>=2023.03.6 in /root/miniconda3/lib/python3.11/site-packages (from jsonschema>=3.0->altair<6.0,>=4.2.0->gradio) (2023.11.2)\n", "Requirement already satisfied: referencing>=0.28.4 in /root/miniconda3/lib/python3.11/site-packages (from jsonschema>=3.0->altair<6.0,>=4.2.0->gradio) (0.32.0)\n", "Requirement already satisfied: rpds-py>=0.7.1 in /root/miniconda3/lib/python3.11/site-packages (from jsonschema>=3.0->altair<6.0,>=4.2.0->gradio) (0.13.2)\n", "Requirement already satisfied: six>=1.5 in /root/miniconda3/lib/python3.11/site-packages (from python-dateutil>=2.7->matplotlib~=3.0->gradio) (1.16.0)\n", "Requirement already satisfied: markdown-it-py>=2.2.0 in /root/miniconda3/lib/python3.11/site-packages (from rich<14.0.0,>=10.11.0->typer[all]<1.0,>=0.9->gradio) (3.0.0)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /root/miniconda3/lib/python3.11/site-packages (from rich<14.0.0,>=10.11.0->typer[all]<1.0,>=0.9->gradio) (2.17.2)\n", "Requirement already satisfied: mpmath>=0.19 in /root/miniconda3/lib/python3.11/site-packages (from sympy->torch>=1.10.0->accelerate) (1.3.0)\n", "Requirement already satisfied: mdurl~=0.1 in /root/miniconda3/lib/python3.11/site-packages (from markdown-it-py>=2.2.0->rich<14.0.0,>=10.11.0->typer[all]<1.0,>=0.9->gradio) (0.1.2)\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv\u001b[0m\u001b[33m\n", "\u001b[0m\u001b[33mDEPRECATION: Loading egg at /root/miniconda3/lib/python3.11/site-packages/huggingface_hub-0.19.4-py3.8.egg is deprecated. pip 24.3 will enforce this behaviour change. A possible replacement is to use pip for package installation.. Discussion can be found at https://github.com/pypa/pip/issues/12330\u001b[0m\u001b[33m\n", "\u001b[0mRequirement already satisfied: torch in /root/miniconda3/lib/python3.11/site-packages (2.1.1)\n", "Collecting torch\n", "  Downloading torch-2.1.2-cp311-cp311-manylinux1_x86_64.whl.metadata (25 kB)\n", "Requirement already satisfied: filelock in /root/miniconda3/lib/python3.11/site-packages (from torch) (3.13.1)\n", "Requirement already satisfied: typing-extensions in /root/miniconda3/lib/python3.11/site-packages (from torch) (4.9.0)\n", "Requirement already satisfied: sympy in /root/miniconda3/lib/python3.11/site-packages (from torch) (1.12)\n", "Requirement already satisfied: networkx in /root/miniconda3/lib/python3.11/site-packages (from torch) (3.2.1)\n", "Requirement already satisfied: jinja2 in /root/miniconda3/lib/python3.11/site-packages (from torch) (3.1.2)\n", "Requirement already satisfied: fsspec in /root/miniconda3/lib/python3.11/site-packages (from torch) (2023.10.0)\n", "Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.1.105 in /root/miniconda3/lib/python3.11/site-packages (from torch) (12.1.105)\n", "Requirement already satisfied: nvidia-cuda-runtime-cu12==12.1.105 in /root/miniconda3/lib/python3.11/site-packages (from torch) (12.1.105)\n", "Requirement already satisfied: nvidia-cuda-cupti-cu12==12.1.105 in /root/miniconda3/lib/python3.11/site-packages (from torch) (12.1.105)\n", "Requirement already satisfied: nvidia-cudnn-cu12==8.9.2.26 in /root/miniconda3/lib/python3.11/site-packages (from torch) (8.9.2.26)\n", "Requirement already satisfied: nvidia-cublas-cu12==12.1.3.1 in /root/miniconda3/lib/python3.11/site-packages (from torch) (12.1.3.1)\n", "Requirement already satisfied: nvidia-cufft-cu12==11.0.2.54 in /root/miniconda3/lib/python3.11/site-packages (from torch) (11.0.2.54)\n", "Requirement already satisfied: nvidia-curand-cu12==10.3.2.106 in /root/miniconda3/lib/python3.11/site-packages (from torch) (10.3.2.106)\n", "Requirement already satisfied: nvidia-cusolver-cu12==11.4.5.107 in /root/miniconda3/lib/python3.11/site-packages (from torch) (11.4.5.107)\n", "Requirement already satisfied: nvidia-cusparse-cu12==12.1.0.106 in /root/miniconda3/lib/python3.11/site-packages (from torch) (12.1.0.106)\n", "Requirement already satisfied: nvidia-nccl-cu12==2.18.1 in /root/miniconda3/lib/python3.11/site-packages (from torch) (2.18.1)\n", "Requirement already satisfied: nvidia-nvtx-cu12==12.1.105 in /root/miniconda3/lib/python3.11/site-packages (from torch) (12.1.105)\n", "Requirement already satisfied: triton==2.1.0 in /root/miniconda3/lib/python3.11/site-packages (from torch) (2.1.0)\n", "Requirement already satisfied: nvidia-nvjitlink-cu12 in /root/miniconda3/lib/python3.11/site-packages (from nvidia-cusolver-cu12==11.4.5.107->torch) (12.3.101)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /root/miniconda3/lib/python3.11/site-packages (from jinja2->torch) (2.1.3)\n", "Requirement already satisfied: mpmath>=0.19 in /root/miniconda3/lib/python3.11/site-packages (from sympy->torch) (1.3.0)\n", "Downloading torch-2.1.2-cp311-cp311-manylinux1_x86_64.whl (670.2 MB)\n", "\u001b[2K   \u001b[38;2;114;156;31m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m670.2/670.2 MB\u001b[0m \u001b[31m1.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m0m eta \u001b[36m0:00:01\u001b[0m[36m0:00:02\u001b[0m��━━━━━━━━━━━━━━━\u001b[0m\u001b[38;2;249;38;114m╸\u001b[0m\u001b[38;5;237m━━━━━━━━━━━━\u001b[0m \u001b[32m461.5/670.2 MB\u001b[0m \u001b[31m267.4 MB/s\u001b[0m eta \u001b[36m0:00:01\u001b[0m2;249;38;114m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[38;2;249;38;114m╸\u001b[0m\u001b[38;5;237m━━━━━━━━━━\u001b[0m \u001b[32m498.9/670.2 MB\u001b[0m \u001b[31m52.8 MB/s\u001b[0m eta \u001b[36m0:00:04\u001b[0m�━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[38;2;249;38;114m╸\u001b[0m\u001b[38;5;237m━━━━\u001b[0m \u001b[32m596.2/670.2 MB\u001b[0m \u001b[31m13.8 MB/s\u001b[0m eta \u001b[36m0:00:06\u001b[0m\n", "\u001b[?25hI<PERSON><PERSON>ing collected packages: torch\n", "  Attempting uninstall: torch\n", "    Found existing installation: torch 2.1.1\n", "    Uninstalling torch-2.1.1:\n", "      Successfully uninstalled torch-2.1.1\n", "\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "torchvision 0.16.1 requires torch==2.1.1, but you have torch 2.1.2 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed torch-2.1.2\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv\u001b[0m\u001b[33m\n", "\u001b[0m"]}], "source": ["!pip install protobuf transformers==4.30.2 cpm_kernels gradio mdtex2html sentencepiece accelerate\n", "!pip install -U torch"]}, {"cell_type": "code", "execution_count": 1, "id": "90dcc2cd-8960-4982-8565-5c0f82dd7889", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f407f657346244679342e12ee9d25963", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading checkpoint shards:   0%|          | 0/7 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from transformers import AutoTokenizer, AutoModel\n", "tokenizer = AutoTokenizer.from_pretrained(\"THUDM/chatglm3-6b\", trust_remote_code=True)\n", "model = AutoModel.from_pretrained(\"THUDM/chatglm3-6b\", trust_remote_code=True).half().cuda()"]}, {"cell_type": "code", "execution_count": null, "id": "3b00a441-c27e-4d55-923b-02ce6deca3bd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 2, "id": "8471cf60-7ddb-46e2-9701-93b8db6e63e7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["你好👋！我是人工智能助手 ChatGLM3-6B，很高兴见到你，欢迎问我任何问题。\n", "晚上睡不着觉可能是由多种原因引起的，例如压力、焦虑、饮食、睡眠环境等。这里有一些建议，可以帮助你更容易入睡：\n", "\n", "1. 保持规律的作息时间：尽量每天在相同的时间上床睡觉和起床，这有助于调整你的生物钟。\n", "\n", "2. 创造舒适的睡眠环境：确保你的卧室温度适中、光线柔和、噪音较低，并使用舒适的床上用品。\n", "\n", "3. 避免咖啡因和酒精：咖啡因和酒精都可能影响睡眠质量，因此晚上避免摄入这些物质。\n", "\n", "4. 注意饮食：尽量避免在睡前摄入过多的食物和饮水，以免导致夜间频繁上厕所影响睡眠。同时，可以考虑摄入一些有助于睡眠的食物，如燕麦、香蕉、蜂蜜等。\n", "\n", "5. 进行放松活动：在睡前进行一些放松活动，如深呼吸、瑜伽、阅读等，可以帮助你放松身心，更容易入睡。\n", "\n", "6. 避免使用电子产品：睡前避免使用手机、电脑等电子产品，这些产品的蓝光可能会干扰你的睡眠。\n", "\n", "7. 进行适当的白天活动：白天进行适当的运动，有助于提高睡眠质量。但避免在临近睡觉的时间内进行剧烈运动。\n", "\n", "8. 尝试冥想和放松技巧：如上所述，冥想和放松技巧可以帮助你更容易入睡。可以尝试在睡前进行这些活动，看看是否能帮助你入睡。\n", "\n", "如果以上方法都无法解决问题，建议寻求专业医生的帮助，以排除任何潜在的健康问题。\n"]}], "source": ["model = model.eval()\n", "response, history = model.chat(tokenizer, \"你好\", history=[])\n", "print(response)\n", "response, history = model.chat(tokenizer, \"晚上睡不着应该怎么办\", history=history)\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "id": "e7189aa0-972b-4ca7-8e93-7cf1664b72d1", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}