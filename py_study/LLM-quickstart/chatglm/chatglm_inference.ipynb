{"cells": [{"cell_type": "markdown", "id": "fc5bde60-1899-461d-8083-3ee04ac7c099", "metadata": {}, "source": ["# 模型推理 - 使用 QLoRA 微调后的 ChatGLM-6B"]}, {"cell_type": "code", "execution_count": 1, "id": "3292b88c-91f0-48d2-91a5-06b0830c7e70", "metadata": {}, "outputs": [], "source": ["import torch\n", "from transformers import AutoModel, AutoTokenizer, BitsAndBytesConfig\n", "\n", "# 模型ID或本地路径\n", "model_name_or_path = 'THUDM/chatglm3-6b'"]}, {"cell_type": "code", "execution_count": 2, "id": "9f81454c-24b2-4072-ab05-b25f9b120ae6", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "25763bf7afc544329363a3c84be34bb9", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading checkpoint shards:   0%|          | 0/7 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["_compute_dtype_map = {\n", "    'fp32': torch.float32,\n", "    'fp16': torch.float16,\n", "    'bf16': torch.bfloat16\n", "}\n", "\n", "# QLoRA 量化配置\n", "q_config = BitsAndBytesConfig(load_in_4bit=True,\n", "                              bnb_4bit_quant_type='nf4',\n", "                              bnb_4bit_use_double_quant=True,\n", "                              bnb_4bit_compute_dtype=_compute_dtype_map['bf16'])\n", "\n", "# 加载量化后模型(与微调的 revision 保持一致）\n", "base_model = AutoModel.from_pretrained(model_name_or_path,\n", "                                      quantization_config=q_config,\n", "                                      device_map='auto',\n", "                                      trust_remote_code=True,\n", "                                      revision='b098244')"]}, {"cell_type": "code", "execution_count": 3, "id": "d488846f-41bb-4fe6-9f09-0f392f3b39e6", "metadata": {}, "outputs": [{"data": {"text/plain": ["ChatGLMForConditionalGeneration(\n", "  (transformer): ChatGLMModel(\n", "    (embedding): Embedding(\n", "      (word_embeddings): Embedding(65024, 4096)\n", "    )\n", "    (rotary_pos_emb): RotaryEmbedding()\n", "    (encoder): GLMTransformer(\n", "      (layers): ModuleList(\n", "        (0-27): 28 x GLMBlock(\n", "          (input_layernorm): RMSNorm()\n", "          (self_attention): SelfAttention(\n", "            (query_key_value): Linear4bit(in_features=4096, out_features=4608, bias=True)\n", "            (core_attention): CoreAttention(\n", "              (attention_dropout): Dropout(p=0.0, inplace=False)\n", "            )\n", "            (dense): Linear4bit(in_features=4096, out_features=4096, bias=False)\n", "          )\n", "          (post_attention_layernorm): RMSNorm()\n", "          (mlp): MLP(\n", "            (dense_h_to_4h): Linear4bit(in_features=4096, out_features=27392, bias=False)\n", "            (dense_4h_to_h): Linear4bit(in_features=13696, out_features=4096, bias=False)\n", "          )\n", "        )\n", "      )\n", "      (final_layernorm): RMSNorm()\n", "    )\n", "    (output_layer): Linear(in_features=4096, out_features=65024, bias=False)\n", "  )\n", ")"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["base_model.requires_grad_(False)\n", "base_model.eval()"]}, {"cell_type": "code", "execution_count": 4, "id": "7e4270e2-c827-450e-bf27-7cb43a97f8f7", "metadata": {}, "outputs": [], "source": ["tokenizer = AutoTokenizer.from_pretrained(model_name_or_path,\n", "                                          trust_remote_code=True,\n", "                                          revision='b098244')"]}, {"cell_type": "markdown", "id": "63408b60-876e-4eda-b501-90f842cca002", "metadata": {}, "source": ["## 使用原始 ChatGLM3-6B 模型"]}, {"cell_type": "code", "execution_count": 5, "id": "6ef405cf-7d77-41a6-a07b-c6c768ee30cf", "metadata": {}, "outputs": [], "source": ["input_text = \"解释下乾卦是什么？\""]}, {"cell_type": "code", "execution_count": 6, "id": "566ed80e-828b-4105-b6e6-49de8905c991", "metadata": {}, "outputs": [], "source": ["response, history = base_model.chat(tokenizer, query=input_text)"]}, {"cell_type": "code", "execution_count": 7, "id": "6cee217e-f276-4c2f-94e7-69afb6d541a6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["乾卦是八卦之一，也是八宫图之一，它的卦象是由三个阳爻夹一个阴爻构成，象征着天、强、积极、刚毅等含义。乾卦的卦辞为“元、亨、利、永、唯天、初、亨”，意味着“天命”、“行健”、“健行”、“强健”等。乾卦主要反映的是天、强、积极、刚毅等特性，与事业、权力、努力、坚定等有关。在占卜中，乾卦通常被视为吉卦，预示着成功、发展、力量和财富等。\n"]}], "source": ["print(response)"]}, {"cell_type": "markdown", "id": "3db3245d-037d-4fe5-ac0d-cc5e82742399", "metadata": {}, "source": ["#### 询问一个64卦相关问题（应该不在 ChatGLM3-6B 预训练数据中）"]}, {"cell_type": "code", "execution_count": 8, "id": "bbe1395f-39c2-4759-ae81-90ef3bcfae47", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["讼卦是八卦之一，它的卦象是由两个阳爻夹一个阴爻构成，象征着诉讼、争端、矛盾和困扰等。讼卦的卦辞为“天 perspectives”、“天听”、“有罚”、“无射”，意味着“天 perspective”、“天命”、“有所罚”、“没有射箭的”等。\n", "\n", "讼卦主要反映的是矛盾、争端和困扰等，与法律、诉讼、争议、争端等有关。在占卜中，讼卦通常被视为凶卦，预示着诉讼、争端和困扰等可能会加剧，建议先和解、调解，避免进一步恶化。\n"]}], "source": ["response, history = base_model.chat(tokenizer, query=\"周易中的讼卦是什么？\", history=history)\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "id": "342b3659-d644-4232-8af1-f092e733bf40", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "6d23e720-dee1-4b43-a298-0cbe1d8ad11d", "metadata": {}, "source": ["## 使用微调后的 ChatGLM3-6B"]}, {"cell_type": "markdown", "id": "6bcfc5a2-41ed-405c-a31c-dca4fbb67425", "metadata": {}, "source": ["### 加载 QLoRA Adapter(Epoch=3, automade-dataset(fixed)) - 请根据训练时间戳修改 timestamp "]}, {"cell_type": "code", "execution_count": 9, "id": "9c767c67-42aa-459c-a096-e226226c359b", "metadata": {}, "outputs": [], "source": ["from peft import PeftModel, PeftConfig\n", "\n", "epochs = 3\n", "# timestamp = \"20240118_164514\"\n", "timestamp = \"20240225_222843\"\n", "\n", "peft_model_path = f\"models/{model_name_or_path}-epoch{epochs}-{timestamp}\"\n", "\n", "config = PeftConfig.from_pretrained(peft_model_path)\n", "qlora_model = PeftModel.from_pretrained(base_model, peft_model_path)\n", "training_tag=f\"ChatGLM3-6B(Epoch=3, automade-dataset(fixed))-{timestamp}\""]}, {"cell_type": "code", "execution_count": 10, "id": "24a5d22b-2c94-4dcf-8135-18d78f98755f", "metadata": {}, "outputs": [], "source": ["def compare_chatglm_results(query, base_model, qlora_model, training_tag):\n", "    base_response, base_history = base_model.chat(tokenizer, query)\n", "\n", "    inputs = tokenizer(query, return_tensors=\"pt\").to(0)\n", "    ft_out = qlora_model.generate(**inputs, max_new_tokens=512)\n", "    ft_response = tokenizer.decode(ft_out[0], skip_special_tokens=True)\n", "    \n", "    print(f\"问题：{query}\\n\\n原始输出：\\n{base_response}\\n\\n\\n微调后（{training_tag}）：\\n{ft_response}\")\n", "    return base_response, ft_response"]}, {"cell_type": "markdown", "id": "062cd62e-69f9-4605-8c83-e468f71ef3d3", "metadata": {}, "source": ["### 微调前后效果对比"]}, {"cell_type": "code", "execution_count": 11, "id": "7db16cd5-0bb5-44ab-b861-d9ca6a4970c2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["问题：解释下乾卦是什么？\n", "\n", "原始输出：\n", "{'name': '乾卦是八宫图中的第一宫，也是八卦中的第一卦，由乾卦纯阳所组成，代表着天。它象征着刚健、刚健不阿，希望排除困难，敢作敢为，坚定不拔，具有刚健不阿的特质。乾卦的核心哲学是：天行健，君子拟之。意思是以健健的品德行走在世界上，排除障碍，勇敢前进。', 'content': '\\n乾卦象征天，代表着刚健不阿，刚健不阿，希望排除困难，敢作敢为，坚定不拔，具有刚健不阿的特质。在事业方面，乾卦代表着刚健不阿的品德，能够在事业中克服困难，取得胜利。在经商方面，乾卦象征着刚健不阿，坚韧不拔，具有刚健不阿的特质。在求名方面，乾卦代表着刚健不阿，希望排除困难，敢作敢为，坚定不拔，具有刚健不阿的特质。\\n\\n总体来说，乾卦是一个具有刚健不阿的品性的卦象，象征着不阿不叛、坚贞不屈的精神，具有坚贞不阿、刚健不阿的特质。在不同的环境中，乾卦所代表的意义也有所不同，但总体上是代表着刚健不阿的品性。'}\n", "\n", "\n", "微调后（ChatGLM3-6B(Epoch=3, automade-dataset(fixed))-20240225_222843）：\n", "[gMASK]sop 解释下乾卦是什么？ 在周易中，乾卦是六十四卦之首，由六个阳爻组成，象征着天。它所代表的是刚健、健行、刚健不屈的意境。乾卦的核心哲学是：天道刚健，运行不已，君子观此卦象，从而以天为法，自强不息。\n", "\n", "乾卦象征天，为大通而至正。得此卦者，名利双收，应把握机会，争取成果。然而，切勿过于骄傲自满，而应保持谦逊、冷静和警惕。在事业、经商、求名等方面，乾卦皆暗示着大吉大利，但也警示着必须坚持正道、修养德行，方能永远亨通。\n", "\n", "在婚恋方面，乾卦提示着阳盛阴衰，但也强调刚柔相济，相互补足，形成美满的结果。在决策方面，则是强调刚健、正直、公允，自强不息的实质，需要修养德行、坚定信念，方能克服困难，消除灾难。\n"]}], "source": ["base_response, ft_response = compare_chatglm_results(\"解释下乾卦是什么？\", base_model, qlora_model, training_tag)"]}, {"cell_type": "code", "execution_count": 12, "id": "7aa074bd-c819-4533-a10f-f3184dc9549a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["问题：周易中的讼卦是什么\n", "\n", "原始输出：\n", "{'name': 'ich möchte mich im Liede der linken Hand ergeben, um Ihnen mitzuteilen, was die Obrigkeiten der linken Hand bedeuten. Im Kiese der linken Hand findet sich das Maß für reine Rede, das den Ding und denen, denen es gilt, wissen und verstehen lassen kann. Immerwhere also immer, wenn es muss geteilt worden muss, das Maß findet die Gelegenheit, gebrachte Verantwortlichkeiten zu bergen und generell zu helfen.', 'content': '\\nDas Ergebnis der linken Hand sollte also berüftreffen, was die Gegenströme des Gutes und der Rechtspflichten ist. Solche Gelegenheiten können zu Beginn und Mitte des Rechtsverfahrens gearbeitet werden, um das Verbrechen und die Verantwortlichkeiten zu entdecken. Solche Gelegenheiten können die Rede und die Taten des linken Handes erfordern, um die Würgen und die Verantwortlichkeiten des linken Handes zu bergen.\\n\\nUm das Gefühl des linken Handes zu verstehen, muss man sich für die reine Rede engagiert und das Gefühl des linken Handes muss miterfahren. Solche Erfahrung kann man erlangen, wenn man sich für andere interesselte Sacheen engagiert, um deren reine Rede und deren Gefühl zu verstehen. Solche Erfahrung kann man erlangen, wenn man sich für andere interesselte Sacheen engagiert und das Gefühl des linken Handes miterfahren lassen muss.\\n\\nDas Ergebnis der linken Hand kann auch mit anderem bedeuten, wenn er sich für andere Dinge interesset und andere Sacheen miterfahren muss, um die reine Rede und das Gefühl des linken Handes zu verstehen. Solche Interpretation kann man u. a. mit demselben章程 (Zhonghong) des Zhoukang, dem classifier des Confucius, das mit demselben Gefühl der linken Hand assoziiert ist, erkennen.'}\n", "\n", "\n", "微调后（ChatGLM3-6B(Epoch=3, automade-dataset(fixed))-20240225_222843）：\n", "[gMASK]sop 周易中的讼卦是什么样子, 代表什么意思? 在周易中，讼卦是一个充满警示的卦象。它由上卦乾（天）和下卦坎（水）组成，代表着天与水背道而驰，形成争讼的局面。虽然事情开始时有利可图，但必须警惕戒惧，因为中间虽然吉利，但最终会带来凶险。对于涉及大川，涉水渡河的行动不利。因此，君子观此卦象，应当慎之又慎，杜绝争讼之事，并在谋事之初谨慎行事。讼卦的核心哲学是要避免争讼，退而让人，求得化解，安于正理，方可避免意外之灾。在事业上，务必避免介入诉讼纠纷的争执之中，与其这样，不如退而让人。即使最终获胜，也难免得失不均。经商方面，要坚持公正、公平、互利的原则，避免冲突，这样会有好结果。而对于求名、婚恋和决策，也都需要慎重行事，避免盲目追求，退让让人，可助事业、婚姻和决策的发展。\n"]}], "source": ["base_response, ft_response = compare_chatglm_results(\"周易中的讼卦是什么\", base_model, qlora_model, training_tag)"]}, {"cell_type": "code", "execution_count": 13, "id": "d5a31554-40f1-4e6e-8240-f207c4a61b42", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["问题：师卦是什么？\n", "\n", "原始输出：\n", "在周易中，师卦是一个由坎卦（水）和坤卦（地）相叠而成的异卦。这一卦象代表着军队的力量和军情的总指挥，预示着吉祥无灾。象辞中描述了地中有水的情景，寓意着君子应当像大地一样容纳和畜养大众。师卦的解释强调选择德高望重的长者来统率军队，才能获得吉祥无咎。另外，师卦也象征着困难重重，需要包容别人、艰苦努力，及时行事，严于律已。在事业、经商、求名、婚恋等方面的决策中，都需要警惕潜在敌人，小心谨慎，合作与决断兼顾，方能成功。\n", "\n", "\n", "微调后（ChatGLM3-6B(Epoch=3, automade-dataset(fixed))-20240225_222843）：\n", "[gMASK]sop 师卦是什么？ 在周易中，师卦是一个由坎卦（水）和坤卦（地）相叠而成的异卦。这一卦象代表着军队的力量和军情的总指挥，预示着吉祥无灾。象辞中描述了地中有水的情景，寓意着君子应当像大地一样容纳和畜养大众。师卦的解释强调选择德高望重的长者来统率军队，才能获得吉祥无咎。另外，师卦也象征着困难重重，需要包容别人、艰苦努力，及时行事，严于律已。在事业、经商、求名、婚恋等方面的决策中，都需要警惕潜在敌人，小心谨慎，合作与决断兼顾，方能成功。\n"]}], "source": ["base_response, ft_response = compare_chatglm_results(\"师卦是什么？\", base_model, qlora_model, training_tag)"]}, {"cell_type": "code", "execution_count": null, "id": "abae8a8e-00bb-4801-931a-c942206f0e2a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "7d48183f-f1dc-4171-b217-e269a5b9c1b9", "metadata": {}, "source": ["## 其他模型（错误数据或训练参数）\n", "\n", "#### 加载 QLoRA Adapter(Epoch=3, automade-dataset)"]}, {"cell_type": "code", "execution_count": 14, "id": "46a0e881-a4f3-43b2-8a61-0ec543a538a7", "metadata": {}, "outputs": [], "source": ["from peft import PeftModel, PeftConfig\n", "\n", "epochs = 3\n", "peft_model_path = f\"models/{model_name_or_path}-epoch{epochs}\"\n", "\n", "config = PeftConfig.from_pretrained(peft_model_path)\n", "qlora_model_e3 = PeftModel.from_pretrained(base_model, peft_model_path)\n", "training_tag = f\"ChatGLM3-6B(Epoch=3, automade-dataset)\""]}, {"cell_type": "code", "execution_count": 15, "id": "1f53196e-f523-4105-b04a-9ddab349cce1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["问题：解释下乾卦是什么？\n", "\n", "原始输出：\n", "乾卦是周易中的一卦，代表天，包含着万物，象征刚健强劲的特性。它由两个卦相组合而成，分别是乾卦和坤卦。其中，乾卦代表阳刚之卦，由六个阳爻组成，表示天地的力量和人民的勇气。\n", "\n", "在周易中，阳刚之卦具有刚健强劲的特性，代表着强烈的决断力和行动力。同时，阳刚之卦也象征着收获和繁荣，预示着我们能够克服困难，取得成功。\n", "\n", "在解卦时，乾卦预示着刚健强劲的特性，并带来收获和繁荣。同时，也需要注意警惕困难的降临，并采取措施来化解困难。\n", "\n", "在运势方面，乾卦表示运势顺利，具有刚健强劲的特性，能够克服困难，取得成功。同时，也需要注意保持冷静，避免决策错误。\n", "\n", "总结起来，乾卦是周易中具有强烈象征意义的卦象，代表着刚健强劲的特性，并带来收获和繁荣，预示着我们能够克服困难，取得成功。\n", "\n", "\n", "微调后（ChatGLM3-6B(Epoch=3, automade-dataset)）：\n", "[gMASK]sop 解释下乾卦是什么？ \n", "乾卦是周易中的一卦，代表天，具有刚健强劲的特性。它由两个卦相组合而成，一个是乾卦，另一个是坤卦。乾卦的排列是：元、亨、利、贞，代表天象运行，具有刚健强劲的特性。\n", "\n", "在周易中，乾卦预示着天将健行， Cloud Deck（云层）预示着天将有所得，丽（美丽）预示着天将利市，利（吉祥）预示着天将得利。\n", "\n", "乾卦的哲学内涵是：天行健，君子以自强不息。它强调的是刚健强劲的品质，鼓励人们不断自我提高，自我完善。\n", "\n", "在周易中，乾卦代表的是天，具有刚健强劲的特性。它预示着天将健行，云层将有所得，丽将利市，利将得利。\n", "\n", "在周易的哲学中，乾卦预示着天将健行， Cloud Deck（云层）预示着天将有所得，丽（美丽）预示着天将利市，利（吉祥）预示着天将得利。\n", "\n", "乾卦的刚健强劲特性反映了天象运行的特点，具有强劲的运势，但也要注意谨慎行事，避免过度自信。\n"]}], "source": ["base_response, ft_response = compare_chatglm_results(\"解释下乾卦是什么？\", base_model, qlora_model_e3, training_tag)"]}, {"cell_type": "code", "execution_count": 16, "id": "046306ad-6afe-4ec9-ae55-3df04f61d8f5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["问题：地水师卦是什么？\n", "\n", "原始输出：\n", "地水师卦是易经中的一卦，代表的是坤卦，它的卦象是由两个坤卦叠加而成。在卜筮时，这个卦象象征着地中的水（代表阴柔），以及 associated with it 的一些事物（代表阳刚）。地水师卦的卦象表示了地的承载能力，象征着大地包容万物，具有强大的包容力。\n", "\n", "在哲学层面上，地水师卦象征着阳刚生于阴柔之中，预示着事物的发展将有著新的动向。在卜筮时，这种卦象被认为具有吉祥的意义，预示着将会面临新的挑战，但也能带来成功。\n", "\n", "地水师卦的运势特点如下：\n", "\n", "- 事业：面临新的挑战，需要积极应对，寻求新的发展机会。\n", "- 爱情：感情可能受到 external 因素的影响，需要多沟通，理解对方。\n", "- 财运：事业顺利，能够得到财富。\n", "- 健康：身体可能会有状况，需要保持健康的生活方式。\n", "\n", "地水师卦的经商运势：\n", "\n", "- 事业：需要积极拓展业务，提高自身竞争力，寻求新的合作伙伴。\n", "- 投资：不宜于进行高风险投资，需要谨慎对待。\n", "- 经商：适合以稳健为主，不宜冒险，需要仔细考虑。\n", "\n", "总结起来，地水师卦的总体运势较为顺利，但也需要注意 new challenges and opportunities, and the need for careful consideration and understanding.\n", "\n", "\n", "微调后（ChatGLM3-6B(Epoch=3, automade-dataset)）：\n", "[gMASK]sop 地水师卦是什么？ 地水师卦是一个由坎卦（水）和坤卦（地）组成的卦象，代表地下的水，象征润泽大地的力量。根据《易经》中的解释，地水师卦预示着吉祥如意，具有强大的力量，可以克服各种困难。\n", "\n", "根据传统解卦，地水师卦分为两个卦象，第一个卦象是坎卦（水），代表灵活变化的力量，具有吉祥如意，靠智慧取悦于人；第二个卦象是坤卦（地），代表刚健柔顺，具有强大的力量，可以克服各种困难。\n", "\n", "地水师卦的核心理念是：吉祥如意，靠智慧取悦于人，靠刚健柔顺的力量，可以克服各种困难。\n"]}], "source": ["base_response, ft_response = compare_chatglm_results(\"地水师卦是什么？\", base_model, qlora_model_e3, training_tag)"]}, {"cell_type": "code", "execution_count": 17, "id": "2ab3c310-8cc8-428a-91fa-964b7a58df43", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["问题：周易中的讼卦是什么\n", "\n", "原始输出：\n", "讼卦是周易中的一卦，代表诉讼的意义。卦名“讼”由上卦坎（水）和下卦乾（天）组成，上卦为云，下卦为天。代表险恶的刚直之象。讼卦的核心哲学是：天（乾）刚直，云（坎）险恶，刚柔相济，可以免患失。\n", "\n", "讼卦的核心哲学是：刚直刚硬，不能容悦于人，因此必致刚柔相济。刚可以济柔，刚又能柔，则无失矣。\n", "\n", "讼卦的取裁：\n", "\n", "刚直刚硬，不能容悦于人，因此必致刚直硬板。\n", "\n", "刚可以济柔，刚又能柔，则无失矣。\n", "\n", "刚直硬板，可以容悦于人，则无失矣。\n", "\n", "诉词刚硬，必致刚硬相济。\n", "\n", "刚直硬板，必须得人相助。\n", "\n", "source: 《易经》\n", "\n", "\n", "微调后（ChatGLM3-6B(Epoch=3, automade-dataset)）：\n", "[gMASK]sop 周易中的讼卦是什么卦象\n", "\n", " 讼卦是周易卦象中的一卦，由上卦坎（水）和下卦乾（天）组成，象征着天神下降于坎水之中，具有刚健刚毅之象。讼卦的卦象具有云雾聚集、天光破云的意象，象征着事情将会顺利发展，宜早做准备。\n", "\n", "讼卦的时运是：\n", " 初爻：阳爻（天）刚健刚毅\n", " 二爻：阳爻（天）刚健刚毅\n", " 三爻：阳爻（天）刚健刚毅\n", " 四爻：阳爻（天）刚健刚毅\n", " 初爻：阳爻（天）刚健刚毅\n", " 限运：\n", " 阳爻（天）刚健刚毅\n", " 阴爻（天）刚柔温和\n", " 阳爻（天）刚健刚毅\n", " 阳爻（天）刚健刚毅\n", "\n", "讼卦的解卦是：\n", " 初爻：阳爻（天）刚健刚毅\n", " 二爻：阳爻（天）刚健刚毅\n", " 三爻：阳爻（天）刚健刚毅\n", " 初爻：阳爻（天）刚健刚毅\n", " 限运：\n", " 阳爻（天）刚健刚毅\n", " 阴爻（天）刚柔温和\n", " 阳爻（天）刚健刚毅\n", " 阳爻（天）刚健刚毅\n", "\n", "讼卦的卦象和时运都预示着刚健刚毅的特性，天神下降于坎水之中，具有刚健刚毅之象。在事业和生活中遇到困难和挑战时，需要积极应对，宜早做准备，不宜冒险，切勿冒进。在处理问题和决策时，要冷静分析，不宜草率行事，宜等待时机的到来。同时，要注重刚健刚毅的态度，积极应对各种困难和挑战，从而获得成功。\n"]}], "source": ["base_response, ft_response = compare_chatglm_results(\"周易中的讼卦是什么\", base_model, qlora_model_e3, training_tag)"]}, {"cell_type": "code", "execution_count": null, "id": "9cfffcc5-afa6-45c1-985a-a3eb86a0d1c8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "8169237c-55d3-4d91-9f6b-8dbe635f1844", "metadata": {}, "source": ["#### 加载 QLoRA Adapter(Epoch=50, Overfit, handmade-dataset)"]}, {"cell_type": "code", "execution_count": 18, "id": "72e6cc4f-c030-4107-b07a-6ef44f66a4b0", "metadata": {}, "outputs": [], "source": ["from peft import PeftModel, PeftConfig\n", "\n", "epochs = 50\n", "peft_model_path = f\"models/{model_name_or_path}-epoch{epochs}\"\n", "\n", "config = PeftConfig.from_pretrained(peft_model_path)\n", "qlora_model_e50_handmade = PeftModel.from_pretrained(base_model, peft_model_path)\n", "training_tag = f\"ChatGLM3-6B(Epoch=50, handmade-dataset)\""]}, {"cell_type": "code", "execution_count": 19, "id": "8d63b187-37be-4721-8959-098d0437c41d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["问题：解释下乾卦是什么？\n", "\n", "原始输出：\n", "乾卦是《易经》中的第一卦，卦象由六个阳爻（即阳性的木）排列而成，它们从下至上排列，看起来像是天上的云彩，因此得名“乾”。乾卦象征着刚强、坚毅和独立，以及与天地之间的联系。\n", "\n", "乾卦的卦辞是：“乾为天，为父，为君，亲阳人。亲，先祖，家远的代际，宜先祖；国，先王，宜治国；夫，配偶，宜婚配；父，尊亲，宜尊父；君，朋友，宜友道。”卦辞描述了乾卦的内涵，它既象征着天，也象征着人。作为天，它与“先祖”和“先王”有关，表示宇宙的起点和万物的基础。作为人，它与“父”和“君”有关，表示人类的社会性和文化传承。\n", "\n", "在《易经》中，乾卦是“八卦”之一，它既象征着阳，也象征着“刚”和“强”。乾卦的卦象既象征着“天”，也象征着“人”，它是“八卦”中最为重要的卦之一，表示宇宙的运行和万物的生成。\n", "\n", "\n", "微调后（ChatGLM3-6B(Epoch=50, handmade-dataset)）：\n", "[gMASK]sop 解释下乾卦是什么？乾卦是《易经》中的第一卦，也是六十四卦中的首卦，它的卦象是由两个阳爻夹一个阴爻构成，象征着天，也象征着刚强和坚毅。乾卦的卦辞是“元，亨，利，贞”，意味着“ beginning, success, profit, integrity”。它表示万事万物的本源和原理，是成功的基础。乾卦 also means \"the heaven\", and it is a symbol of the universe, with six lines of yin and one line of yang. It represents the power and strength of the world, and is a symbol of integrity.\n"]}], "source": ["base_response, ft_response = compare_chatglm_results(\"解释下乾卦是什么？\", base_model, qlora_model_e50_handmade, training_tag)"]}, {"cell_type": "code", "execution_count": 20, "id": "be5da80e-d1de-467f-a3bb-508d5a77a46d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["问题：地水师卦\n", "\n", "原始输出：\n", "师卦（师的卦象为乾为天，地为地）主动向被动，主动而被动，地震师，吉。象征师兵，兵者国之大事，故放师卦于和创新，守师卦则不进。\n", "\n", "师卦是由天（乾）和地（坤）组成的卦象，乾为天，坤为地。师卦是《易经》中第八卦，属于坤卦（ earth）系列卦象，坤卦下卦为坤，上卦为乾，乾为天，坤为地。\n", "\n", "师卦象征着师兵，主动向被动，主动而被动，地震师，吉。象征师兵，兵者国之大事，故放师卦于和创新，守师卦则不进。\n", "\n", "师卦最佳状态为师的（师卦上卦为乾，下卦为坤），乾为天，坤为地，师卦象征师兵，主动向被动，主动而被动，地震师，吉。象征师兵，兵者国之大事，故放师卦于和创新，守师卦则不进。\n", "\n", "在占卦中，师卦卦象为吉凶相生，以阳刚为上，以顺为正，symbolism of the earth and sky，象征天地。师卦：师 师 师。三个师字，下坤上乾。\n", "\n", "师卦卦象分析：师卦是乾卦下坤卦上，乾为天，坤为地，象征天和地。师卦象征师兵，主动向被动，主动而被动，地震师，吉。\n", "\n", "师卦卦象占卜：占师卦，得师卦，吉。\n", "\n", "师卦卦象应用：师卦象征师兵，主动向被动，主动而被动，象征天和地。适用于天和地的事物，占师卦 Creature and earth。\n", "\n", "\n", "微调后（ChatGLM3-6B(Epoch=50, handmade-dataset)）：\n", "[gMASK]sop 地水师卦 师卦原文：师。贞，丈人吉，无咎。象曰：地中有水，师。君子以容民畜众。白话文解释：师卦象征军队指挥，无灾祸。《象辞》说：下卦为坎（水），上卦为坤（地），如大地容纳江河，君子应容纳众人。《断易天机》解：师卦坤上坎下，象征军众，需德高长者统率以吉无咎。北宋易学家邵雍解：忧劳动众，公正无私排难。得卦者应包容他人，努力排除困难。台湾国学大儒傅佩荣解：时运包容他人，财运有财需珍惜，家宅旧亲联姻吉，身体腹胀调气。传统解卦：异卦（下坎上坤），“师”指军队。坎为水险，坤为地顺，寓兵于农，用兵应顺势，故化凶为吉。\n"]}], "source": ["base_response, ft_response = compare_chatglm_results(\"地水师卦\", base_model, qlora_model_e50_handmade, training_tag)"]}, {"cell_type": "code", "execution_count": 21, "id": "04f0eb9a-5075-4588-914a-2538bea801aa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["问题：天水讼卦\n", "\n", "原始输出：\n", "讼卦（卦名：讼）是《易经》中的第八卦，属于雷（卦头）坎（卦体）离（卦象）两种卦象的组合。讼卦象征诉讼、争议、争端等诉讼方面的现象。卦形为雷上坎下，雷象征刚毅，坎象征险，离象征烈。卦象下卦为坎，上卦为离，险险相间，刚毅相结合。\n", "\n", "讼卦的卦辞：“讼，吉凶交战，得利藏损。”卦象下坎上离，坎险离光，光争下，险林光，刚毅争利，光损藏吉。\n", "\n", "蔽卦（卦名：蔽）是《易经》中的第六卦，属于离（卦头）坤（卦体）震（卦象）两种卦象的组合。蔽卦象征屏蔽、遮蔽，阻止邪恶势力，弘扬正道。\n", "\n", "蔽卦的卦辞：“蔽，利ulo，吉凶凶险皆屏蔽。”卦象下离上坤，离火遮蔽，坤地顺从，刚毅相合，和顺利害，吉祥安全。\n", "\n", " src=\"http://www.qk6.com/article/html/277776.html\" target=\"_blank\">http://www.qk6.com/article/html/277776.html\n", "\n", "\n", "微调后（ChatGLM3-6B(Epoch=50, handmade-dataset)）：\n", "[gMASK]sop 天水讼卦 讼卦原文：讼。有孚，窒惕，中吉，终凶。利见大人，不利涉大川。象曰：天与水违行，讼。君子以做事谋始。白话文解释：讼卦象征虽有利可图但需警惕。事情初吉后凶，利于见贵人，不宜涉水。《象辞》说：上卦为乾（天），下卦为坎（水），天水相隔，事理不合，君子需慎重谋事。《断易天机》解：讼卦乾上坎下，刚遇险，必有争论，多不吉。北宋易学家邵雍解：天高水深，远离不亲，慎谋退守则无凶。得此卦者，身心不安，多争诉，宜修身养性。台湾国学大儒傅佩荣解：时运受阻，财运初谨慎终获利，家宅君子求淑女，身体预防胜于治疗。传统解卦：异卦（下坎上乾），刚健遇险，彼此反对，生争讼，需慎重戒惧。\n"]}], "source": ["base_response, ft_response = compare_chatglm_results(\"天水讼卦\", base_model, qlora_model_e50_handmade, training_tag)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}