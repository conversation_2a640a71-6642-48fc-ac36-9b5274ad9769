import copy
import math
import torch
import torch.nn as nn
import torch.nn.functional as F

"""
================================================================================
TRANSFORMER 完整架构图
================================================================================

输入序列 → [词嵌入 + 位置编码] → 编码器 → 解码器 → 生成器 → 输出概率分布

详细架构：

┌─────────────────────────────────────────────────────────────────────────────┐
│                                TRANSFORMER                                   │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  输入序列 (src)                                                              │
│        ↓                                                                     │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐         │
│  │   词嵌入层       │    │   位置编码       │    │   编码器         │         │
│  │  Embeddings     │ →  │PositionalEncoding│ →  │   Encoder       │         │
│  │  (vocab→d_model)│    │   (sin/cos)     │    │   (N层)         │         │
│  └─────────────────┘    └─────────────────┘    └─────────────────┘         │
│                                                                             │
│  目标序列 (tgt)                                                              │
│        ↓                                                                     │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐         │
│  │   词嵌入层       │    │   位置编码       │    │   解码器         │         │
│  │  Embeddings     │ →  │PositionalEncoding│ →  │   Decoder       │         │
│  │  (vocab→d_model)│    │   (sin/cos)     │    │   (N层)         │         │
│  └─────────────────┘    └─────────────────┘    └─────────────────┘         │
│                                                                             │
│  编码器输出 (memory) ──────────────────────────────────┐                   │
│                                                        ↓                   │
│                                              ┌─────────────────┐         │
│                                              │   生成器         │         │
│                                              │   Generator      │         │
│                                              │ (d_model→vocab)  │         │
│                                              └─────────────────┘         │
│                                                        ↓                   │
│                                              输出概率分布 (log_softmax)     │
└─────────────────────────────────────────────────────────────────────────────┘

编码器层详细结构 (重复N次)：
┌─────────────────────────────────────────────────────────────────────────────┐
│                              ENCODER LAYER                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  输入 x                                                                      │
│    ↓                                                                        │
│  ┌─────────────────────────────────────────────────────────────────────────┐ │
│  │                   子层1: 多头自注意力机制                                │ │
│  │  SublayerConnection(x, MultiHeadAttention(x, x, x, mask))              │ │
│  │                                                                         │ │
│  │  计算过程:                                                               │ │
│  │  1. LayerNorm(x) → x_norm                                               │ │
│  │  2. MultiHeadAttention(x_norm, x_norm, x_norm, mask) → attn_out        │ │
│  │  3. Dropout(attn_out) → attn_dropout                                    │ │
│  │  4. x + attn_dropout → residual_connection                              │ │
│  └─────────────────────────────────────────────────────────────────────────┘ │
│    ↓                                                                        │
│  ┌─────────────────────────────────────────────────────────────────────────┐ │
│  │                   子层2: 前馈网络                                        │ │
│  │  SublayerConnection(x, PositionwiseFeedForward(x))                     │ │
│  │                                                                         │ │
│  │  计算过程:                                                               │ │
│  │  1. LayerNorm(x) → x_norm                                               │ │
│  │  2. Linear1(x_norm) → ReLU → Dropout → Linear2 → ff_out                │ │
│  │  3. Dropout(ff_out) → ff_dropout                                        │ │
│  │  4. x + ff_dropout → residual_connection                                │ │
│  └─────────────────────────────────────────────────────────────────────────┘ │
│    ↓                                                                        │
│  输出 x                                                                      │
└─────────────────────────────────────────────────────────────────────────────┘

解码器层详细结构 (重复N次)：
┌─────────────────────────────────────────────────────────────────────────────┐
│                              DECODER LAYER                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  输入 x, memory (编码器输出)                                                 │
│    ↓                                                                        │
│  ┌─────────────────────────────────────────────────────────────────────────┐ │
│  │                   子层1: 掩码多头自注意力机制                            │ │
│  │  SublayerConnection(x, MultiHeadAttention(x, x, x, tgt_mask))          │ │
│  │  (tgt_mask防止看到未来信息)                                              │ │
│  └─────────────────────────────────────────────────────────────────────────┘ │
│    ↓                                                                        │
│  ┌─────────────────────────────────────────────────────────────────────────┐ │
│  │                   子层2: 编码器-解码器注意力机制                         │ │
│  │  SublayerConnection(x, MultiHeadAttention(x, memory, memory, src_mask))│ │
│  │  (让解码器关注编码器的输出)                                              │ │
│  └─────────────────────────────────────────────────────────────────────────┘ │
│    ↓                                                                        │
│  ┌─────────────────────────────────────────────────────────────────────────┐ │
│  │                   子层3: 前馈网络                                        │ │
│  │  SublayerConnection(x, PositionwiseFeedForward(x))                     │ │
│  └─────────────────────────────────────────────────────────────────────────┘ │
│    ↓                                                                        │
│  输出 x                                                                      │
└─────────────────────────────────────────────────────────────────────────────┘

================================================================================
关键数学公式
================================================================================

1. 注意力机制公式:
   Attention(Q,K,V) = softmax(QK^T/√d_k)V

2. 多头注意力公式:
   MultiHead(Q,K,V) = Concat(head_1,...,head_h)W^O
   其中 head_i = Attention(QW_i^Q, KW_i^K, VW_i^V)

3. 位置编码公式:
   PE(pos,2i) = sin(pos/10000^(2i/d_model))
   PE(pos,2i+1) = cos(pos/10000^(2i/d_model))

4. 层归一化公式:
   LayerNorm(x) = γ * (x - μ) / √(σ² + ε) + β

5. 前馈网络公式:
   FFN(x) = max(0, xW_1 + b_1)W_2 + b_2

6. 残差连接公式:
   SublayerConnection(x, sublayer) = x + Dropout(sublayer(LayerNorm(x)))

================================================================================
"""

class Transformer(nn.Module):
    """
    Transformer模型的主类
    包含编码器、解码器、词嵌入层和生成器
    
    数学表示:
    Transformer(src, tgt) = Generator(Decoder(Embed(tgt) + PE, Encoder(Embed(src) + PE)))
    """
    def __init__(self, encoder, decoder, src_embed, tgt_embed, generator):
        super(Transformer, self).__init__()
        self.encoder = encoder      # 编码器：将输入序列编码为隐藏表示
        self.decoder = decoder      # 解码器：基于编码器输出生成目标序列
        self.src_embed = src_embed  # 源语言词嵌入层
        self.tgt_embed = tgt_embed  # 目标语言词嵌入层
        self.generator = generator  # 生成器：将解码器输出转换为词汇概率分布

    def forward(self, src, tgt, src_mask, tgt_mask):
        """
        前向传播：编码输入序列，然后解码生成输出序列
        
        数学流程:
        1. memory = Encoder(Embed(src) + PE(src), src_mask)
        2. output = Decoder(Embed(tgt) + PE(tgt), memory, src_mask, tgt_mask)
        3. logits = Generator(output)
        
        参数:
        src: 源序列 (batch_size, src_len)
        tgt: 目标序列 (batch_size, tgt_len)
        src_mask: 源序列的掩码，用于忽略padding位置
        tgt_mask: 目标序列的掩码，用于防止看到未来信息
        """
        return self.decode(self.encode(src, src_mask), src_mask, tgt, tgt_mask)
    
    def encode(self, src, src_mask):
        """编码阶段：将输入序列转换为隐藏表示"""
        return self.encoder(self.src_embed(src), src_mask)
    
    def decode(self, memory, src_mask, tgt, tgt_mask):
        """解码阶段：基于编码器输出生成目标序列"""
        return self.decoder(self.tgt_embed(tgt), memory, src_mask, tgt_mask)
    
    def generate(self, memory, src_mask, tgt, tgt_mask):
        """生成阶段：将解码器输出转换为词汇概率分布"""
        return self.generator(self.decode(memory, src_mask, tgt, tgt_mask))
    
class Generator(nn.Module):
    """
    生成器：将解码器的输出转换为词汇表上的概率分布
    用于最终的预测任务
    
    数学公式:
    Generator(x) = log_softmax(xW + b)
    其中 W ∈ R^(d_model × vocab_size), b ∈ R^vocab_size
    """
    def __init__(self, d_model, vocab):
        super(Generator, self).__init__()
        self.proj = nn.Linear(d_model, vocab)  # 线性投影层：d_model -> vocab_size

    def forward(self, x):
        """将解码器输出转换为log概率分布"""
        return F.log_softmax(self.proj(x), dim=-1)  # 使用log_softmax提高数值稳定性
    
class LayerNorm(nn.Module):
    """
    层归一化：对每个样本的特征维度进行归一化
    有助于训练稳定性和收敛速度
    
    数学公式:
    LayerNorm(x) = γ * (x - μ) / √(σ² + ε) + β
    其中 μ = mean(x), σ² = var(x), γ和β是可学习参数
    """
    def __init__(self, features, eps=1e-6):
        super(LayerNorm, self).__init__()
        self.a_2 = nn.Parameter(torch.ones(features))   # 可学习的缩放参数 γ
        self.b_2 = nn.Parameter(torch.zeros(features))  # 可学习的偏移参数 β
        self.eps = eps  # 防止除零的小常数 ε

    def forward(self, x):
        """
        层归一化计算：
        1. 计算均值和标准差
        2. 归一化
        3. 应用可学习的缩放和偏移
        """
        mean = x.mean(dim=-1, keepdim=True)  # 计算最后一个维度的均值 μ
        std = x.std(dim=-1, keepdim=True)    # 计算最后一个维度的标准差 σ
        return self.a_2 * (x - mean) / (std + self.eps) + self.b_2
    
class SublayerConnection(nn.Module):
    """
    子层连接：实现残差连接和层归一化
    这是Transformer中的关键组件，有助于梯度传播
    
    数学公式:
    SublayerConnection(x, sublayer) = x + Dropout(sublayer(LayerNorm(x)))
    """
    def __init__(self, size, dropout):
        super(SublayerConnection, self).__init__()
        self.norm = LayerNorm(size)  # 层归一化
        self.dropout = nn.Dropout(dropout)  # Dropout正则化

    def forward(self, x, sublayer):
        """
        残差连接：x + Dropout(Sublayer(Norm(x)))
        这种设计有助于训练深层网络
        """
        return x + self.dropout(sublayer(self.norm(x)))

class PositionwiseFeedForward(nn.Module):
    """
    位置前馈网络：每个位置独立应用的两层全连接网络
    这是Transformer中的另一个关键组件
    
    数学公式:
    FFN(x) = max(0, xW_1 + b_1)W_2 + b_2
    其中 W_1 ∈ R^(d_model × d_ff), W_2 ∈ R^(d_ff × d_model)
    """
    def __init__(self, d_model, d_ff, dropout=0.1):
        super(PositionwiseFeedForward, self).__init__()
        self.w_1 = nn.Linear(d_model, d_ff)  # 第一层：d_model -> d_ff
        self.w_2 = nn.Linear(d_ff, d_model)  # 第二层：d_ff -> d_model
        self.dropout = nn.Dropout(dropout)

    def forward(self, x):
        """前馈网络：Linear -> ReLU -> Dropout -> Linear"""
        return self.w_2(self.dropout(F.relu(self.w_1(x))))
    
def attention(query, key, value, mask=None, dropout=None):
    """
    注意力机制的核心计算函数
    
    数学公式:
    Attention(Q,K,V) = softmax(QK^T/√d_k)V
    
    参数:
    query: 查询矩阵 (batch_size, h, seq_len, d_k)
    key: 键矩阵 (batch_size, h, seq_len, d_k)
    value: 值矩阵 (batch_size, h, seq_len, d_k)
    mask: 掩码，用于忽略某些位置
    """
    d_k = query.size(-1)  # 键的维度
    # 计算注意力分数：Q * K^T / sqrt(d_k)
    scores = torch.matmul(query, key.transpose(-2, -1)) / math.sqrt(d_k)
    
    if mask is not None:
        # 将掩码位置的值设为很小的负数，softmax后接近0
        scores = scores.masked_fill(mask == 0, -1e9)
    
    # 应用softmax得到注意力权重
    p_attn = F.softmax(scores, dim=-1)
    
    if dropout is not None:
        p_attn = dropout(p_attn)
    
    # 计算加权和：注意力权重 * 值矩阵
    return torch.matmul(p_attn, value), p_attn

class MultiHeadedAttention(nn.Module):
    """
    多头注意力机制：将注意力分成多个头，每个头关注不同的特征子空间
    然后将所有头的输出拼接起来
    
    数学公式:
    MultiHead(Q,K,V) = Concat(head_1,...,head_h)W^O
    其中 head_i = Attention(QW_i^Q, KW_i^K, VW_i^V)
    
    架构图:
    ┌─────────────────────────────────────────────────────────────────────────┐
    │                        MULTI-HEAD ATTENTION                            │
    ├─────────────────────────────────────────────────────────────────────────┤
    │                                                                         │
    │  输入: Q, K, V (batch_size, seq_len, d_model)                          │
    │    ↓                                                                    │
    │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐         │
    │  │   Q投影层        │  │   K投影层        │  │   V投影层        │         │
    │  │  Linear(d_model)│  │  Linear(d_model)│  │  Linear(d_model)│         │
    │  └─────────────────┘  └─────────────────┘  └─────────────────┘         │
    │    ↓                    ↓                    ↓                          │
    │  ┌─────────────────────────────────────────────────────────────────────┐ │
    │  │                    重塑为多头形式                                    │ │
    │  │  view(batch_size, seq_len, h, d_k).transpose(1, 2)                 │ │
    │  └─────────────────────────────────────────────────────────────────────┘ │
    │    ↓                                                                    │
    │  ┌─────────────────────────────────────────────────────────────────────┐ │
    │  │                    注意力计算 (h个头并行)                            │ │
    │  │  Attention(Q_i, K_i, V_i) = softmax(Q_i K_i^T/√d_k)V_i            │ │
    │  └─────────────────────────────────────────────────────────────────────┘ │
    │    ↓                                                                    │
    │  ┌─────────────────────────────────────────────────────────────────────┐ │
    │  │                    拼接多头输出                                      │ │
    │  │  transpose(1, 2).contiguous().view(batch_size, seq_len, d_model)   │ │
    │  └─────────────────────────────────────────────────────────────────────┘ │
    │    ↓                                                                    │
    │  ┌─────────────────┐                                                    │
    │  │   输出投影层     │                                                    │
    │  │  Linear(d_model)│                                                    │
    │  └─────────────────┘                                                    │
    │    ↓                                                                    │
    │  输出: (batch_size, seq_len, d_model)                                  │
    └─────────────────────────────────────────────────────────────────────────┘
    """
    def __init__(self, h, d_model, dropout=0.1):
        super(MultiHeadedAttention, self).__init__()
        assert d_model % h == 0  # 确保d_model能被h整除
        self.d_k = d_model // h  # 每个头的维度
        self.h = h  # 头的数量
        # 4个线性层：Q, K, V的投影 + 输出的投影
        self.linears = clones(nn.Linear(d_model, d_model), 4)
        self.attn = None  # 存储注意力权重（用于可视化）
        self.dropout = nn.Dropout(dropout)  

    def forward(self, query, key, value, mask=None):
        if mask is not None:
            mask = mask.unsqueeze(1)  # 为多头注意力扩展掩码维度
        
        nbatches = query.size(0)  # batch大小
        
        # 1. 线性投影并重塑为多头形式
        query, key, value = [
            l(x).view(nbatches, -1, self.h, self.d_k).transpose(1, 2) 
            for l, x in zip(self.linears, (query, key, value))
        ]
        
        # 2. 应用注意力机制
        x, self.attn = attention(query, key, value, mask=mask, dropout=self.dropout)
        
        # 3. 拼接多头输出并应用最后的线性层
        x = x.transpose(1, 2).contiguous().view(nbatches, -1, self.h * self.d_k)
        return self.linears[-1](x)

def clones(module, N):
    """创建N个相同模块的副本"""
    return nn.ModuleList([copy.deepcopy(module) for _ in range(N)])

class Encoder(nn.Module):
    """
    编码器：由N个编码器层堆叠而成
    每个编码器层包含自注意力机制和前馈网络
    
    数学表示:
    Encoder(x) = LayerNorm(EncoderLayer_N(...EncoderLayer_1(x)...))
    """
    def __init__(self, layer, N):
        super(Encoder, self).__init__()
        self.layers = clones(layer, N)  # N个编码器层
        self.norm = LayerNorm(layer.size)  # 最终的层归一化

    def forward(self, x, mask):
        """依次通过每个编码器层"""
        for layer in self.layers:
            x = layer(x, mask)
        return self.norm(x)  # 最后的层归一化

class EncoderLayer(nn.Module):
    """
    编码器层：包含自注意力机制和前馈网络
    每个子层都有残差连接和层归一化
    
    数学表示:
    EncoderLayer(x) = SublayerConnection(x, FFN(SublayerConnection(x, SelfAttn(x))))
    """
    def __init__(self, size, self_attn, feed_forward, dropout):
        super(EncoderLayer, self).__init__()
        self.self_attn = self_attn      # 自注意力机制
        self.feed_forward = feed_forward # 前馈网络
        self.sublayer = clones(SublayerConnection(size, dropout), 2)  # 2个子层连接
        self.size = size

    def forward(self, x, mask):
        """
        编码器层的前向传播：
        1. 自注意力子层
        2. 前馈网络子层
        """
        x = self.sublayer[0](x, lambda x: self.self_attn(x, x, x, mask))
        return self.sublayer[1](x, self.feed_forward)
    
class Decoder(nn.Module):
    """
    解码器：由N个解码器层堆叠而成
    每个解码器层包含自注意力、编码器-解码器注意力和前馈网络
    
    数学表示:
    Decoder(x, memory) = LayerNorm(DecoderLayer_N(...DecoderLayer_1(x, memory)...))
    """
    def __init__(self, layer, N):
        super(Decoder, self).__init__()
        self.layers = clones(layer, N)  # N个解码器层
        self.norm = LayerNorm(layer.size)  # 最终的层归一化

    def forward(self, x, memory, src_mask, tgt_mask):
        """依次通过每个解码器层"""
        for layer in self.layers:
            x = layer(x, memory, src_mask, tgt_mask)
        return self.norm(x)
    
class DecoderLayer(nn.Module):
    """
    解码器层：包含三个子层
    1. 自注意力机制（带掩码）
    2. 编码器-解码器注意力机制
    3. 前馈网络
    
    数学表示:
    DecoderLayer(x, memory) = SublayerConnection(x, FFN(
        SublayerConnection(x, CrossAttn(x, memory))(
            SublayerConnection(x, MaskedSelfAttn(x))
        )
    ))
    """
    def __init__(self, size, self_attn, src_attn, feed_forward, dropout):
        super(DecoderLayer, self).__init__()
        self.size = size
        self.self_attn = self_attn      # 自注意力机制
        self.src_attn = src_attn        # 编码器-解码器注意力机制
        self.feed_forward = feed_forward # 前馈网络
        self.sublayer = clones(SublayerConnection(size, dropout), 3)  # 3个子层连接

    def forward(self, x, memory, src_mask, tgt_mask):
        """
        解码器层的前向传播：
        1. 自注意力子层（带目标掩码）
        2. 编码器-解码器注意力子层
        3. 前馈网络子层
        """
        m = memory  # 编码器的输出
        x = self.sublayer[0](x, lambda x: self.self_attn(x, x, x, tgt_mask))
        x = self.sublayer[1](x, lambda x: self.src_attn(x, m, m, src_mask))
        return self.sublayer[2](x, self.feed_forward)
    
def subsequent_mask(size):
    """
    生成后续掩码：用于防止解码器看到未来的信息
    创建一个下三角矩阵，上三角部分为False（被掩码）
    
    示例 (size=4):
    ┌─────────┐
    │ 1 0 0 0 │  ← 位置0只能看到位置0
    │ 1 1 0 0 │  ← 位置1能看到位置0,1
    │ 1 1 1 0 │  ← 位置2能看到位置0,1,2
    │ 1 1 1 1 │  ← 位置3能看到所有位置
    └─────────┘
    """
    attn_shape = (1, size, size)
    # 创建上三角矩阵，k=1表示对角线以上（不包括对角线）
    subsequent_mask = torch.triu(torch.ones(attn_shape), k=1).astype('uint8')
    return torch.from_numpy(subsequent_mask) == 0  # 返回下三角为True的掩码

class Embeddings(nn.Module):
    """
    词嵌入层：将词汇索引转换为密集向量表示
    
    数学公式:
    Embedding(x) = lookup(x) * √d_model
    其中lookup(x)是词嵌入表中的向量
    """
    def __init__(self, d_model, vocab):
        super(Embeddings, self).__init__()
        self.lut = nn.Embedding(vocab, d_model)  # 词嵌入表
        self.d_model = d_model

    def forward(self, x):
        """
        词嵌入前向传播：
        1. 查找词嵌入
        2. 乘以sqrt(d_model)进行缩放（这是Transformer论文中的技巧）
        """
        return self.lut(x) * math.sqrt(self.d_model)
    
class PositionalEncoding(nn.Module):
    """
    位置编码：为序列中的每个位置添加位置信息
    使用正弦和余弦函数生成位置编码
    
    数学公式:
    PE(pos,2i) = sin(pos/10000^(2i/d_model))
    PE(pos,2i+1) = cos(pos/10000^(2i/d_model))
    
    其中pos是位置，i是维度索引
    
    架构图:
    ┌─────────────────────────────────────────────────────────────────────────┐
    │                        POSITIONAL ENCODING                             │
    ├─────────────────────────────────────────────────────────────────────────┤
    │                                                                         │
    │  位置索引: pos = 0, 1, 2, ..., seq_len-1                               │
    │    ↓                                                                    │
    │  ┌─────────────────────────────────────────────────────────────────────┐ │
    │  │                    频率计算                                          │ │
    │  │  freq_i = 1/10000^(2i/d_model)                                     │ │
    │  └─────────────────────────────────────────────────────────────────────┘ │
    │    ↓                                                                    │
    │  ┌─────────────────────────────────────────────────────────────────────┐ │
    │  │                    位置编码计算                                      │ │
    │  │  PE[pos,2i] = sin(pos * freq_i)                                    │ │
    │  │  PE[pos,2i+1] = cos(pos * freq_i)                                  │ │
    │  └─────────────────────────────────────────────────────────────────────┘ │
    │    ↓                                                                    │
    │  ┌─────────────────────────────────────────────────────────────────────┐ │
    │  │                    与输入相加                                        │ │
    │  │  output = input + PE[:, :seq_len]                                  │ │
    │  └─────────────────────────────────────────────────────────────────────┘ │
    └─────────────────────────────────────────────────────────────────────────┘
    """
    def __init__(self, d_model, dropout, max_len=5000):
        super(PositionalEncoding, self).__init__()
        self.dropout = nn.Dropout(dropout)

        # 创建位置编码矩阵
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        
        # 计算频率项: 1/10000^(2i/d_model)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * -(math.log(10000.0) / d_model))
        
        # 偶数位置使用sin，奇数位置使用cos
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0)  # 添加batch维度
        
        # 注册为buffer（不参与梯度更新）
        self.register_buffer('pe', pe)

    def forward(self, x):
        """
        添加位置编码到输入序列
        只取前x.size(1)个位置的位置编码
        """
        x = x + self.pe[:, :x.size(1)].clone().detach()
        return self.dropout(x)

def make_model(src_vocab, tgt_vocab, N=6, d_model=512, d_ff=2048, h=8, dropout=0.1):
    """
    构建完整的Transformer模型
    
    参数说明：
    - src_vocab: 源语言词汇表大小
    - tgt_vocab: 目标语言词汇表大小
    - N: 编码器和解码器的层数
    - d_model: 模型的维度
    - d_ff: 前馈网络的隐藏层维度
    - h: 多头注意力的头数
    - dropout: dropout概率
    
    完整模型架构:
    ┌─────────────────────────────────────────────────────────────────────────┐
    │                           TRANSFORMER MODEL                             │
    ├─────────────────────────────────────────────────────────────────────────┤
    │                                                                         │
    │  输入序列 (src)                                                          │
    │    ↓                                                                    │
    │  ┌─────────────────────────────────────────────────────────────────────┐ │
    │  │                    编码器部分                                        │ │
    │  │  Embeddings(src_vocab, d_model) + PositionalEncoding(d_model)      │ │
    │  │    ↓                                                                │ │
    │  │  Encoder(EncoderLayer × N)                                          │ │
    │  │    - SelfAttention(h=8, d_model=512)                               │ │
    │  │    - FeedForward(d_model=512, d_ff=2048)                           │ │
    │  │    - LayerNorm + Residual Connection                                │ │
    │  └─────────────────────────────────────────────────────────────────────┘ │
    │    ↓                                                                    │
    │  memory (编码器输出)                                                     │
    │    ↓                                                                    │
    │  ┌─────────────────────────────────────────────────────────────────────┐ │
    │  │                    解码器部分                                        │ │
    │  │  Embeddings(tgt_vocab, d_model) + PositionalEncoding(d_model)      │ │
    │  │    ↓                                                                │ │
    │  │  Decoder(DecoderLayer × N)                                          │ │
    │  │    - MaskedSelfAttention(h=8, d_model=512)                         │ │
    │  │    - CrossAttention(h=8, d_model=512)                              │ │
    │  │    - FeedForward(d_model=512, d_ff=2048)                           │ │
    │  │    - LayerNorm + Residual Connection                                │ │
    │  └─────────────────────────────────────────────────────────────────────┘ │
    │    ↓                                                                    │
    │  ┌─────────────────────────────────────────────────────────────────────┐ │
    │  │                    生成器部分                                        │ │
    │  │  Generator(d_model, tgt_vocab)                                      │ │
    │  │    - Linear(d_model, tgt_vocab)                                     │ │
    │  │    - LogSoftmax                                                      │ │
    │  └─────────────────────────────────────────────────────────────────────┘ │
    │    ↓                                                                    │
    │  输出概率分布 (log_softmax)                                              │
    └─────────────────────────────────────────────────────────────────────────┘
    """
    c = copy.deepcopy  # 用于创建模块的深拷贝
    
    # 创建注意力机制
    attn = MultiHeadedAttention(h, d_model)
    
    # 创建前馈网络
    ff = PositionwiseFeedForward(d_model, d_ff, dropout)
    
    # 创建位置编码
    position = PositionalEncoding(d_model, dropout)
    
    # 构建完整的Transformer模型
    model = Transformer(
        Encoder(EncoderLayer(d_model, c(attn), c(ff), dropout), N),  # 编码器
        Decoder(DecoderLayer(d_model, c(attn), c(attn), c(ff), dropout), N),  # 解码器
        nn.Sequential(Embeddings(d_model, src_vocab), c(position)),  # 源语言嵌入+位置编码
        nn.Sequential(Embeddings(d_model, tgt_vocab), c(position)),  # 目标语言嵌入+位置编码
        Generator(d_model, tgt_vocab)  # 生成器
    )
    return model

if __name__ == "__main__":
    # 创建一个简单的Transformer模型进行测试
    model = make_model(11, 11, N=2)  # 词汇表大小11，2层编码器/解码器
    
    # 创建测试输入
    src = torch.LongTensor([[1,2,3,4,5,6,7,8,9,10]])  # 源序列
    src_mask = torch.ones(1,1,10)  # 源序列掩码（全1表示没有padding）
    
    # 导出为ONNX格式（用于部署）
    torch.onnx._export(model, (src,src,src_mask,src_mask), "transformer.onnx")
    print(model)    