import pandas as pd
import requests
from datetime import datetime


def get_location_data(lon, lat, api_key):
    url = f"http://api.tianditu.gov.cn/geocoder?postStr={{'lon':{lon},'lat':{lat},'ver':1}}&type=geocode&tk={api_key}"
    response = requests.get(url)
    if response.status_code == 200:
        return response.json()
    else:
        return None


def process_county_code(county_code):
    province_code = county_code[3:5]
    city_code = county_code[3:7]
    region_code = county_code[3:9]
    return province_code, city_code, region_code


# 码头导入处理
if __name__ == '__main__':
    file_path = '/usr/local/coding/extracted_data.xlsx'
    df = pd.read_excel(file_path, sheet_name='Sheet1')
    with open('/usr/local/coding/extracted_data.sql', 'w') as file:
        for index, row in df.iterrows():
            s1 = row.to_dict().get("码头名称")
            s2 = row.to_dict().get("码头简称")
            s3 = row.to_dict().get("经纬度", "[0.0, 0.0]")
            if pd.notna(s3):
                lon, lat = map(float, s3.strip('[]').split(','))
                location_data = get_location_data(lon, lat, '85814748d871d987b3f0587d04715c96')
                if location_data and location_data.get('status') == '0':
                    formatted_address = location_data['result']['formatted_address']
                    county_code = location_data['result']['addressComponent']['county_code']
                    province_code, city_code, region_code = process_county_code(county_code)
                    name = s1
                    short_name = s2
                    address = formatted_address
                    full_address = formatted_address
                    lat_lon = f"{lon},{lat}"
                    created_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    updated_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    sql = f"INSERT INTO zhihaoscm.t_port (name, short_name, province_code, city_code, region_code, address, full_address, lat_lon, del, created_by, created_time, updated_by, updated_time) VALUES ('{name}', '{short_name}', '{province_code}', '{city_code}', '{region_code}', '{address}', '{full_address}', '{lat_lon}', 0, 1, '{created_time}', 42, '{updated_time}');"
                    file.write(sql + '\n')
