import numpy as np
import matplotlib.pyplot as plt


def sigmoid(x):
    return 1 / (1 + np.exp(-x))


def plot_activation_function(func, x, title):
    y = func(x)
    plt.plot(x, y)
    plt.title(title)
    plt.xlabel('x')
    plt.ylabel('y')
    plt.grid()
    plt.show()


def tanh(x):
    return np.tanh(x)


def relu(x):
    return np.maximum(0, x)


def leaky_relu(x, alpha=0.01):
    return np.where(x > 0, x, x * alpha)


def parametric_relu(x, parm=0.05):
    return np.where(x > 0, x, x * parm)


x_value = np.arange(-10, 10, 0.1)


# plot_activation_function(sigmoid, x_value, 'Sigmoid')
# plot_activation_function(tanh, x_value, 'Tanh')
# plot_activation_function(relu, x_value, 'Relu')
# plot_activation_function(leaky_relu, x_value, 'Leaky Relu')
# plot_activation_function(parametric_relu, x_value, 'Parametric Relu')


def f(x):
    return x ** 2


def df(x):
    return 2 * x


x = np.linspace(-3, 3, 100)
y = f(x)

plt.figure(figsize=(8, 6))
plt.plot(x, y, label='f(x) = x^2')
# plt.show()

x1 = 1
y1 = f(x1)
slope = df(x1)


def tangent_line(x, x1, y1, slope):
    return slope * (x - x1) + y1


x_tangent = np.linspace(x1 - 1, x1 + 1, 10)
y_tangent = tangent_line(x_tangent, x1, y1, slope)
plt.plot(x_tangent, y_tangent, label="Tangent at x = 1", color='red')
plt.scatter([x1],[y1],color='black')
plt.legend()
plt.xlabel('x')
plt.ylabel('f(x)')
plt.title("function and tangent line at a point")
plt.grid(True)
plt.show()



