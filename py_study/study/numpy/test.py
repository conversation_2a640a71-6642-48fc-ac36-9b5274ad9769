import pandas as pd

# Example usage
if __name__ == '__main__':
    file_path = '/usr/local/coding/船舶详情.xlsx'
    df = pd.read_excel(file_path, sheet_name='Sheet1')
    with open('/usr/local/coding/update_statements.sql', 'w') as file:
        for index, row in df.iterrows():
            s1 = row.to_dict().get("mmsi")
            s2 = row.to_dict().get("载重吨位")
            s3 = row.to_dict().get("船舶总长")
            s4 = row.to_dict().get("型宽")
            s5 = row.to_dict().get("型深")
            s6 = row.to_dict().get("吃水深度")
            s7 = row.to_dict().get("联系电话")
            s8 = row.to_dict().get("联系人")

            update_fields = []
            if pd.notna(s2):
                update_fields.append(f"ton_capacity = {s2}")
            if pd.notna(s3):
                update_fields.append(f"length = {s3}")
            if pd.notna(s4):
                update_fields.append(f"width = {s4}")
            if pd.notna(s5):
                update_fields.append(f"deep = {s5}")
            if pd.notna(s7):
                s7 = str(s7)
                s7 = s7.replace('.0', '') if s7.endswith('.0') else s7
                update_fields.append(f"mobile = '{s7}'")
            if pd.notna(s8):
                update_fields.append(f"captain = '{s8}'")

            if update_fields:
                update_clause = ", ".join(update_fields)
                sql = f"UPDATE t_ship SET {update_clause} WHERE id = '{s1}';"
                file.write(sql + '\n')