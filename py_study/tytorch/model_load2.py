import torch
from transformers import ViTForImageClassification, ViTFeatureExtractor

# 下载并加载模型
model_name = "google/vit-base-patch16-224-in21k"
model = ViTForImageClassification.from_pretrained(model_name)
feature_extractor = ViTFeatureExtractor.from_pretrained(model_name)

# 创建示例输入
example_input = torch.rand(1, 3, 224, 224)

# 将模型转换为 TorchScript 格式
model.eval()
traced_model = torch.jit.trace(model, example_input)
traced_model.save("vit_model.pt")

# 保存 feature extractor
feature_extractor.save_pretrained("models/vit")