import torch
from torch import nn
from torch.nn import Conv2d, Max<PERSON>ool2d, <PERSON><PERSON>, <PERSON>ar, Sequential

# 搭建神经网络
class Tudui(nn.Module):
    def __init__(self):
        super(<PERSON>du<PERSON>, self).__init__()
        self.model1 = Sequential(
            Conv2d(3, 32, 5, padding=2),
            MaxPool2d(2),
            Conv2d(32, 32, 5, padding=2),
            MaxPool2d(2),
            Conv2d(32, 64, 5, padding=2),
            MaxPool2d(2),
            Flatten(),
            Linear(1024, 64),
            Linear(64, 10)
        )

    def forward(self, x):
        return self.model1(x)

if __name__ == '__main__':
    tudui = Tudui()
    input = torch.ones((64,3,32,32))
    output = tudui(input)
    print(output.shape)
