import torch
import torchvision
from torch import nn
from torch.ao.nn.quantized import Sigmoid
from torch.nn import ReLU
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter

dataset = torchvision.datasets.CIFAR10("./dataset", train=False, transform=torchvision.transforms.ToTensor(),
                                       download=True)
dataloader = DataLoader(dataset, batch_size=64)
writer = SummaryWriter("logs")
input = torch.tensor([[1, -0.5],
                      [-1, 3]])
input = torch.reshape(input, (-1, 1, 2, 2))
print(input.shape)


class Tudui(nn.Module):
    def __init__(self):
        super(Tudui, self).__init__()
        self.relu1 = ReLU()
        self.sigmoid1 = Sigmoid(0.1, 10)

    def forward(self, input):
        return self.sigmoid1(input)


tudui = Tudui()

step = 0
for data in dataloader:
    imgs, targets = data
    writer.add_image("input", imgs, step)
    output = tudui(imgs)
    writer.add_image("output", imgs, step)
    step = step + 1

writer.close()
