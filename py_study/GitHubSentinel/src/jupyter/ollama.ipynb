{"cells": [{"cell_type": "markdown", "id": "b99f3090-1500-4e25-a900-3f0bebfa3ace", "metadata": {}, "source": ["# Ollama 快速入门\n", "\n", "## 调用 LLama 3 模型服务（文本生成 API）"]}, {"cell_type": "code", "execution_count": 2, "id": "83a77eb9-64d1-48d0-a4f2-477b854998ae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'model': 'llama3', 'created_at': '2024-08-27T09:14:43.655739841Z', 'response': '{ \"times\": [\\n  { \"time\": \"dawn\", \"color\": \"#66CCCC\" },\\n  { \"time\": \"morning\", \"color\": \"#00BFFF\" },\\n  { \"time\": \"noon\", \"color\": \"#ADD8E6\" },\\n  { \"time\": \"afternoon\", \"color\": \"#FFC0CB\" },\\n  { \"time\": \"sunset\", \"color\": \"#FF69B4\" },\\n  { \"time\": \"evening\", \"color\": \"#6666CC\" }\\n] }\\n\\n  \\n\\n  \\n ', 'done': True, 'done_reason': 'stop', 'context': [128006, 882, 128007, 271, 3923, 1933, 374, 279, 13180, 520, 2204, 3115, 315, 279, 1938, 30, 40633, 1701, 4823, 128009, 128006, 78191, 128007, 271, 90, 330, 15487, 794, 2330, 220, 314, 330, 1712, 794, 330, 67, 6513, 498, 330, 3506, 794, 5993, 2287, 49032, 1, 1173, 220, 314, 330, 1712, 794, 330, 57902, 1251, 498, 330, 3506, 794, 5993, 410, 33, 32303, 1, 1173, 220, 314, 330, 1712, 794, 330, 12684, 498, 330, 3506, 794, 5993, 16040, 23, 36, 21, 1, 1173, 220, 314, 330, 1712, 794, 330, 10924, 12684, 498, 330, 3506, 794, 5993, 1785, 34, 15, 13276, 1, 1173, 220, 314, 330, 1712, 794, 330, 82, 37904, 498, 330, 3506, 794, 5993, 1785, 3076, 33, 19, 1, 1173, 220, 314, 330, 1712, 794, 330, 17206, 287, 498, 330, 3506, 794, 5993, 10943, 21, 3791, 1, 457, 60, 557, 19124, 2355, 220], 'total_duration': 2938311800, 'load_duration': 23136029, 'prompt_eval_count': 25, 'prompt_eval_duration': 38596000, 'eval_count': 120, 'eval_duration': 2834880000}\n"]}], "source": ["import requests\n", "\n", "url = \"http://localhost:11434/api/generate\"\n", "payload = {\n", "    \"model\": \"llama3\",\n", "    \"prompt\": \"What color is the sky at different times of the day? Respond using JSON\",\n", "    \"format\": \"json\",\n", "    \"stream\": False\n", "}\n", "\n", "response = requests.post(url, json=payload)\n", "\n", "print(response.json())"]}, {"cell_type": "markdown", "id": "bdcba421-78bc-4074-844b-7a510af047cd", "metadata": {}, "source": ["## 调用 Gemma 2 模型服务（对话聊天 API）"]}, {"cell_type": "code", "execution_count": 5, "id": "dedf3232-e6a8-4b91-b5f2-15b1536772e2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'model': 'gemma2:2b', 'created_at': '2024-08-28T11:42:21.716755813Z', 'message': {'role': 'assistant', 'content': \"The sky appears blue due to a phenomenon called **Rayleigh scattering**. \\n\\nHere's a simplified breakdown:\\n\\n1. **Sunlight:** Sunlight consists of all colors of the rainbow. When it enters Earth's atmosphere, it encounters tiny air molecules (mostly nitrogen and oxygen).\\n\\n2. **Scattering:** These air molecules are much smaller than the wavelengths of visible light.  When sunlight hits these molecules, they scatter the light in different directions. \\n\\n3. **Blue Light Dominates:** Blue light has a shorter wavelength than other colors in the visible spectrum. This means it gets scattered more effectively by air molecules compared to longer wavelengths like red and orange.  \\n\\n4. **Appearance of the Sky:** As we look up, our eyes see all the scattered blue light coming from all directions. That's why we perceive the sky as blue. \\n\\n\\n**Other factors affecting the sky's color:**\\n\\n* **Time of Day:** At sunrise or sunset, sunlight travels through more atmosphere to reach us. This longer path causes more scattering of red and orange wavelengths, resulting in those beautiful hues.  \\n* **Clouds and Pollution:** Clouds are made up of water droplets, which are much larger than air molecules. They scatter all wavelengths of light equally, resulting in white or gray clouds. Air pollution can also influence the color of the sky by absorbing and scattering some colors. \\n\\n\\nLet me know if you'd like more details about any specific aspect! \\n\"}, 'done_reason': 'stop', 'done': True, 'total_duration': 6860605233, 'load_duration': 2290594767, 'prompt_eval_count': 15, 'prompt_eval_duration': 22305000, 'eval_count': 301, 'eval_duration': 4504272000}\n"]}], "source": ["import requests\n", "\n", "url = \"http://localhost:11434/api/chat\"\n", "payload = {\n", "    \"model\": \"gemma2:2b\",\n", "    \"messages\": [\n", "        {\"role\": \"user\", \"content\": \"Why is the sky blue?\"}\n", "    ],\n", "    \"stream\": False\n", "}\n", "\n", "response = requests.post(url, json=payload)\n", "print(response.json())\n"]}, {"cell_type": "code", "execution_count": null, "id": "f6bc0adc-4e7f-4ecb-8960-5ddcde6f26fa", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}