{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 准备工作\n", "\n", "1.确保您按照[README](README-CN.md)中的说明在环境中设置了API密钥\n", "\n", "2.安装依赖包"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"scrolled": true}, "outputs": [], "source": ["# 如果已经执行 pip install -r requirements.txt，以下命令不建议执行，避免版本升级后，API不兼容\n", "# !pip install tiktoken openai pandas matplotlib plotly scikit-learn numpy"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Name: tik<PERSON><PERSON>\n", "Version: 0.9.0\n", "Summary: tiktoken is a fast BPE tokeniser for use with OpenAI's models\n", "Home-page: https://github.com/openai/tiktoken\n", "Author: <PERSON><PERSON><PERSON>\n", "Author-email: shan<PERSON><PERSON>@openai.com\n", "License: MIT License\n", "        \n", "        Copyright (c) 2022 <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>\n", "        \n", "        Permission is hereby granted, free of charge, to any person obtaining a copy\n", "        of this software and associated documentation files (the \"Software\"), to deal\n", "        in the Software without restriction, including without limitation the rights\n", "        to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n", "        copies of the Software, and to permit persons to whom the Software is\n", "        furnished to do so, subject to the following conditions:\n", "        \n", "        The above copyright notice and this permission notice shall be included in all\n", "        copies or substantial portions of the Software.\n", "        \n", "        THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n", "        IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n", "        FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n", "        AUTHORS OR <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>RS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n", "        LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n", "        OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n", "        SOFTWARE.\n", "        \n", "Location: /root/miniconda3/envs/langchain/lib/python3.10/site-packages\n", "Requires: regex, requests\n", "Required-by: lang<PERSON><PERSON>-<PERSON>ai\n", "---\n", "Name: openai\n", "Version: 1.61.1\n", "Summary: The official Python library for the openai API\n", "Home-page: https://github.com/openai/openai-python\n", "Author: \n", "Author-email: OpenAI <<EMAIL>>\n", "License-Expression: Apache-2.0\n", "Location: /root/miniconda3/envs/langchain/lib/python3.10/site-packages\n", "Requires: anyio, distro, httpx, jiter, pydantic, sniffio, tqdm, typing-extensions\n", "Required-by: lang<PERSON><PERSON>-<PERSON>ai\n", "---\n", "Name: pandas\n", "Version: 2.2.2\n", "Summary: Powerful data structures for data analysis, time series, and statistics\n", "Home-page: https://pandas.pydata.org\n", "Author: \n", "Author-email: The Pandas Development Team <<EMAIL>>\n", "License: BSD 3-Clause License\n", "        \n", "        Copyright (c) 2008-2011, AQR Capital Management, LLC, Lambda Foundry, Inc. and PyData Development Team\n", "        All rights reserved.\n", "        \n", "        Copyright (c) 2011-2023, Open source contributors.\n", "        \n", "        Redistribution and use in source and binary forms, with or without\n", "        modification, are permitted provided that the following conditions are met:\n", "        \n", "        * Redistributions of source code must retain the above copyright notice, this\n", "          list of conditions and the following disclaimer.\n", "        \n", "        * Redistributions in binary form must reproduce the above copyright notice,\n", "          this list of conditions and the following disclaimer in the documentation\n", "          and/or other materials provided with the distribution.\n", "        \n", "        * Neither the name of the copyright holder nor the names of its\n", "          contributors may be used to endorse or promote products derived from\n", "          this software without specific prior written permission.\n", "        \n", "        THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n", "        AND ANY EXPRESS OR <PERSON><PERSON><PERSON>IED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n", "        IMPLIED WARRANTIES OF ME<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> AND FITNESS FOR A PARTICULAR PURPOSE ARE\n", "        DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE\n", "        FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL\n", "        DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR\n", "        SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER\n", "        CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,\n", "        OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n", "        OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n", "Location: /root/miniconda3/envs/langchain/lib/python3.10/site-packages\n", "Requires: numpy, python-date<PERSON>l, pytz, t<PERSON>ta\n", "Required-by: gradio\n", "---\n", "Name: <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "Version: 3.10.0\n", "Summary: Python plotting package\n", "Home-page: https://matplotlib.org\n", "Author: <PERSON>, <PERSON>\n", "Author-email: Unknown <<EMAIL>>\n", "License: License agreement for matplotlib versions 1.3.0 and later\n", "         =========================================================\n", "         \n", "         1. This LICENSE AGREEMENT is between the Matplotlib Development Team\n", "         (\"MDT\"), and the Individual or Organization (\"Licensee\") accessing and\n", "         otherwise using matplotlib software in source or binary form and its\n", "         associated documentation.\n", "         \n", "         2. Subject to the terms and conditions of this License Agreement, MDT\n", "         hereby grants Licensee a nonexclusive, royalty-free, world-wide license\n", "         to reproduce, analyze, test, perform and/or display publicly, prepare\n", "         derivative works, distribute, and otherwise use matplotlib\n", "         alone or in any derivative version, provided, however, that MDT's\n", "         License Agreement and MDT's notice of copyright, i.e., \"Copyright (c)\n", "         2012- Matplotlib Development Team; All Rights Reserved\" are retained in\n", "         matplotlib  alone or in any derivative version prepared by\n", "         Licensee.\n", "         \n", "         3. In the event Licensee prepares a derivative work that is based on or\n", "         incorporates matplotlib or any part thereof, and wants to\n", "         make the derivative work available to others as provided herein, then\n", "         Licensee hereby agrees to include in any such work a brief summary of\n", "         the changes made to matplotlib .\n", "         \n", "         4. MDT is making matplotlib available to Licensee on an \"AS\n", "         IS\" basis.  MDT MAKES NO REPRESENTATIONS OR WARRANTIES, EXPRESS OR\n", "         IMPLIED.  BY WAY OF EXAMPLE, BUT NOT LIMITATION, MDT MAKES NO AND\n", "         DISCLAIMS ANY REPRESENTATION OR WARRANTY OF MERCHANTABILITY OR FITNESS\n", "         FOR ANY PARTICULAR PURPOSE OR THAT THE USE OF MATPLOTLIB\n", "         WILL NOT INFRINGE ANY THIRD PARTY RIGHTS.\n", "         \n", "         5. MDT SHALL NOT BE LIABLE TO LICENSEE OR ANY OTHER USERS OF MATPLOTLIB\n", "          FOR ANY INCIDENTAL, SPECIAL, OR CONSEQUENTIAL DAMAGES OR\n", "         LOSS AS A RESULT OF M<PERSON><PERSON>YING, DISTRIBUTING, OR OTHERWISE USING\n", "         MATPLOTL<PERSON> , OR ANY DERIVATIVE THEREOF, EVEN IF ADVISED OF\n", "         THE POSSIBILITY THEREOF.\n", "         \n", "         6. This License Agreement will automatically terminate upon a material\n", "         breach of its terms and conditions.\n", "         \n", "         7. Nothing in this License Agreement shall be deemed to create any\n", "         relationship of agency, partnership, or joint venture between MDT and\n", "         Licensee.  This License Agreement does not grant permission to use MDT\n", "         trademarks or trade name in a trademark sense to endorse or promote\n", "         products or services of Licensee, or any third party.\n", "         \n", "         8. By copying, installing or otherwise using matplotlib ,\n", "         Licensee agrees to be bound by the terms and conditions of this License\n", "         Agreement.\n", "         \n", "         License agreement for matplotlib versions prior to 1.3.0\n", "         ========================================================\n", "         \n", "         1. This LICENSE AGREEMENT is between <PERSON> (\"JDH\"), and the\n", "         Individual or Organization (\"Licensee\") accessing and otherwise using\n", "         matplotlib software in source or binary form and its associated\n", "         documentation.\n", "         \n", "         2. Subject to the terms and conditions of this License Agreement, JDH\n", "         hereby grants Licensee a nonexclusive, royalty-free, world-wide license\n", "         to reproduce, analyze, test, perform and/or display publicly, prepare\n", "         derivative works, distribute, and otherwise use matplotlib\n", "         alone or in any derivative version, provided, however, that JDH's\n", "         License Agreement and JDH's notice of copyright, i.e., \"Copyright (c)\n", "         2002-2011 <PERSON>; All Rights Reserved\" are retained in\n", "         matplotlib  alone or in any derivative version prepared by\n", "         Licensee.\n", "         \n", "         3. In the event Licensee prepares a derivative work that is based on or\n", "         incorporates matplotlib  or any part thereof, and wants to\n", "         make the derivative work available to others as provided herein, then\n", "         Licensee hereby agrees to include in any such work a brief summary of\n", "         the changes made to matplotlib.\n", "         \n", "         4. JDH is making matplotlib  available to Licensee on an \"AS\n", "         IS\" basis.  <PERSON>D<PERSON> MAKES NO REPRESENTATIONS OR WARRANTIES, EXPRESS OR\n", "         IMPLIED.  BY WAY OF EXAMPLE, BUT NOT LIMITATION, JDH MAKES NO AND\n", "         DISCLAIMS ANY REPRESENTATION OR WARRANTY OF MERCHANTABILITY OR FITNESS\n", "         FOR ANY PARTICULAR PURPOSE OR THAT THE USE OF MATPLOTLIB\n", "         WILL NOT INFRINGE ANY THIRD PARTY RIGHTS.\n", "         \n", "         5. JDH SHALL NOT BE LIABLE TO LICENSEE OR ANY OTHER USERS OF MATPLOTLIB\n", "          FOR ANY INCIDENTAL, SPECIAL, OR CONSEQUENTIAL DAMAGES OR\n", "         LOSS AS A RESULT OF M<PERSON><PERSON>YING, DISTRIBUTING, OR OTHERWISE USING\n", "         MATPLOTL<PERSON> , OR ANY DERIVATIVE THEREOF, EVEN IF ADVISED OF\n", "         THE POSSIBILITY THEREOF.\n", "         \n", "         6. This License Agreement will automatically terminate upon a material\n", "         breach of its terms and conditions.\n", "         \n", "         7. Nothing in this License Agreement shall be deemed to create any\n", "         relationship of agency, partnership, or joint venture between JDH and\n", "         Licensee.  This License Agreement does not grant permission to use JDH\n", "         trademarks or trade name in a trademark sense to endorse or promote\n", "         products or services of Licensee, or any third party.\n", "         \n", "         8. By copying, installing or otherwise using matplotlib,\n", "         Licensee agrees to be bound by the terms and conditions of this License\n", "         Agreement.\n", "Location: /root/miniconda3/envs/langchain/lib/python3.10/site-packages\n", "Requires: contourpy, cycler, fonttools, kiwisolver, numpy, packaging, pillow, pyparsing, python-dateutil\n", "Required-by: \n", "---\n", "Name: plotly\n", "Version: 5.24.1\n", "Summary: An open-source, interactive data visualization library for Python\n", "Home-page: https://plotly.com/python/\n", "Author: <PERSON>\n", "Author-email: <EMAIL>\n", "License: MIT\n", "Location: /root/miniconda3/envs/langchain/lib/python3.10/site-packages\n", "Requires: packaging, tenacity\n", "Required-by: \n", "---\n", "Name: scikit-learn\n", "Version: 1.6.1\n", "Summary: A set of python modules for machine learning and data mining\n", "Home-page: https://scikit-learn.org\n", "Author: \n", "Author-email: \n", "License: BSD 3-Clause License\n", "         \n", "         Copyright (c) 2007-2024 The scikit-learn developers.\n", "         All rights reserved.\n", "         \n", "         Redistribution and use in source and binary forms, with or without\n", "         modification, are permitted provided that the following conditions are met:\n", "         \n", "         * Redistributions of source code must retain the above copyright notice, this\n", "           list of conditions and the following disclaimer.\n", "         \n", "         * Redistributions in binary form must reproduce the above copyright notice,\n", "           this list of conditions and the following disclaimer in the documentation\n", "           and/or other materials provided with the distribution.\n", "         \n", "         * Neither the name of the copyright holder nor the names of its\n", "           contributors may be used to endorse or promote products derived from\n", "           this software without specific prior written permission.\n", "         \n", "         THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n", "         AND ANY EXPRESS OR <PERSON><PERSON><PERSON>IED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n", "         IMPLIED WARRANTIES OF ME<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> AND FITNESS FOR A PARTICULAR PURPOSE ARE\n", "         DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE\n", "         FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL\n", "         DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR\n", "         SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER\n", "         CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,\n", "         OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n", "         OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n", "         \n", "         ----\n", "         \n", "         This binary distribution of scikit-learn also bundles the following software:\n", "         \n", "         ----\n", "         \n", "         Name: GCC runtime library\n", "         Files: scikit_learn.libs/libgomp*.so*\n", "         Availability: https://gcc.gnu.org/git/?p=gcc.git;a=tree;f=libgomp\n", "         \n", "         GCC RUNTIME LIBRARY EXCEPTION\n", "         \n", "         Version 3.1, 31 March 2009\n", "         \n", "         Copyright (C) 2009 Free Software Foundation, Inc. <http://fsf.org/>\n", "         \n", "         Everyone is permitted to copy and distribute verbatim copies of this\n", "         license document, but changing it is not allowed.\n", "         \n", "         This GCC Runtime Library Exception (\"Exception\") is an additional\n", "         permission under section 7 of the GNU General Public License, version\n", "         3 (\"GPLv3\"). It applies to a given file (the \"Runtime Library\") that\n", "         bears a notice placed by the copyright holder of the file stating that\n", "         the file is governed by GPLv3 along with this Exception.\n", "         \n", "         When you use GCC to compile a program, GCC may combine portions of\n", "         certain GCC header files and runtime libraries with the compiled\n", "         program. The purpose of this Exception is to allow compilation of\n", "         non-GPL (including proprietary) programs to use, in this way, the\n", "         header files and runtime libraries covered by this Exception.\n", "         \n", "         0. Definitions.\n", "         \n", "         A file is an \"Independent Module\" if it either requires the Runtime\n", "         Library for execution after a Compilation Process, or makes use of an\n", "         interface provided by the Runtime Library, but is not otherwise based\n", "         on the Runtime Library.\n", "         \n", "         \"GCC\" means a version of the GNU Compiler Collection, with or without\n", "         modifications, governed by version 3 (or a specified later version) of\n", "         the GNU General Public License (GPL) with the option of using any\n", "         subsequent versions published by the FSF.\n", "         \n", "         \"GPL-compatible Software\" is software whose conditions of propagation,\n", "         modification and use would permit combination with GCC in accord with\n", "         the license of GCC.\n", "         \n", "         \"Target Code\" refers to output from any compiler for a real or virtual\n", "         target processor architecture, in executable form or suitable for\n", "         input to an assembler, loader, linker and/or execution\n", "         phase. Notwithstanding that, Target Code does not include data in any\n", "         format that is used as a compiler intermediate representation, or used\n", "         for producing a compiler intermediate representation.\n", "         \n", "         The \"Compilation Process\" transforms code entirely represented in\n", "         non-intermediate languages designed for human-written code, and/or in\n", "         Java Virtual Machine byte code, into Target Code. Thus, for example,\n", "         use of source code generators and preprocessors need not be considered\n", "         part of the Compilation Process, since the Compilation Process can be\n", "         understood as starting with the output of the generators or\n", "         preprocessors.\n", "         \n", "         A Compilation Process is \"Eligible\" if it is done using GCC, alone or\n", "         with other GPL-compatible software, or if it is done without using any\n", "         work based on GCC. For example, using non-GPL-compatible Software to\n", "         optimize any GCC intermediate representations would not qualify as an\n", "         Eligible Compilation Process.\n", "         \n", "         1. Grant of Additional Permission.\n", "         \n", "         You have permission to propagate a work of Target Code formed by\n", "         combining the Runtime Library with Independent Modules, even if such\n", "         propagation would otherwise violate the terms of GPLv3, provided that\n", "         all Target Code was generated by Eligible Compilation Processes. You\n", "         may then convey such a combination under terms of your choice,\n", "         consistent with the licensing of the Independent Modules.\n", "         \n", "         2. No Weakening of GCC Copyleft.\n", "         \n", "         The availability of this Exception does not imply any general\n", "         presumption that third-party software is unaffected by the copyleft\n", "         requirements of the license of GCC.\n", "         \n", "Location: /root/miniconda3/envs/langchain/lib/python3.10/site-packages\n", "Requires: joblib, numpy, scipy, threadpoolctl\n", "Required-by: \n", "---\n", "Name: numpy\n", "Version: 1.26.4\n", "Summary: Fundamental package for array computing in Python\n", "Home-page: https://numpy.org\n", "Author: <PERSON> et al.\n", "Author-email: \n", "License: Copyright (c) 2005-2023, NumPy Developers.\n", "        All rights reserved.\n", "        \n", "        Redistribution and use in source and binary forms, with or without\n", "        modification, are permitted provided that the following conditions are\n", "        met:\n", "        \n", "            * Redistributions of source code must retain the above copyright\n", "               notice, this list of conditions and the following disclaimer.\n", "        \n", "            * Redistributions in binary form must reproduce the above\n", "               copyright notice, this list of conditions and the following\n", "               disclaimer in the documentation and/or other materials provided\n", "               with the distribution.\n", "        \n", "            * Neither the name of the NumPy Developers nor the names of any\n", "               contributors may be used to endorse or promote products derived\n", "               from this software without specific prior written permission.\n", "        \n", "        THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n", "        \"AS IS\" AND ANY EXPRESS OR <PERSON><PERSON><PERSON>IED WARRANTIES, INCLUDING, BUT NOT\n", "        LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\n", "        A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT\n", "        OWNER OR <PERSON><PERSON><PERSON><PERSON><PERSON>ORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,\n", "        SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT\n", "        LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n", "        DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n", "        THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n", "        (INCLUDING NEG<PERSON>IGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n", "        OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n", "        \n", "        ----\n", "        \n", "        The NumPy repository and source distributions bundle several libraries that are\n", "        compatibly licensed.  We list these here.\n", "        \n", "        Name: lapack-lite\n", "        Files: numpy/linalg/lapack_lite/*\n", "        License: BSD-3-<PERSON>e\n", "          For details, see numpy/linalg/lapack_lite/LICENSE.txt\n", "        \n", "        Name: te<PERSON><PERSON>\n", "        Files: tools/npy_tempita/*\n", "        License: MIT\n", "          For details, see tools/npy_tempita/license.txt\n", "        \n", "        Name: dragon4\n", "        Files: numpy/core/src/multiarray/dragon4.c\n", "        License: MIT\n", "          For license text, see numpy/core/src/multiarray/dragon4.c\n", "        \n", "        Name: libdivide\n", "        Files: numpy/core/include/numpy/libdivide/*\n", "        License: Zlib\n", "          For license text, see numpy/core/include/numpy/libdivide/LICENSE.txt\n", "        \n", "        \n", "        Note that the following files are vendored in the repository and sdist but not\n", "        installed in built numpy packages:\n", "        \n", "        Name: <PERSON><PERSON>\n", "        Files: vendored-meson/meson/*\n", "        License: Apache 2.0\n", "          For license text, see vendored-meson/meson/COPYING\n", "        \n", "        Name: spin\n", "        Files: .spin/cmds.py\n", "        License: BSD-3\n", "          For license text, see .spin/LICENSE\n", "        \n", "        ----\n", "        \n", "        This binary distribution of NumPy also bundles the following software:\n", "        \n", "        \n", "        Name: OpenBLAS\n", "        Files: numpy.libs/libopenblas*.so\n", "        Description: bundled as a dynamically linked library\n", "        Availability: https://github.com/OpenMathLib/OpenBLAS/\n", "        License: BSD-3-<PERSON>e\n", "          Copyright (c) 2011-2014, The OpenBLAS Project\n", "          All rights reserved.\n", "        \n", "          Redistribution and use in source and binary forms, with or without\n", "          modification, are permitted provided that the following conditions are\n", "          met:\n", "        \n", "             1. Redistributions of source code must retain the above copyright\n", "                notice, this list of conditions and the following disclaimer.\n", "        \n", "             2. Redistributions in binary form must reproduce the above copyright\n", "                notice, this list of conditions and the following disclaimer in\n", "                the documentation and/or other materials provided with the\n", "                distribution.\n", "             3. Neither the name of the OpenBLAS project nor the names of\n", "                its contributors may be used to endorse or promote products\n", "                derived from this software without specific prior written\n", "                permission.\n", "        \n", "          THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n", "          AND ANY EXPRESS OR <PERSON><PERSON><PERSON>IED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n", "          IMPLIED WARRANTIES OF ME<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> AND FITNESS FOR A PARTICULAR PURPOSE\n", "          ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE\n", "          LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL\n", "          DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR\n", "          SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER\n", "          CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,\n", "          OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE\n", "          USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n", "        \n", "        \n", "        Name: LAPACK\n", "        Files: numpy.libs/libopenblas*.so\n", "        Description: bundled in OpenBLAS\n", "        Availability: https://github.com/OpenMathLib/OpenBLAS/\n", "        License: BSD-3-Clause-Attribution\n", "          Copyright (c) 1992-2013 The University of Tennessee and The University\n", "                                  of Tennessee Research Foundation.  All rights\n", "                                  reserved.\n", "          Copyright (c) 2000-2013 The University of California Berkeley. All\n", "                                  rights reserved.\n", "          Copyright (c) 2006-2013 The University of Colorado Denver.  All rights\n", "                                  reserved.\n", "        \n", "          $COPYRIGHT$\n", "        \n", "          Additional copyrights may follow\n", "        \n", "          $HEADER$\n", "        \n", "          Redistribution and use in source and binary forms, with or without\n", "          modification, are permitted provided that the following conditions are\n", "          met:\n", "        \n", "          - Redistributions of source code must retain the above copyright\n", "            notice, this list of conditions and the following disclaimer.\n", "        \n", "          - Redistributions in binary form must reproduce the above copyright\n", "            notice, this list of conditions and the following disclaimer listed\n", "            in this license in the documentation and/or other materials\n", "            provided with the distribution.\n", "        \n", "          - Neither the name of the copyright holders nor the names of its\n", "            contributors may be used to endorse or promote products derived from\n", "            this software without specific prior written permission.\n", "        \n", "          The copyright holders provide no reassurances that the source code\n", "          provided does not infringe any patent, copyright, or any other\n", "          intellectual property rights of third parties.  The copyright holders\n", "          disclaim any liability to any recipient for claims brought against\n", "          recipient by any third party for infringement of that parties\n", "          intellectual property rights.\n", "        \n", "          THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n", "          \"AS IS\" AND ANY EXPRESS OR <PERSON><PERSON><PERSON>IED WARRANTIES, INCLUDING, BUT NOT\n", "          LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\n", "          A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT\n", "          OWNER OR <PERSON><PERSON><PERSON><PERSON><PERSON>ORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,\n", "          SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT\n", "          LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n", "          DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n", "          THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n", "          (INCLUDING NEG<PERSON>IGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE\n", "          OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n", "        \n", "        \n", "        Name: GCC runtime library\n", "        Files: numpy.libs/libgfortran*.so\n", "        Description: dynamically linked to files compiled with gcc\n", "        Availability: https://gcc.gnu.org/git/?p=gcc.git;a=tree;f=libgfortran\n", "        License: GPL-3.0-with-GCC-exception\n", "          Copyright (C) 2002-2017 Free Software Foundation, Inc.\n", "        \n", "          Libgfortran is free software; you can redistribute it and/or modify\n", "          it under the terms of the GNU General Public License as published by\n", "          the Free Software Foundation; either version 3, or (at your option)\n", "          any later version.\n", "        \n", "          Libgfortran is distributed in the hope that it will be useful,\n", "          but WITHOUT ANY WARRANTY; without even the implied warranty of\n", "          MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n", "          GNU General Public License for more details.\n", "        \n", "          Under Section 7 of GPL version 3, you are granted additional\n", "          permissions described in the GCC Runtime Library Exception, version\n", "          3.1, as published by the Free Software Foundation.\n", "        \n", "          You should have received a copy of the GNU General Public License and\n", "          a copy of the GCC Runtime Library Exception along with this program;\n", "          see the files COPYING3 and COPYING.RUNTIME respectively.  If not, see\n", "          <http://www.gnu.org/licenses/>.\n", "        \n", "        ----\n", "        \n", "        Full text of license texts referred to above follows (that they are\n", "        listed below does not necessarily imply the conditions apply to the\n", "        present binary release):\n", "        \n", "        ----\n", "        \n", "        GCC RUNTIME LIBRARY EXCEPTION\n", "        \n", "        Version 3.1, 31 March 2009\n", "        \n", "        Copyright (C) 2009 Free Software Foundation, Inc. <http://fsf.org/>\n", "        \n", "        Everyone is permitted to copy and distribute verbatim copies of this\n", "        license document, but changing it is not allowed.\n", "        \n", "        This GCC Runtime Library Exception (\"Exception\") is an additional\n", "        permission under section 7 of the GNU General Public License, version\n", "        3 (\"GPLv3\"). It applies to a given file (the \"Runtime Library\") that\n", "        bears a notice placed by the copyright holder of the file stating that\n", "        the file is governed by GPLv3 along with this Exception.\n", "        \n", "        When you use GCC to compile a program, GCC may combine portions of\n", "        certain GCC header files and runtime libraries with the compiled\n", "        program. The purpose of this Exception is to allow compilation of\n", "        non-GPL (including proprietary) programs to use, in this way, the\n", "        header files and runtime libraries covered by this Exception.\n", "        \n", "        0. Definitions.\n", "        \n", "        A file is an \"Independent Module\" if it either requires the Runtime\n", "        Library for execution after a Compilation Process, or makes use of an\n", "        interface provided by the Runtime Library, but is not otherwise based\n", "        on the Runtime Library.\n", "        \n", "        \"GCC\" means a version of the GNU Compiler Collection, with or without\n", "        modifications, governed by version 3 (or a specified later version) of\n", "        the GNU General Public License (GPL) with the option of using any\n", "        subsequent versions published by the FSF.\n", "        \n", "        \"GPL-compatible Software\" is software whose conditions of propagation,\n", "        modification and use would permit combination with GCC in accord with\n", "        the license of GCC.\n", "        \n", "        \"Target Code\" refers to output from any compiler for a real or virtual\n", "        target processor architecture, in executable form or suitable for\n", "        input to an assembler, loader, linker and/or execution\n", "        phase. Notwithstanding that, Target Code does not include data in any\n", "        format that is used as a compiler intermediate representation, or used\n", "        for producing a compiler intermediate representation.\n", "        \n", "        The \"Compilation Process\" transforms code entirely represented in\n", "        non-intermediate languages designed for human-written code, and/or in\n", "        Java Virtual Machine byte code, into Target Code. Thus, for example,\n", "        use of source code generators and preprocessors need not be considered\n", "        part of the Compilation Process, since the Compilation Process can be\n", "        understood as starting with the output of the generators or\n", "        preprocessors.\n", "        \n", "        A Compilation Process is \"Eligible\" if it is done using GCC, alone or\n", "        with other GPL-compatible software, or if it is done without using any\n", "        work based on GCC. For example, using non-GPL-compatible Software to\n", "        optimize any GCC intermediate representations would not qualify as an\n", "        Eligible Compilation Process.\n", "        \n", "        1. Grant of Additional Permission.\n", "        \n", "        You have permission to propagate a work of Target Code formed by\n", "        combining the Runtime Library with Independent Modules, even if such\n", "        propagation would otherwise violate the terms of GPLv3, provided that\n", "        all Target Code was generated by Eligible Compilation Processes. You\n", "        may then convey such a combination under terms of your choice,\n", "        consistent with the licensing of the Independent Modules.\n", "        \n", "        2. No Weakening of GCC Copyleft.\n", "        \n", "        The availability of this Exception does not imply any general\n", "        presumption that third-party software is unaffected by the copyleft\n", "        requirements of the license of GCC.\n", "        \n", "        ----\n", "        \n", "                            GNU GENERAL PUBLIC LICENSE\n", "                               Version 3, 29 June 2007\n", "        \n", "         Copyright (C) 2007 Free Software Foundation, Inc. <http://fsf.org/>\n", "         Everyone is permitted to copy and distribute verbatim copies\n", "         of this license document, but changing it is not allowed.\n", "        \n", "                                    Preamble\n", "        \n", "          The GNU General Public License is a free, copyleft license for\n", "        software and other kinds of works.\n", "        \n", "          The licenses for most software and other practical works are designed\n", "        to take away your freedom to share and change the works.  By contrast,\n", "        the GNU General Public License is intended to guarantee your freedom to\n", "        share and change all versions of a program--to make sure it remains free\n", "        software for all its users.  We, the Free Software Foundation, use the\n", "        GNU General Public License for most of our software; it applies also to\n", "        any other work released this way by its authors.  You can apply it to\n", "        your programs, too.\n", "        \n", "          When we speak of free software, we are referring to freedom, not\n", "        price.  Our General Public Licenses are designed to make sure that you\n", "        have the freedom to distribute copies of free software (and charge for\n", "        them if you wish), that you receive source code or can get it if you\n", "        want it, that you can change the software or use pieces of it in new\n", "        free programs, and that you know you can do these things.\n", "        \n", "          To protect your rights, we need to prevent others from denying you\n", "        these rights or asking you to surrender the rights.  Therefore, you have\n", "        certain responsibilities if you distribute copies of the software, or if\n", "        you modify it: responsibilities to respect the freedom of others.\n", "        \n", "          For example, if you distribute copies of such a program, whether\n", "        gratis or for a fee, you must pass on to the recipients the same\n", "        freedoms that you received.  You must make sure that they, too, receive\n", "        or can get the source code.  And you must show them these terms so they\n", "        know their rights.\n", "        \n", "          Developers that use the GNU GPL protect your rights with two steps:\n", "        (1) assert copyright on the software, and (2) offer you this License\n", "        giving you legal permission to copy, distribute and/or modify it.\n", "        \n", "          For the developers' and authors' protection, the GPL clearly explains\n", "        that there is no warranty for this free software.  For both users' and\n", "        authors' sake, the GPL requires that modified versions be marked as\n", "        changed, so that their problems will not be attributed erroneously to\n", "        authors of previous versions.\n", "        \n", "          Some devices are designed to deny users access to install or run\n", "        modified versions of the software inside them, although the manufacturer\n", "        can do so.  This is fundamentally incompatible with the aim of\n", "        protecting users' freedom to change the software.  The systematic\n", "        pattern of such abuse occurs in the area of products for individuals to\n", "        use, which is precisely where it is most unacceptable.  Therefore, we\n", "        have designed this version of the GPL to prohibit the practice for those\n", "        products.  If such problems arise substantially in other domains, we\n", "        stand ready to extend this provision to those domains in future versions\n", "        of the GPL, as needed to protect the freedom of users.\n", "        \n", "          Finally, every program is threatened constantly by software patents.\n", "        States should not allow patents to restrict development and use of\n", "        software on general-purpose computers, but in those that do, we wish to\n", "        avoid the special danger that patents applied to a free program could\n", "        make it effectively proprietary.  To prevent this, the GPL assures that\n", "        patents cannot be used to render the program non-free.\n", "        \n", "          The precise terms and conditions for copying, distribution and\n", "        modification follow.\n", "        \n", "                               TERMS AND CONDITIONS\n", "        \n", "          0. Definitions.\n", "        \n", "          \"This License\" refers to version 3 of the GNU General Public License.\n", "        \n", "          \"Copyright\" also means copyright-like laws that apply to other kinds of\n", "        works, such as semiconductor masks.\n", "        \n", "          \"The Program\" refers to any copyrightable work licensed under this\n", "        License.  Each licensee is addressed as \"you\".  \"Licensees\" and\n", "        \"recipients\" may be individuals or organizations.\n", "        \n", "          To \"modify\" a work means to copy from or adapt all or part of the work\n", "        in a fashion requiring copyright permission, other than the making of an\n", "        exact copy.  The resulting work is called a \"modified version\" of the\n", "        earlier work or a work \"based on\" the earlier work.\n", "        \n", "          A \"covered work\" means either the unmodified Program or a work based\n", "        on the Program.\n", "        \n", "          To \"propagate\" a work means to do anything with it that, without\n", "        permission, would make you directly or secondarily liable for\n", "        infringement under applicable copyright law, except executing it on a\n", "        computer or modifying a private copy.  Propagation includes copying,\n", "        distribution (with or without modification), making available to the\n", "        public, and in some countries other activities as well.\n", "        \n", "          To \"convey\" a work means any kind of propagation that enables other\n", "        parties to make or receive copies.  Mere interaction with a user through\n", "        a computer network, with no transfer of a copy, is not conveying.\n", "        \n", "          An interactive user interface displays \"Appropriate Legal Notices\"\n", "        to the extent that it includes a convenient and prominently visible\n", "        feature that (1) displays an appropriate copyright notice, and (2)\n", "        tells the user that there is no warranty for the work (except to the\n", "        extent that warranties are provided), that licensees may convey the\n", "        work under this License, and how to view a copy of this License.  If\n", "        the interface presents a list of user commands or options, such as a\n", "        menu, a prominent item in the list meets this criterion.\n", "        \n", "          1. Source Code.\n", "        \n", "          The \"source code\" for a work means the preferred form of the work\n", "        for making modifications to it.  \"Object code\" means any non-source\n", "        form of a work.\n", "        \n", "          A \"Standard Interface\" means an interface that either is an official\n", "        standard defined by a recognized standards body, or, in the case of\n", "        interfaces specified for a particular programming language, one that\n", "        is widely used among developers working in that language.\n", "        \n", "          The \"System Libraries\" of an executable work include anything, other\n", "        than the work as a whole, that (a) is included in the normal form of\n", "        packaging a Major Component, but which is not part of that Major\n", "        Component, and (b) serves only to enable use of the work with that\n", "        Major Component, or to implement a Standard Interface for which an\n", "        implementation is available to the public in source code form.  A\n", "        \"Major Component\", in this context, means a major essential component\n", "        (kernel, window system, and so on) of the specific operating system\n", "        (if any) on which the executable work runs, or a compiler used to\n", "        produce the work, or an object code interpreter used to run it.\n", "        \n", "          The \"Corresponding Source\" for a work in object code form means all\n", "        the source code needed to generate, install, and (for an executable\n", "        work) run the object code and to modify the work, including scripts to\n", "        control those activities.  However, it does not include the work's\n", "        System Libraries, or general-purpose tools or generally available free\n", "        programs which are used unmodified in performing those activities but\n", "        which are not part of the work.  For example, Corresponding Source\n", "        includes interface definition files associated with source files for\n", "        the work, and the source code for shared libraries and dynamically\n", "        linked subprograms that the work is specifically designed to require,\n", "        such as by intimate data communication or control flow between those\n", "        subprograms and other parts of the work.\n", "        \n", "          The Corresponding Source need not include anything that users\n", "        can regenerate automatically from other parts of the Corresponding\n", "        Source.\n", "        \n", "          The Corresponding Source for a work in source code form is that\n", "        same work.\n", "        \n", "          2. Basic Permissions.\n", "        \n", "          All rights granted under this License are granted for the term of\n", "        copyright on the Program, and are irrevocable provided the stated\n", "        conditions are met.  This License explicitly affirms your unlimited\n", "        permission to run the unmodified Program.  The output from running a\n", "        covered work is covered by this License only if the output, given its\n", "        content, constitutes a covered work.  This License acknowledges your\n", "        rights of fair use or other equivalent, as provided by copyright law.\n", "        \n", "          You may make, run and propagate covered works that you do not\n", "        convey, without conditions so long as your license otherwise remains\n", "        in force.  You may convey covered works to others for the sole purpose\n", "        of having them make modifications exclusively for you, or provide you\n", "        with facilities for running those works, provided that you comply with\n", "        the terms of this License in conveying all material for which you do\n", "        not control copyright.  Those thus making or running the covered works\n", "        for you must do so exclusively on your behalf, under your direction\n", "        and control, on terms that prohibit them from making any copies of\n", "        your copyrighted material outside their relationship with you.\n", "        \n", "          Conveying under any other circumstances is permitted solely under\n", "        the conditions stated below.  Sublicensing is not allowed; section 10\n", "        makes it unnecessary.\n", "        \n", "          3. Protecting Users' Legal Rights From Anti-Circumvention Law.\n", "        \n", "          No covered work shall be deemed part of an effective technological\n", "        measure under any applicable law fulfilling obligations under article\n", "        11 of the WIPO copyright treaty adopted on 20 December 1996, or\n", "        similar laws prohibiting or restricting circumvention of such\n", "        measures.\n", "        \n", "          When you convey a covered work, you waive any legal power to forbid\n", "        circumvention of technological measures to the extent such circumvention\n", "        is effected by exercising rights under this License with respect to\n", "        the covered work, and you disclaim any intention to limit operation or\n", "        modification of the work as a means of enforcing, against the work's\n", "        users, your or third parties' legal rights to forbid circumvention of\n", "        technological measures.\n", "        \n", "          4. Conveying Verbatim Copies.\n", "        \n", "          You may convey verbatim copies of the Program's source code as you\n", "        receive it, in any medium, provided that you conspicuously and\n", "        appropriately publish on each copy an appropriate copyright notice;\n", "        keep intact all notices stating that this License and any\n", "        non-permissive terms added in accord with section 7 apply to the code;\n", "        keep intact all notices of the absence of any warranty; and give all\n", "        recipients a copy of this License along with the Program.\n", "        \n", "          You may charge any price or no price for each copy that you convey,\n", "        and you may offer support or warranty protection for a fee.\n", "        \n", "          5. Conveying Modified Source Versions.\n", "        \n", "          You may convey a work based on the Program, or the modifications to\n", "        produce it from the Program, in the form of source code under the\n", "        terms of section 4, provided that you also meet all of these conditions:\n", "        \n", "            a) The work must carry prominent notices stating that you modified\n", "            it, and giving a relevant date.\n", "        \n", "            b) The work must carry prominent notices stating that it is\n", "            released under this License and any conditions added under section\n", "            7.  This requirement modifies the requirement in section 4 to\n", "            \"keep intact all notices\".\n", "        \n", "            c) You must license the entire work, as a whole, under this\n", "            License to anyone who comes into possession of a copy.  This\n", "            License will therefore apply, along with any applicable section 7\n", "            additional terms, to the whole of the work, and all its parts,\n", "            regardless of how they are packaged.  This License gives no\n", "            permission to license the work in any other way, but it does not\n", "            invalidate such permission if you have separately received it.\n", "        \n", "            d) If the work has interactive user interfaces, each must display\n", "            Appropriate Legal Notices; however, if the Program has interactive\n", "            interfaces that do not display Appropriate Legal Notices, your\n", "            work need not make them do so.\n", "        \n", "          A compilation of a covered work with other separate and independent\n", "        works, which are not by their nature extensions of the covered work,\n", "        and which are not combined with it such as to form a larger program,\n", "        in or on a volume of a storage or distribution medium, is called an\n", "        \"aggregate\" if the compilation and its resulting copyright are not\n", "        used to limit the access or legal rights of the compilation's users\n", "        beyond what the individual works permit.  Inclusion of a covered work\n", "        in an aggregate does not cause this License to apply to the other\n", "        parts of the aggregate.\n", "        \n", "          6. Conveying Non-Source Forms.\n", "        \n", "          You may convey a covered work in object code form under the terms\n", "        of sections 4 and 5, provided that you also convey the\n", "        machine-readable Corresponding Source under the terms of this License,\n", "        in one of these ways:\n", "        \n", "            a) Convey the object code in, or embodied in, a physical product\n", "            (including a physical distribution medium), accompanied by the\n", "            Corresponding Source fixed on a durable physical medium\n", "            customarily used for software interchange.\n", "        \n", "            b) Convey the object code in, or embodied in, a physical product\n", "            (including a physical distribution medium), accompanied by a\n", "            written offer, valid for at least three years and valid for as\n", "            long as you offer spare parts or customer support for that product\n", "            model, to give anyone who possesses the object code either (1) a\n", "            copy of the Corresponding Source for all the software in the\n", "            product that is covered by this License, on a durable physical\n", "            medium customarily used for software interchange, for a price no\n", "            more than your reasonable cost of physically performing this\n", "            conveying of source, or (2) access to copy the\n", "            Corresponding Source from a network server at no charge.\n", "        \n", "            c) Convey individual copies of the object code with a copy of the\n", "            written offer to provide the Corresponding Source.  This\n", "            alternative is allowed only occasionally and noncommercially, and\n", "            only if you received the object code with such an offer, in accord\n", "            with subsection 6b.\n", "        \n", "            d) Convey the object code by offering access from a designated\n", "            place (gratis or for a charge), and offer equivalent access to the\n", "            Corresponding Source in the same way through the same place at no\n", "            further charge.  You need not require recipients to copy the\n", "            Corresponding Source along with the object code.  If the place to\n", "            copy the object code is a network server, the Corresponding Source\n", "            may be on a different server (operated by you or a third party)\n", "            that supports equivalent copying facilities, provided you maintain\n", "            clear directions next to the object code saying where to find the\n", "            Corresponding Source.  Regardless of what server hosts the\n", "            Corresponding Source, you remain obligated to ensure that it is\n", "            available for as long as needed to satisfy these requirements.\n", "        \n", "            e) Convey the object code using peer-to-peer transmission, provided\n", "            you inform other peers where the object code and Corresponding\n", "            Source of the work are being offered to the general public at no\n", "            charge under subsection 6d.\n", "        \n", "          A separable portion of the object code, whose source code is excluded\n", "        from the Corresponding Source as a System Library, need not be\n", "        included in conveying the object code work.\n", "        \n", "          A \"User Product\" is either (1) a \"consumer product\", which means any\n", "        tangible personal property which is normally used for personal, family,\n", "        or household purposes, or (2) anything designed or sold for incorporation\n", "        into a dwelling.  In determining whether a product is a consumer product,\n", "        doubtful cases shall be resolved in favor of coverage.  For a particular\n", "        product received by a particular user, \"normally used\" refers to a\n", "        typical or common use of that class of product, regardless of the status\n", "        of the particular user or of the way in which the particular user\n", "        actually uses, or expects or is expected to use, the product.  A product\n", "        is a consumer product regardless of whether the product has substantial\n", "        commercial, industrial or non-consumer uses, unless such uses represent\n", "        the only significant mode of use of the product.\n", "        \n", "          \"Installation Information\" for a User Product means any methods,\n", "        procedures, authorization keys, or other information required to install\n", "        and execute modified versions of a covered work in that User Product from\n", "        a modified version of its Corresponding Source.  The information must\n", "        suffice to ensure that the continued functioning of the modified object\n", "        code is in no case prevented or interfered with solely because\n", "        modification has been made.\n", "        \n", "          If you convey an object code work under this section in, or with, or\n", "        specifically for use in, a User Product, and the conveying occurs as\n", "        part of a transaction in which the right of possession and use of the\n", "        User Product is transferred to the recipient in perpetuity or for a\n", "        fixed term (regardless of how the transaction is characterized), the\n", "        Corresponding Source conveyed under this section must be accompanied\n", "        by the Installation Information.  But this requirement does not apply\n", "        if neither you nor any third party retains the ability to install\n", "        modified object code on the User Product (for example, the work has\n", "        been installed in ROM).\n", "        \n", "          The requirement to provide Installation Information does not include a\n", "        requirement to continue to provide support service, warranty, or updates\n", "        for a work that has been modified or installed by the recipient, or for\n", "        the User Product in which it has been modified or installed.  Access to a\n", "        network may be denied when the modification itself materially and\n", "        adversely affects the operation of the network or violates the rules and\n", "        protocols for communication across the network.\n", "        \n", "          Corresponding Source conveyed, and Installation Information provided,\n", "        in accord with this section must be in a format that is publicly\n", "        documented (and with an implementation available to the public in\n", "        source code form), and must require no special password or key for\n", "        unpacking, reading or copying.\n", "        \n", "          7. Additional Terms.\n", "        \n", "          \"Additional permissions\" are terms that supplement the terms of this\n", "        License by making exceptions from one or more of its conditions.\n", "        Additional permissions that are applicable to the entire Program shall\n", "        be treated as though they were included in this License, to the extent\n", "        that they are valid under applicable law.  If additional permissions\n", "        apply only to part of the Program, that part may be used separately\n", "        under those permissions, but the entire Program remains governed by\n", "        this License without regard to the additional permissions.\n", "        \n", "          When you convey a copy of a covered work, you may at your option\n", "        remove any additional permissions from that copy, or from any part of\n", "        it.  (Additional permissions may be written to require their own\n", "        removal in certain cases when you modify the work.)  You may place\n", "        additional permissions on material, added by you to a covered work,\n", "        for which you have or can give appropriate copyright permission.\n", "        \n", "          Notwithstanding any other provision of this License, for material you\n", "        add to a covered work, you may (if authorized by the copyright holders of\n", "        that material) supplement the terms of this License with terms:\n", "        \n", "            a) Disclaiming warranty or limiting liability differently from the\n", "            terms of sections 15 and 16 of this License; or\n", "        \n", "            b) Requiring preservation of specified reasonable legal notices or\n", "            author attributions in that material or in the Appropriate Legal\n", "            Notices displayed by works containing it; or\n", "        \n", "            c) Prohibiting misrepresentation of the origin of that material, or\n", "            requiring that modified versions of such material be marked in\n", "            reasonable ways as different from the original version; or\n", "        \n", "            d) Limiting the use for publicity purposes of names of licensors or\n", "            authors of the material; or\n", "        \n", "            e) Declining to grant rights under trademark law for use of some\n", "            trade names, trademarks, or service marks; or\n", "        \n", "            f) Requiring indemnification of licensors and authors of that\n", "            material by anyone who conveys the material (or modified versions of\n", "            it) with contractual assumptions of liability to the recipient, for\n", "            any liability that these contractual assumptions directly impose on\n", "            those licensors and authors.\n", "        \n", "          All other non-permissive additional terms are considered \"further\n", "        restrictions\" within the meaning of section 10.  If the Program as you\n", "        received it, or any part of it, contains a notice stating that it is\n", "        governed by this License along with a term that is a further\n", "        restriction, you may remove that term.  If a license document contains\n", "        a further restriction but permits relicensing or conveying under this\n", "        License, you may add to a covered work material governed by the terms\n", "        of that license document, provided that the further restriction does\n", "        not survive such relicensing or conveying.\n", "        \n", "          If you add terms to a covered work in accord with this section, you\n", "        must place, in the relevant source files, a statement of the\n", "        additional terms that apply to those files, or a notice indicating\n", "        where to find the applicable terms.\n", "        \n", "          Additional terms, permissive or non-permissive, may be stated in the\n", "        form of a separately written license, or stated as exceptions;\n", "        the above requirements apply either way.\n", "        \n", "          8. Termination.\n", "        \n", "          You may not propagate or modify a covered work except as expressly\n", "        provided under this License.  Any attempt otherwise to propagate or\n", "        modify it is void, and will automatically terminate your rights under\n", "        this License (including any patent licenses granted under the third\n", "        paragraph of section 11).\n", "        \n", "          However, if you cease all violation of this License, then your\n", "        license from a particular copyright holder is reinstated (a)\n", "        provisionally, unless and until the copyright holder explicitly and\n", "        finally terminates your license, and (b) permanently, if the copyright\n", "        holder fails to notify you of the violation by some reasonable means\n", "        prior to 60 days after the cessation.\n", "        \n", "          Moreover, your license from a particular copyright holder is\n", "        reinstated permanently if the copyright holder notifies you of the\n", "        violation by some reasonable means, this is the first time you have\n", "        received notice of violation of this License (for any work) from that\n", "        copyright holder, and you cure the violation prior to 30 days after\n", "        your receipt of the notice.\n", "        \n", "          Termination of your rights under this section does not terminate the\n", "        licenses of parties who have received copies or rights from you under\n", "        this License.  If your rights have been terminated and not permanently\n", "        reinstated, you do not qualify to receive new licenses for the same\n", "        material under section 10.\n", "        \n", "          9. Acceptance Not Required for Having Copies.\n", "        \n", "          You are not required to accept this License in order to receive or\n", "        run a copy of the Program.  Ancillary propagation of a covered work\n", "        occurring solely as a consequence of using peer-to-peer transmission\n", "        to receive a copy likewise does not require acceptance.  However,\n", "        nothing other than this License grants you permission to propagate or\n", "        modify any covered work.  These actions infringe copyright if you do\n", "        not accept this License.  Therefore, by modifying or propagating a\n", "        covered work, you indicate your acceptance of this License to do so.\n", "        \n", "          10. Automatic Licensing of Downstream Recipients.\n", "        \n", "          Each time you convey a covered work, the recipient automatically\n", "        receives a license from the original licensors, to run, modify and\n", "        propagate that work, subject to this License.  You are not responsible\n", "        for enforcing compliance by third parties with this License.\n", "        \n", "          An \"entity transaction\" is a transaction transferring control of an\n", "        organization, or substantially all assets of one, or subdividing an\n", "        organization, or merging organizations.  If propagation of a covered\n", "        work results from an entity transaction, each party to that\n", "        transaction who receives a copy of the work also receives whatever\n", "        licenses to the work the party's predecessor in interest had or could\n", "        give under the previous paragraph, plus a right to possession of the\n", "        Corresponding Source of the work from the predecessor in interest, if\n", "        the predecessor has it or can get it with reasonable efforts.\n", "        \n", "          You may not impose any further restrictions on the exercise of the\n", "        rights granted or affirmed under this License.  For example, you may\n", "        not impose a license fee, royalty, or other charge for exercise of\n", "        rights granted under this License, and you may not initiate litigation\n", "        (including a cross-claim or counterclaim in a lawsuit) alleging that\n", "        any patent claim is infringed by making, using, selling, offering for\n", "        sale, or importing the Program or any portion of it.\n", "        \n", "          11. Patents.\n", "        \n", "          A \"contributor\" is a copyright holder who authorizes use under this\n", "        License of the Program or a work on which the Program is based.  The\n", "        work thus licensed is called the contributor's \"contributor version\".\n", "        \n", "          A contributor's \"essential patent claims\" are all patent claims\n", "        owned or controlled by the contributor, whether already acquired or\n", "        hereafter acquired, that would be infringed by some manner, permitted\n", "        by this License, of making, using, or selling its contributor version,\n", "        but do not include claims that would be infringed only as a\n", "        consequence of further modification of the contributor version.  For\n", "        purposes of this definition, \"control\" includes the right to grant\n", "        patent sublicenses in a manner consistent with the requirements of\n", "        this License.\n", "        \n", "          Each contributor grants you a non-exclusive, worldwide, royalty-free\n", "        patent license under the contributor's essential patent claims, to\n", "        make, use, sell, offer for sale, import and otherwise run, modify and\n", "        propagate the contents of its contributor version.\n", "        \n", "          In the following three paragraphs, a \"patent license\" is any express\n", "        agreement or commitment, however denominated, not to enforce a patent\n", "        (such as an express permission to practice a patent or covenant not to\n", "        sue for patent infringement).  To \"grant\" such a patent license to a\n", "        party means to make such an agreement or commitment not to enforce a\n", "        patent against the party.\n", "        \n", "          If you convey a covered work, knowingly relying on a patent license,\n", "        and the Corresponding Source of the work is not available for anyone\n", "        to copy, free of charge and under the terms of this License, through a\n", "        publicly available network server or other readily accessible means,\n", "        then you must either (1) cause the Corresponding Source to be so\n", "        available, or (2) arrange to deprive yourself of the benefit of the\n", "        patent license for this particular work, or (3) arrange, in a manner\n", "        consistent with the requirements of this License, to extend the patent\n", "        license to downstream recipients.  \"Knowingly relying\" means you have\n", "        actual knowledge that, but for the patent license, your conveying the\n", "        covered work in a country, or your recipient's use of the covered work\n", "        in a country, would infringe one or more identifiable patents in that\n", "        country that you have reason to believe are valid.\n", "        \n", "          If, pursuant to or in connection with a single transaction or\n", "        arrangement, you convey, or propagate by procuring conveyance of, a\n", "        covered work, and grant a patent license to some of the parties\n", "        receiving the covered work authorizing them to use, propagate, modify\n", "        or convey a specific copy of the covered work, then the patent license\n", "        you grant is automatically extended to all recipients of the covered\n", "        work and works based on it.\n", "        \n", "          A patent license is \"discriminatory\" if it does not include within\n", "        the scope of its coverage, prohibits the exercise of, or is\n", "        conditioned on the non-exercise of one or more of the rights that are\n", "        specifically granted under this License.  You may not convey a covered\n", "        work if you are a party to an arrangement with a third party that is\n", "        in the business of distributing software, under which you make payment\n", "        to the third party based on the extent of your activity of conveying\n", "        the work, and under which the third party grants, to any of the\n", "        parties who would receive the covered work from you, a discriminatory\n", "        patent license (a) in connection with copies of the covered work\n", "        conveyed by you (or copies made from those copies), or (b) primarily\n", "        for and in connection with specific products or compilations that\n", "        contain the covered work, unless you entered into that arrangement,\n", "        or that patent license was granted, prior to 28 March 2007.\n", "        \n", "          Nothing in this License shall be construed as excluding or limiting\n", "        any implied license or other defenses to infringement that may\n", "        otherwise be available to you under applicable patent law.\n", "        \n", "          12. No Surrender of Others' Freedom.\n", "        \n", "          If conditions are imposed on you (whether by court order, agreement or\n", "        otherwise) that contradict the conditions of this License, they do not\n", "        excuse you from the conditions of this License.  If you cannot convey a\n", "        covered work so as to satisfy simultaneously your obligations under this\n", "        License and any other pertinent obligations, then as a consequence you may\n", "        not convey it at all.  For example, if you agree to terms that obligate you\n", "        to collect a royalty for further conveying from those to whom you convey\n", "        the Program, the only way you could satisfy both those terms and this\n", "        License would be to refrain entirely from conveying the Program.\n", "        \n", "          13. Use with the GNU Affero General Public License.\n", "        \n", "          Notwithstanding any other provision of this License, you have\n", "        permission to link or combine any covered work with a work licensed\n", "        under version 3 of the GNU Affero General Public License into a single\n", "        combined work, and to convey the resulting work.  The terms of this\n", "        License will continue to apply to the part which is the covered work,\n", "        but the special requirements of the GNU Affero General Public License,\n", "        section 13, concerning interaction through a network will apply to the\n", "        combination as such.\n", "        \n", "          14. Revised Versions of this License.\n", "        \n", "          The Free Software Foundation may publish revised and/or new versions of\n", "        the GNU General Public License from time to time.  Such new versions will\n", "        be similar in spirit to the present version, but may differ in detail to\n", "        address new problems or concerns.\n", "        \n", "          Each version is given a distinguishing version number.  If the\n", "        Program specifies that a certain numbered version of the GNU General\n", "        Public License \"or any later version\" applies to it, you have the\n", "        option of following the terms and conditions either of that numbered\n", "        version or of any later version published by the Free Software\n", "        Foundation.  If the Program does not specify a version number of the\n", "        GNU General Public License, you may choose any version ever published\n", "        by the Free Software Foundation.\n", "        \n", "          If the Program specifies that a proxy can decide which future\n", "        versions of the GNU General Public License can be used, that proxy's\n", "        public statement of acceptance of a version permanently authorizes you\n", "        to choose that version for the Program.\n", "        \n", "          Later license versions may give you additional or different\n", "        permissions.  However, no additional obligations are imposed on any\n", "        author or copyright holder as a result of your choosing to follow a\n", "        later version.\n", "        \n", "          15. <PERSON><PERSON><PERSON> of Warranty.\n", "        \n", "          THERE IS NO WARRANTY FOR THE PROGRAM, TO THE EXTENT PERMITTED BY\n", "        APPLICABLE LAW.  EXCEPT WHEN OTHERWISE STATED IN WRITING THE COPYRIGHT\n", "        HOLDERS AND/OR <PERSON><PERSON><PERSON> PARTIES PROVIDE THE PROGRAM \"AS IS\" WITHOUT WARRANTY\n", "        OF ANY KIND, EITHER EXPRESSED OR <PERSON><PERSON><PERSON>IE<PERSON>, INCLUDING, BUT NOT LIMITED TO,\n", "        THE IMPLIED WARRANTIES OF MERCHANT<PERSON><PERSON><PERSON><PERSON> AND FITNESS FOR A PARTICULAR\n", "        PURPOSE.  THE ENTIRE RISK AS TO THE QUALITY AND PERFORMANCE OF THE PROGRAM\n", "        IS WITH YOU.  SHOULD THE PROGRAM PROVE DEFECTIVE, YOU ASSUME THE COST OF\n", "        ALL NECESSARY SERVICING, REPAIR OR CORRECTION.\n", "        \n", "          16. Limitation of Liability.\n", "        \n", "          IN NO EVENT UNLESS REQUIRED BY APPLICABLE LAW OR AGREED TO IN WRITING\n", "        WILL ANY COPYRIGHT HOL<PERSON><PERSON>, OR ANY OTHER PARTY WHO MODIFIES AND/OR CONVEYS\n", "        THE PROGRAM AS PERMITTED ABOVE, BE LIABLE TO YOU FOR DAMAGES, INCLUDING ANY\n", "        GENERAL, SPECIAL, INCIDENTAL OR CONSEQUENTIAL DAMAGES ARISING OUT OF THE\n", "        USE OR INABILITY TO USE THE PROGRAM (INCLUDING BUT NOT LIMITED TO LOSS OF\n", "        DATA OR DATA BEING RENDERED INACCURATE OR LOSSES SUSTAINED BY YOU OR THIRD\n", "        PARTIES OR A FAILURE OF THE PROGRAM TO OPERATE WITH ANY OTHER PROGRAMS),\n", "        EVEN IF SUCH HOLDER OR OTHER PARTY HAS BEEN ADVISED OF THE POSSIBILITY OF\n", "        SUCH DAMAGES.\n", "        \n", "          17. Interpretation of Sections 15 and 16.\n", "        \n", "          If the disclaimer of warranty and limitation of liability provided\n", "        above cannot be given local legal effect according to their terms,\n", "        reviewing courts shall apply local law that most closely approximates\n", "        an absolute waiver of all civil liability in connection with the\n", "        Program, unless a warranty or assumption of liability accompanies a\n", "        copy of the Program in return for a fee.\n", "        \n", "                             END OF TERMS AND CONDITIONS\n", "        \n", "                    How to Apply These Terms to Your New Programs\n", "        \n", "          If you develop a new program, and you want it to be of the greatest\n", "        possible use to the public, the best way to achieve this is to make it\n", "        free software which everyone can redistribute and change under these terms.\n", "        \n", "          To do so, attach the following notices to the program.  It is safest\n", "        to attach them to the start of each source file to most effectively\n", "        state the exclusion of warranty; and each file should have at least\n", "        the \"copyright\" line and a pointer to where the full notice is found.\n", "        \n", "            <one line to give the program's name and a brief idea of what it does.>\n", "            Copyright (C) <year>  <name of author>\n", "        \n", "            This program is free software: you can redistribute it and/or modify\n", "            it under the terms of the GNU General Public License as published by\n", "            the Free Software Foundation, either version 3 of the License, or\n", "            (at your option) any later version.\n", "        \n", "            This program is distributed in the hope that it will be useful,\n", "            but WITHOUT ANY WARRANTY; without even the implied warranty of\n", "            MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n", "            GNU General Public License for more details.\n", "        \n", "            You should have received a copy of the GNU General Public License\n", "            along with this program.  If not, see <http://www.gnu.org/licenses/>.\n", "        \n", "        Also add information on how to contact you by electronic and paper mail.\n", "        \n", "          If the program does terminal interaction, make it output a short\n", "        notice like this when it starts in an interactive mode:\n", "        \n", "            <program>  Copyright (C) <year>  <name of author>\n", "            This program comes with ABSOLUTELY NO WARRANTY; for details type `show w'.\n", "            This is free software, and you are welcome to redistribute it\n", "            under certain conditions; type `show c' for details.\n", "        \n", "        The hypothetical commands `show w' and `show c' should show the appropriate\n", "        parts of the General Public License.  Of course, your program's commands\n", "        might be different; for a GUI interface, you would use an \"about box\".\n", "        \n", "          You should also get your employer (if you work as a programmer) or school,\n", "        if any, to sign a \"copyright disclaimer\" for the program, if necessary.\n", "        For more information on this, and how to apply and follow the GNU GPL, see\n", "        <http://www.gnu.org/licenses/>.\n", "        \n", "          The GNU General Public License does not permit incorporating your program\n", "        into proprietary programs.  If your program is a subroutine library, you\n", "        may consider it more useful to permit linking proprietary applications with\n", "        the library.  If this is what you want to do, use the GNU Lesser General\n", "        Public License instead of this License.  But first, please read\n", "        <http://www.gnu.org/philosophy/why-not-lgpl.html>.\n", "        \n", "        Name: lib<PERSON><PERSON><PERSON><PERSON>\n", "        Files: numpy.libs/libquadmath*.so\n", "        Description: dynamically linked to files compiled with gcc\n", "        Availability: https://gcc.gnu.org/git/?p=gcc.git;a=tree;f=libquadmath\n", "        License: LGPL-2.1-or-later\n", "        \n", "            GCC Quad-Precision Math Library\n", "            Copyright (C) 2010-2019 Free Software Foundation, Inc.\n", "            Written by <PERSON><PERSON><PERSON><PERSON>  <<EMAIL>>\n", "        \n", "            This file is part of the libquadmath library.\n", "            Libquadmath is free software; you can redistribute it and/or\n", "            modify it under the terms of the GNU Library General Public\n", "            License as published by the Free Software Foundation; either\n", "            version 2.1 of the License, or (at your option) any later version.\n", "        \n", "            Libquadmath is distributed in the hope that it will be useful,\n", "            but WITHOUT ANY WARRANTY; without even the implied warranty of\n", "            MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU\n", "            Lesser General Public License for more details.\n", "            https://www.gnu.org/licenses/old-licenses/lgpl-2.1.html\n", "Location: /root/miniconda3/envs/langchain/lib/python3.10/site-packages\n", "Requires: \n", "Required-by: chroma-hnswlib, chromadb, contourpy, docarray, faiss-cpu, gradio, langchain, langchain-community, matplotlib, onnxruntime, pandas, scikit-learn, scipy, unstructured\n"]}], "source": ["!pip show tiktoken openai pandas matp<PERSON><PERSON>b plotly scikit-learn numpy"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 生成 Embedding (基于 text-embedding-ada-002 模型)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["嵌入对于处理自然语言和代码非常有用，因为其他机器学习模型和算法（如聚类或搜索）可以轻松地使用和比较它们。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![Embedding](images/embedding-vectors.svg)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### 亚马逊美食评论数据集(amazon-fine-food-reviews)\n", "\n", "Source:[美食评论数据集](https://www.kaggle.com/snap/amazon-fine-food-reviews)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![dataset](images/amazon-fine-food-reviews.png)\n", "\n", "\n", "该数据集包含截至2012年10月用户在亚马逊上留下的共计568,454条美食评论。为了说明目的，我们将使用该数据集的一个子集，其中包括最近1,000条评论。这些评论都是用英语撰写的，并且倾向于积极或消极。每个评论都有一个产品ID、用户ID、评分、标题（摘要）和正文。\n", "\n", "我们将把评论摘要和正文合并成一个单一的组合文本。模型将对这个组合文本进行编码，并输出一个单一的向量嵌入。"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# 导入 pandas 包。Pandas 是一个用于数据处理和分析的 Python 库\n", "# 提供了 DataFrame 数据结构，方便进行数据的读取、处理、分析等操作。\n", "import pandas as pd\n", "# 导入 tiktoken 库。Tiktoken 是 OpenAI 开发的一个库，用于从模型生成的文本中计算 token 数量。\n", "import tiktoken"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["#### 加载数据集"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Time</th>\n", "      <th>ProductId</th>\n", "      <th>UserId</th>\n", "      <th>Score</th>\n", "      <th>Summary</th>\n", "      <th>Text</th>\n", "      <th>combined</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1351123200</td>\n", "      <td>B003XPF9BO</td>\n", "      <td>A3R7JR3FMEBXQB</td>\n", "      <td>5</td>\n", "      <td>where does one  start...and stop... with a tre...</td>\n", "      <td>Wanted to save some to bring to my Chicago fam...</td>\n", "      <td>Title: where does one  start...and stop... wit...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1351123200</td>\n", "      <td>B003JK537S</td>\n", "      <td>A3JBPC3WFUT5ZP</td>\n", "      <td>1</td>\n", "      <td>Arrived in pieces</td>\n", "      <td>Not pleased at all. When I opened the box, mos...</td>\n", "      <td>Title: Arrived in pieces; Content: Not pleased...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         Time   ProductId          UserId  Score  \\\n", "0  1351123200  B003XPF9BO  A3R7JR3FMEBXQB      5   \n", "1  1351123200  B003JK537S  A3JBPC3WFUT5ZP      1   \n", "\n", "                                             Summary  \\\n", "0  where does one  start...and stop... with a tre...   \n", "1                                  Arrived in pieces   \n", "\n", "                                                Text  \\\n", "0  Wanted to save some to bring to my Chicago fam...   \n", "1  Not pleased at all. When I opened the box, mos...   \n", "\n", "                                            combined  \n", "0  Title: where does one  start...and stop... wit...  \n", "1  Title: Arrived in pieces; Content: Not pleased...  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["input_datapath = \"data/fine_food_reviews_1k.csv\"\n", "df = pd.read_csv(input_datapath, index_col=0)\n", "df = df[[\"Time\", \"ProductId\", \"UserId\", \"Score\", \"Summary\", \"Text\"]]\n", "df = df.dropna()\n", "\n", "# 将 \"Summary\" 和 \"Text\" 字段组合成新的字段 \"combined\"\n", "df[\"combined\"] = (\n", "    \"Title: \" + df.Summary.str.strip() + \"; Content: \" + df.Text.str.strip()\n", ")\n", "df.head(2)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["0      Title: where does one  start...and stop... wit...\n", "1      Title: Arrived in pieces; Content: Not pleased...\n", "2      Title: It isn't blanc mange, but isn't bad . ....\n", "3      Title: These also have SALT and it's not sea s...\n", "4      Title: Happy with the product; Content: My dog...\n", "                             ...                        \n", "995    Title: Delicious!; Content: I have ordered the...\n", "996    Title: Good Training Treat; Content: My dog wi...\n", "997    Title: <PERSON>ica <PERSON> Coffee; Content: <PERSON><PERSON>...\n", "998    Title: Party Peanuts; Content: Great product f...\n", "999    Title: I love Maui Coffee!; Content: My first ...\n", "Name: combined, Length: 1000, dtype: object"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"combined\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Embedding 模型关键参数"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# 模型类型\n", "# 建议使用官方推荐的第二代嵌入模型：text-embedding-ada-002\n", "embedding_model = \"text-embedding-ada-002\"\n", "# text-embedding-ada-002 模型对应的分词器（TOKENIZER）\n", "embedding_encoding = \"cl100k_base\"\n", "# text-embedding-ada-002 模型支持的输入最大 Token 数是8191，向量维度 1536\n", "# 在我们的 DEMO 中过滤 Token 超过 8000 的文本\n", "max_tokens = 8000  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 将样本减少到最近的1,000个评论，并删除过长的样本\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["1000"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# 设置要筛选的评论数量为1000\n", "top_n = 1000\n", "# 对DataFrame进行排序，基于\"Time\"列，然后选取最后的2000条评论。\n", "# 这个假设是，我们认为最近的评论可能更相关，因此我们将对它们进行初始筛选。\n", "df = df.sort_values(\"Time\").tail(top_n * 2) \n", "# 丢弃\"Time\"列，因为我们在这个分析中不再需要它。\n", "df.drop(\"Time\", axis=1, inplace=True)\n", "# 从'embedding_encoding'获取编码\n", "encoding = tiktoken.get_encoding(embedding_encoding)\n", "\n", "# 计算每条评论的token数量。我们通过使用encoding.encode方法获取每条评论的token数，然后把结果存储在新的'n_tokens'列中。\n", "df[\"n_tokens\"] = df.combined.apply(lambda x: len(encoding.encode(x)))\n", "\n", "# 如果评论的token数量超过最大允许的token数量，我们将忽略（删除）该评论。\n", "# 我们使用.tail方法获取token数量在允许范围内的最后top_n（1000）条评论。\n", "df = df[df.n_tokens <= max_tokens].tail(top_n)\n", "\n", "# 打印出剩余评论的数量。\n", "len(df)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["#### 生成 Embeddings 并保存\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["from openai import OpenAI"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# OpenAI Python SDK v1.0 更新后的使用方式\n", "client = OpenAI(  api_key=\"***************************************************\",base_url=\"https://api.openai.com\")\n", "\n", "# 使用OpenAI代理方式，需要修改base_url\n", "# client = OpenAI(\n", "#     api_key=\"XXX\", # 你的KEY\n", "#     base_url=\"https://vip.apiyi.com/v1\"\n", "# )\n", "\n", "# 替换为国内大模型 API 地址\n", "# # 智谱API调用(glm-4v-flash)\n", "# openai = OpenAI(\n", "#     api_key=\"XXX\",\n", "#     base_url=\"https://open.bigmodel.cn/api/paas/v4/\"\n", "# )\n", "# DeepSeek API调用（deepseek-chat / deepseek-reasoner）\n", "# DeepSeek Completions接口 用户需要设置 base_url=\"https://api.deepseek.com/beta\" 来使用此功能\n", "# openai = OpenAI(\n", "#     api_key=\"XXX\",\n", "#     base_url=\"https://api.deepseek.com\"\n", "# )\n", "\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"scrolled": true}, "outputs": [{"ename": "APIConnectionError", "evalue": "Connection error.", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mConnectError\u001b[39m                              <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m/opt/anaconda3/envs/pytorch/lib/python3.11/site-packages/httpx/_transports/default.py:101\u001b[39m, in \u001b[36mmap_httpcore_exceptions\u001b[39m\u001b[34m()\u001b[39m\n\u001b[32m    100\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m101\u001b[39m     \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m\n\u001b[32m    102\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/anaconda3/envs/pytorch/lib/python3.11/site-packages/httpx/_transports/default.py:250\u001b[39m, in \u001b[36mHTTPTransport.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    249\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m map_httpcore_exceptions():\n\u001b[32m--> \u001b[39m\u001b[32m250\u001b[39m     resp = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_pool\u001b[49m\u001b[43m.\u001b[49m\u001b[43mhandle_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mreq\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    252\u001b[39m \u001b[38;5;28;01<PERSON>ert\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(resp.stream, typing.Iterable)\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/anaconda3/envs/pytorch/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py:256\u001b[39m, in \u001b[36mConnectionPool.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    255\u001b[39m     \u001b[38;5;28mself\u001b[39m._close_connections(closing)\n\u001b[32m--> \u001b[39m\u001b[32m256\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m exc \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m    258\u001b[39m \u001b[38;5;66;03m# Return the response. Note that in this case we still have to manage\u001b[39;00m\n\u001b[32m    259\u001b[39m \u001b[38;5;66;03m# the point at which the response is closed.\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/anaconda3/envs/pytorch/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py:236\u001b[39m, in \u001b[36mConnectionPool.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    234\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m    235\u001b[39m     \u001b[38;5;66;03m# Send the request on the assigned connection.\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m236\u001b[39m     response = \u001b[43mconnection\u001b[49m\u001b[43m.\u001b[49m\u001b[43mhandle_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    237\u001b[39m \u001b[43m        \u001b[49m\u001b[43mpool_request\u001b[49m\u001b[43m.\u001b[49m\u001b[43mrequest\u001b[49m\n\u001b[32m    238\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    239\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m ConnectionNotAvailable:\n\u001b[32m    240\u001b[39m     \u001b[38;5;66;03m# In some cases a connection may initially be available to\u001b[39;00m\n\u001b[32m    241\u001b[39m     \u001b[38;5;66;03m# handle a request, but then become unavailable.\u001b[39;00m\n\u001b[32m    242\u001b[39m     \u001b[38;5;66;03m#\u001b[39;00m\n\u001b[32m    243\u001b[39m     \u001b[38;5;66;03m# In this case we clear the connection and try again.\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/anaconda3/envs/pytorch/lib/python3.11/site-packages/httpcore/_sync/http_proxy.py:316\u001b[39m, in \u001b[36mTunnelHTTPConnection.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    315\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m Trace(\u001b[33m\"\u001b[39m\u001b[33mstart_tls\u001b[39m\u001b[33m\"\u001b[39m, logger, request, kwargs) \u001b[38;5;28;01mas\u001b[39;00m trace:\n\u001b[32m--> \u001b[39m\u001b[32m316\u001b[39m     stream = \u001b[43mstream\u001b[49m\u001b[43m.\u001b[49m\u001b[43mstart_tls\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    317\u001b[39m     trace.return_value = stream\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/anaconda3/envs/pytorch/lib/python3.11/site-packages/httpcore/_sync/http11.py:376\u001b[39m, in \u001b[36mHTTP11UpgradeStream.start_tls\u001b[39m\u001b[34m(self, ssl_context, server_hostname, timeout)\u001b[39m\n\u001b[32m    370\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mstart_tls\u001b[39m(\n\u001b[32m    371\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m    372\u001b[39m     ssl_context: ssl.SSLContext,\n\u001b[32m    373\u001b[39m     server_hostname: \u001b[38;5;28mstr\u001b[39m | \u001b[38;5;28;01mNone\u001b[39;00m = \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[32m    374\u001b[39m     timeout: \u001b[38;5;28mfloat\u001b[39m | \u001b[38;5;28;01mNone\u001b[39;00m = \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[32m    375\u001b[39m ) -> NetworkStream:\n\u001b[32m--> \u001b[39m\u001b[32m376\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_stream\u001b[49m\u001b[43m.\u001b[49m\u001b[43mstart_tls\u001b[49m\u001b[43m(\u001b[49m\u001b[43mssl_context\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mserver_hostname\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/anaconda3/envs/pytorch/lib/python3.11/site-packages/httpcore/_backends/sync.py:154\u001b[39m, in \u001b[36mSyncStream.start_tls\u001b[39m\u001b[34m(self, ssl_context, server_hostname, timeout)\u001b[39m\n\u001b[32m    150\u001b[39m exc_map: ExceptionMapping = {\n\u001b[32m    151\u001b[39m     socket.timeout: ConnectTimeout,\n\u001b[32m    152\u001b[39m     \u001b[38;5;167;01mOSError\u001b[39;00m: ConnectError,\n\u001b[32m    153\u001b[39m }\n\u001b[32m--> \u001b[39m\u001b[32m154\u001b[39m \u001b[43m\u001b[49m\u001b[38;5;28;43;01mwith\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mmap_exceptions\u001b[49m\u001b[43m(\u001b[49m\u001b[43mexc_map\u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m    155\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mtry\u001b[39;49;00m\u001b[43m:\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/anaconda3/envs/pytorch/lib/python3.11/contextlib.py:158\u001b[39m, in \u001b[36m_GeneratorContextManager.__exit__\u001b[39m\u001b[34m(self, typ, value, traceback)\u001b[39m\n\u001b[32m    157\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m158\u001b[39m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mgen\u001b[49m\u001b[43m.\u001b[49m\u001b[43mthrow\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtyp\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mvalue\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtraceback\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    159\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mStopIteration\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[32m    160\u001b[39m     \u001b[38;5;66;03m# Suppress StopIteration *unless* it's the same exception that\u001b[39;00m\n\u001b[32m    161\u001b[39m     \u001b[38;5;66;03m# was passed to throw().  This prevents a StopIteration\u001b[39;00m\n\u001b[32m    162\u001b[39m     \u001b[38;5;66;03m# raised inside the \"with\" statement from being suppressed.\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/anaconda3/envs/pytorch/lib/python3.11/site-packages/httpcore/_exceptions.py:14\u001b[39m, in \u001b[36mmap_exceptions\u001b[39m\u001b[34m(map)\u001b[39m\n\u001b[32m     13\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(exc, from_exc):\n\u001b[32m---> \u001b[39m\u001b[32m14\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m to_exc(exc) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mexc\u001b[39;00m\n\u001b[32m     15\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m\n", "\u001b[31mConnectError\u001b[39m: [Errno 54] Connection reset by peer", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[31mConnectError\u001b[39m                              <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m/opt/anaconda3/envs/pytorch/lib/python3.11/site-packages/openai/_base_client.py:996\u001b[39m, in \u001b[36mSyncAPIClient._request\u001b[39m\u001b[34m(self, cast_to, options, retries_taken, stream, stream_cls)\u001b[39m\n\u001b[32m    995\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m996\u001b[39m     response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_client\u001b[49m\u001b[43m.\u001b[49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    997\u001b[39m \u001b[43m        \u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    998\u001b[39m \u001b[43m        \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_should_stream_response_body\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m=\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    999\u001b[39m \u001b[43m        \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1000\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1001\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m httpx.TimeoutException \u001b[38;5;28;01mas\u001b[39;00m err:\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/anaconda3/envs/pytorch/lib/python3.11/site-packages/httpx/_client.py:914\u001b[39m, in \u001b[36mClient.send\u001b[39m\u001b[34m(self, request, stream, auth, follow_redirects)\u001b[39m\n\u001b[32m    912\u001b[39m auth = \u001b[38;5;28mself\u001b[39m._build_request_auth(request, auth)\n\u001b[32m--> \u001b[39m\u001b[32m914\u001b[39m response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_send_handling_auth\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    915\u001b[39m \u001b[43m    \u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    916\u001b[39m \u001b[43m    \u001b[49m\u001b[43mauth\u001b[49m\u001b[43m=\u001b[49m\u001b[43mauth\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    917\u001b[39m \u001b[43m    \u001b[49m\u001b[43mfollow_redirects\u001b[49m\u001b[43m=\u001b[49m\u001b[43mfollow_redirects\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    918\u001b[39m \u001b[43m    \u001b[49m\u001b[43mhistory\u001b[49m\u001b[43m=\u001b[49m\u001b[43m[\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    919\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    920\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/anaconda3/envs/pytorch/lib/python3.11/site-packages/httpx/_client.py:942\u001b[39m, in \u001b[36mClient._send_handling_auth\u001b[39m\u001b[34m(self, request, auth, follow_redirects, history)\u001b[39m\n\u001b[32m    941\u001b[39m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m942\u001b[39m     response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_send_handling_redirects\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    943\u001b[39m \u001b[43m        \u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    944\u001b[39m \u001b[43m        \u001b[49m\u001b[43mfollow_redirects\u001b[49m\u001b[43m=\u001b[49m\u001b[43mfollow_redirects\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    945\u001b[39m \u001b[43m        \u001b[49m\u001b[43mhistory\u001b[49m\u001b[43m=\u001b[49m\u001b[43mhistory\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    946\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    947\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/anaconda3/envs/pytorch/lib/python3.11/site-packages/httpx/_client.py:979\u001b[39m, in \u001b[36mClient._send_handling_redirects\u001b[39m\u001b[34m(self, request, follow_redirects, history)\u001b[39m\n\u001b[32m    977\u001b[39m     hook(request)\n\u001b[32m--> \u001b[39m\u001b[32m979\u001b[39m response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_send_single_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    980\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/anaconda3/envs/pytorch/lib/python3.11/site-packages/httpx/_client.py:1014\u001b[39m, in \u001b[36mClient._send_single_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m   1013\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m request_context(request=request):\n\u001b[32m-> \u001b[39m\u001b[32m1014\u001b[39m     response = \u001b[43mtransport\u001b[49m\u001b[43m.\u001b[49m\u001b[43mhandle_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1016\u001b[39m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(response.stream, SyncByteStream)\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/anaconda3/envs/pytorch/lib/python3.11/site-packages/httpx/_transports/default.py:249\u001b[39m, in \u001b[36mHTTPTransport.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    237\u001b[39m req = httpcore.Request(\n\u001b[32m    238\u001b[39m     method=request.method,\n\u001b[32m    239\u001b[39m     url=httpcore.URL(\n\u001b[32m   (...)\u001b[39m\u001b[32m    247\u001b[39m     extensions=request.extensions,\n\u001b[32m    248\u001b[39m )\n\u001b[32m--> \u001b[39m\u001b[32m249\u001b[39m \u001b[43m\u001b[49m\u001b[38;5;28;43;01mwith\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mmap_httpcore_exceptions\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m    250\u001b[39m \u001b[43m    \u001b[49m\u001b[43mresp\u001b[49m\u001b[43m \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_pool\u001b[49m\u001b[43m.\u001b[49m\u001b[43mhandle_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mreq\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/anaconda3/envs/pytorch/lib/python3.11/contextlib.py:158\u001b[39m, in \u001b[36m_GeneratorContextManager.__exit__\u001b[39m\u001b[34m(self, typ, value, traceback)\u001b[39m\n\u001b[32m    157\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m158\u001b[39m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mgen\u001b[49m\u001b[43m.\u001b[49m\u001b[43mthrow\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtyp\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mvalue\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtraceback\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    159\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mStopIteration\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[32m    160\u001b[39m     \u001b[38;5;66;03m# Suppress StopIteration *unless* it's the same exception that\u001b[39;00m\n\u001b[32m    161\u001b[39m     \u001b[38;5;66;03m# was passed to throw().  This prevents a StopIteration\u001b[39;00m\n\u001b[32m    162\u001b[39m     \u001b[38;5;66;03m# raised inside the \"with\" statement from being suppressed.\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/anaconda3/envs/pytorch/lib/python3.11/site-packages/httpx/_transports/default.py:118\u001b[39m, in \u001b[36mmap_httpcore_exceptions\u001b[39m\u001b[34m()\u001b[39m\n\u001b[32m    117\u001b[39m message = \u001b[38;5;28mstr\u001b[39m(exc)\n\u001b[32m--> \u001b[39m\u001b[32m118\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m mapped_exc(message) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mexc\u001b[39;00m\n", "\u001b[31mConnectError\u001b[39m: [Errno 54] Connection reset by peer", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[31mAPIConnectionError\u001b[39m                        Traceback (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[11]\u001b[39m\u001b[32m, line 3\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# 新版本创建 Embedding 向量的方法\u001b[39;00m\n\u001b[32m      2\u001b[39m \u001b[38;5;66;03m# Ref：https://community.openai.com/t/embeddings-api-documentation-needs-to-updated/475663\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m3\u001b[39m res = \u001b[43mclient\u001b[49m\u001b[43m.\u001b[49m\u001b[43membeddings\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcreate\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m=\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mabc\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m=\u001b[49m\u001b[43membedding_model\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m      4\u001b[39m \u001b[38;5;28mprint\u001b[39m(res.data[\u001b[32m0\u001b[39m].embedding)\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/anaconda3/envs/pytorch/lib/python3.11/site-packages/openai/resources/embeddings.py:125\u001b[39m, in \u001b[36mEmbeddings.create\u001b[39m\u001b[34m(self, input, model, dimensions, encoding_format, user, extra_headers, extra_query, extra_body, timeout)\u001b[39m\n\u001b[32m    119\u001b[39m         embedding.embedding = np.frombuffer(  \u001b[38;5;66;03m# type: ignore[no-untyped-call]\u001b[39;00m\n\u001b[32m    120\u001b[39m             base64.b64decode(data), dtype=\u001b[33m\"\u001b[39m\u001b[33mfloat32\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    121\u001b[39m         ).tolist()\n\u001b[32m    123\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m obj\n\u001b[32m--> \u001b[39m\u001b[32m125\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_post\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    126\u001b[39m \u001b[43m    \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m/embeddings\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m    127\u001b[39m \u001b[43m    \u001b[49m\u001b[43mbody\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmaybe_transform\u001b[49m\u001b[43m(\u001b[49m\u001b[43mparams\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43membedding_create_params\u001b[49m\u001b[43m.\u001b[49m\u001b[43mEmbeddingCreateParams\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    128\u001b[39m \u001b[43m    \u001b[49m\u001b[43moptions\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmake_request_options\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    129\u001b[39m \u001b[43m        \u001b[49m\u001b[43mextra_headers\u001b[49m\u001b[43m=\u001b[49m\u001b[43mextra_headers\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    130\u001b[39m \u001b[43m        \u001b[49m\u001b[43mextra_query\u001b[49m\u001b[43m=\u001b[49m\u001b[43mextra_query\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    131\u001b[39m \u001b[43m        \u001b[49m\u001b[43mextra_body\u001b[49m\u001b[43m=\u001b[49m\u001b[43mextra_body\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    132\u001b[39m \u001b[43m        \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    133\u001b[39m \u001b[43m        \u001b[49m\u001b[43mpost_parser\u001b[49m\u001b[43m=\u001b[49m\u001b[43mparser\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    134\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    135\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m=\u001b[49m\u001b[43mCreateEmbeddingResponse\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    136\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/anaconda3/envs/pytorch/lib/python3.11/site-packages/openai/_base_client.py:1283\u001b[39m, in \u001b[36mSyncAPIClient.post\u001b[39m\u001b[34m(self, path, cast_to, body, options, files, stream, stream_cls)\u001b[39m\n\u001b[32m   1269\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mpost\u001b[39m(\n\u001b[32m   1270\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   1271\u001b[39m     path: \u001b[38;5;28mstr\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m   1278\u001b[39m     stream_cls: \u001b[38;5;28mtype\u001b[39m[_StreamT] | \u001b[38;5;28;01mNone\u001b[39;00m = \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[32m   1279\u001b[39m ) -> ResponseT | _StreamT:\n\u001b[32m   1280\u001b[39m     opts = FinalRequestOptions.construct(\n\u001b[32m   1281\u001b[39m         method=\u001b[33m\"\u001b[39m\u001b[33mpost\u001b[39m\u001b[33m\"\u001b[39m, url=path, json_data=body, files=to_httpx_files(files), **options\n\u001b[32m   1282\u001b[39m     )\n\u001b[32m-> \u001b[39m\u001b[32m1283\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m cast(ResponseT, \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mopts\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m)\u001b[49m)\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/anaconda3/envs/pytorch/lib/python3.11/site-packages/openai/_base_client.py:960\u001b[39m, in \u001b[36mSyncAPIClient.request\u001b[39m\u001b[34m(self, cast_to, options, remaining_retries, stream, stream_cls)\u001b[39m\n\u001b[32m    957\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    958\u001b[39m     retries_taken = \u001b[32m0\u001b[39m\n\u001b[32m--> \u001b[39m\u001b[32m960\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    961\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    962\u001b[39m \u001b[43m    \u001b[49m\u001b[43moptions\u001b[49m\u001b[43m=\u001b[49m\u001b[43moptions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    963\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    964\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    965\u001b[39m \u001b[43m    \u001b[49m\u001b[43mretries_taken\u001b[49m\u001b[43m=\u001b[49m\u001b[43mretries_taken\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    966\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/anaconda3/envs/pytorch/lib/python3.11/site-packages/openai/_base_client.py:1020\u001b[39m, in \u001b[36mSyncAPIClient._request\u001b[39m\u001b[34m(self, cast_to, options, retries_taken, stream, stream_cls)\u001b[39m\n\u001b[32m   1017\u001b[39m log.debug(\u001b[33m\"\u001b[39m\u001b[33mEncountered Exception\u001b[39m\u001b[33m\"\u001b[39m, exc_info=\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[32m   1019\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m remaining_retries > \u001b[32m0\u001b[39m:\n\u001b[32m-> \u001b[39m\u001b[32m1020\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_retry_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   1021\u001b[39m \u001b[43m        \u001b[49m\u001b[43minput_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1022\u001b[39m \u001b[43m        \u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1023\u001b[39m \u001b[43m        \u001b[49m\u001b[43mretries_taken\u001b[49m\u001b[43m=\u001b[49m\u001b[43mretries_taken\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1024\u001b[39m \u001b[43m        \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1025\u001b[39m \u001b[43m        \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1026\u001b[39m \u001b[43m        \u001b[49m\u001b[43mresponse_headers\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m   1027\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1029\u001b[39m log.debug(\u001b[33m\"\u001b[39m\u001b[33mRaising connection error\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m   1030\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m APIConnectionError(request=request) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01merr\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/anaconda3/envs/pytorch/lib/python3.11/site-packages/openai/_base_client.py:1098\u001b[39m, in \u001b[36mSyncAPIClient._retry_request\u001b[39m\u001b[34m(self, options, cast_to, retries_taken, response_headers, stream, stream_cls)\u001b[39m\n\u001b[32m   1094\u001b[39m \u001b[38;5;66;03m# In a synchronous context we are blocking the entire thread. Up to the library user to run the client in a\u001b[39;00m\n\u001b[32m   1095\u001b[39m \u001b[38;5;66;03m# different thread if necessary.\u001b[39;00m\n\u001b[32m   1096\u001b[39m time.sleep(timeout)\n\u001b[32m-> \u001b[39m\u001b[32m1098\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   1099\u001b[39m \u001b[43m    \u001b[49m\u001b[43moptions\u001b[49m\u001b[43m=\u001b[49m\u001b[43moptions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1100\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1101\u001b[39m \u001b[43m    \u001b[49m\u001b[43mretries_taken\u001b[49m\u001b[43m=\u001b[49m\u001b[43mretries_taken\u001b[49m\u001b[43m \u001b[49m\u001b[43m+\u001b[49m\u001b[43m \u001b[49m\u001b[32;43m1\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m   1102\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1103\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1104\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/anaconda3/envs/pytorch/lib/python3.11/site-packages/openai/_base_client.py:1020\u001b[39m, in \u001b[36mSyncAPIClient._request\u001b[39m\u001b[34m(self, cast_to, options, retries_taken, stream, stream_cls)\u001b[39m\n\u001b[32m   1017\u001b[39m log.debug(\u001b[33m\"\u001b[39m\u001b[33mEncountered Exception\u001b[39m\u001b[33m\"\u001b[39m, exc_info=\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[32m   1019\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m remaining_retries > \u001b[32m0\u001b[39m:\n\u001b[32m-> \u001b[39m\u001b[32m1020\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_retry_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   1021\u001b[39m \u001b[43m        \u001b[49m\u001b[43minput_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1022\u001b[39m \u001b[43m        \u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1023\u001b[39m \u001b[43m        \u001b[49m\u001b[43mretries_taken\u001b[49m\u001b[43m=\u001b[49m\u001b[43mretries_taken\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1024\u001b[39m \u001b[43m        \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1025\u001b[39m \u001b[43m        \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1026\u001b[39m \u001b[43m        \u001b[49m\u001b[43mresponse_headers\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m   1027\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1029\u001b[39m log.debug(\u001b[33m\"\u001b[39m\u001b[33mRaising connection error\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m   1030\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m APIConnectionError(request=request) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01merr\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/anaconda3/envs/pytorch/lib/python3.11/site-packages/openai/_base_client.py:1098\u001b[39m, in \u001b[36mSyncAPIClient._retry_request\u001b[39m\u001b[34m(self, options, cast_to, retries_taken, response_headers, stream, stream_cls)\u001b[39m\n\u001b[32m   1094\u001b[39m \u001b[38;5;66;03m# In a synchronous context we are blocking the entire thread. Up to the library user to run the client in a\u001b[39;00m\n\u001b[32m   1095\u001b[39m \u001b[38;5;66;03m# different thread if necessary.\u001b[39;00m\n\u001b[32m   1096\u001b[39m time.sleep(timeout)\n\u001b[32m-> \u001b[39m\u001b[32m1098\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   1099\u001b[39m \u001b[43m    \u001b[49m\u001b[43moptions\u001b[49m\u001b[43m=\u001b[49m\u001b[43moptions\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1100\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcast_to\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1101\u001b[39m \u001b[43m    \u001b[49m\u001b[43mretries_taken\u001b[49m\u001b[43m=\u001b[49m\u001b[43mretries_taken\u001b[49m\u001b[43m \u001b[49m\u001b[43m+\u001b[49m\u001b[43m \u001b[49m\u001b[32;43m1\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m   1102\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstream\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1103\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstream_cls\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1104\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/anaconda3/envs/pytorch/lib/python3.11/site-packages/openai/_base_client.py:1030\u001b[39m, in \u001b[36mSyncAPIClient._request\u001b[39m\u001b[34m(self, cast_to, options, retries_taken, stream, stream_cls)\u001b[39m\n\u001b[32m   1020\u001b[39m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._retry_request(\n\u001b[32m   1021\u001b[39m             input_options,\n\u001b[32m   1022\u001b[39m             cast_to,\n\u001b[32m   (...)\u001b[39m\u001b[32m   1026\u001b[39m             response_headers=\u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[32m   1027\u001b[39m         )\n\u001b[32m   1029\u001b[39m     log.debug(\u001b[33m\"\u001b[39m\u001b[33mRaising connection error\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m-> \u001b[39m\u001b[32m1030\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m APIConnectionError(request=request) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01merr\u001b[39;00m\n\u001b[32m   1032\u001b[39m log.debug(\n\u001b[32m   1033\u001b[39m     \u001b[33m'\u001b[39m\u001b[33mHTTP Response: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m \u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;132;01m%i\u001b[39;00m\u001b[33m \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m\"\u001b[39m\u001b[33m \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m'\u001b[39m,\n\u001b[32m   1034\u001b[39m     request.method,\n\u001b[32m   (...)\u001b[39m\u001b[32m   1038\u001b[39m     response.headers,\n\u001b[32m   1039\u001b[39m )\n\u001b[32m   1040\u001b[39m log.debug(\u001b[33m\"\u001b[39m\u001b[33mrequest_id: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m\"\u001b[39m, response.headers.get(\u001b[33m\"\u001b[39m\u001b[33mx-request-id\u001b[39m\u001b[33m\"\u001b[39m))\n", "\u001b[31mAPIConnectionError\u001b[39m: Connection error."]}], "source": ["# 新版本创建 Embedding 向量的方法\n", "# Ref：https://community.openai.com/t/embeddings-api-documentation-needs-to-updated/475663\n", "res = client.embeddings.create(input=\"abc\", model=embedding_model)\n", "print(res.data[0].embedding)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 使用新方法调用 OpenAI Embedding API\n", "def embedding_text(text, model=\"text-embedding-ada-002\"):\n", "    res = client.embeddings.create(input=text, model=model)\n", "    return res.data[0].embedding"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "### 注意：如果未使用信用卡支付过 OpenAI 账单的同学，可以直接跳过此步骤。\n", "\n", "### 提醒：非必须步骤，可直接复用项目中的嵌入文件 fine_food_reviews_with_embeddings_1k\n", "\n", "对于免费试用用户的前48小时，OpenAI 设置了 [速率限制](https://platform.openai.com/docs/guides/rate-limits/overview)\n", "\n", "如果你已经支付过 OpenAI API 账单，可以尝试取消注释，调用以下代码测试批量 Embedding："]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["# 实际生成会耗时几分钟，逐行调用 OpenAI Embedding API\n", "\n", "# df[\"embedding\"] = df.combined.apply(embedding_text)\n", "# output_datapath = \"data/fine_food_reviews_with_embeddings_1k_1126.csv\"\n", "# df.to_csv(output_datapath)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# e0 = df[\"embedding\"][0]"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"scrolled": true}, "outputs": [], "source": ["# e0"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## 2.读取 fine_food_reviews_with_embeddings_1k 嵌入文件"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["embedding_datapath = \"data/fine_food_reviews_with_embeddings_1k.csv\"\n", "\n", "df_embedded = pd.read_csv(embedding_datapath, index_col=0)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["#### 查看 Embedding 结果"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["12     [-0.0005399271612986922, -0.004124758299440145...\n", "13     [0.0068963742814958096, 0.0167608093470335, -0...\n", "14     [-0.0023715533316135406, -0.021357767283916473...\n", "15     [0.00226533692330122, 0.010306870564818382, 0....\n", "16     [-0.027459919452667236, -0.009041198529303074,...\n", "                             ...                        \n", "447    [0.00796585250645876, 0.0017102764686569571, 0...\n", "436    [0.001777207711711526, -0.011673098430037498, ...\n", "437    [-0.005498920567333698, -0.014834611676633358,...\n", "438    [-0.00294404081068933, -0.007058987859636545, ...\n", "439    [-0.006043732166290283, -0.000693734094966203,...\n", "Name: embedding, Length: 1000, dtype: object"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["df_embedded[\"embedding\"]"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["34410"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["len(df_embedded[\"embedding\"][0])"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["str"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["type(df_embedded[\"embedding\"][0])"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["'[0.007060592994093895, -0.02732112631201744, 0.010580576956272125, -0.014588683843612671, 0.004358730278909206, 0.019923659041523933, 0.0006634345045313239, -0.02212364971637726, -0.01926366239786148, -0.013578063808381557, 0.018067417666316032, 0.004080294165760279, -0.032944850623607635, 0.00016059498011600226, 0.015798678621649742, 0.019414912909269333, 0.017888668924570084, -0.026317380368709564, -0.019387412816286087, -0.025107385590672493, -0.047382283955812454, -0.00719809252768755, 0.023828642442822456, -0.008463086560368538, -0.008923709392547607, 0.011433073319494724, 0.02945236675441265, -0.02250864915549755, -0.010174954310059547, 0.02161490172147751, 0.001189369591884315, 0.00905433390289545, -0.013364939950406551, -0.017421171069145203, -0.005912473425269127, -0.01095182541757822, -0.02648238092660904, 0.007328717038035393, 0.022481149062514305, -0.02023990824818611, 0.01907116360962391, -0.015303680673241615, 0.009205583482980728, -0.00831183698028326, 0.0017196015687659383, 0.019511161372065544, -0.015482430346310139, -0.033219851553440094, -0.014561183750629425, 0.013268689624965191, 0.03569483757019043, 0.004131856374442577, -0.04163481295108795, 0.004231543280184269, 0.004712791182100773, 0.017379920929670334, -0.019181163981556892, -0.0010664795991033316, 0.011013700626790524, -0.028957368806004524, -0.00012439396232366562, 0.030497362837195396, -0.041277311742305756, 0.013186190277338028, 0.007067468017339706, -0.02073490619659424, -0.0014317122986540198, 0.015317430719733238, -0.01342681422829628, -0.005156226456165314, 0.007754964753985405, 0.0067924694158136845, -0.002138115232810378, 0.002724206540733576, 0.005884973332285881, -0.005582474637776613, -0.018672415986657143, -0.0005701927584595978, 0.014189936220645905, -0.006218409165740013, 0.00967995636165142, -0.03088236041367054, -0.01986866071820259, 0.04680478945374489, 0.02245364896953106, 0.024062391370534897, -0.007981838658452034, 0.015303680673241615, -0.022976147010922432, -0.013165565207600594, 0.0030524861067533493, 0.025437384843826294, -0.00886871013790369, -0.0004945680848322809, -0.005709661636501551, 0.02254989743232727, -0.004936227574944496, 0.034237343817949295, -0.006459033116698265, -0.01871366612613201, 0.02602863311767578, 0.006878406275063753, 0.0032020166981965303, -0.021284904330968857, -0.019511161372065544, 0.0032879537902772427, -0.019992409273982048, -0.02245364896953106, 0.02245364896953106, -0.005396850407123566, -0.009666206315159798, 0.013103690929710865, 0.004252168349921703, -0.03500734269618988, -0.018768664449453354, -0.004114668816328049, -0.011433073319494724, -0.026331130415201187, -0.005496537778526545, -0.017709920182824135, -0.006434971000999212, 0.01393556222319603, 0.011075574904680252, -0.016169926151633263, 0.002559207146987319, 0.03277985006570816, 0.004393104929476976, 0.004369042813777924, -0.013564313761889935, -0.012072445824742317, 0.047437284141778946, 0.008215587586164474, 0.00824308767914772, -0.0016267895698547363, -0.02749987505376339, 0.016279926523566246, -0.02931486815214157, 0.001474680844694376, 0.0019215538632124662, -0.00846996158361435, 0.0172699224203825, 0.03717983141541481, 0.008105588145554066, -0.010099329054355621, -0.010078703984618187, 0.029177367687225342, -0.007748090196400881, 0.03833482787013054, -0.010360578075051308, -0.019744910299777985, 0.014368684962391853, -0.019387412816286087, 0.022329898551106453, 0.008112463168799877, 0.002920143073424697, 0.01828741654753685, 0.01689867302775383, 0.00743184145539999, -0.01935991272330284, 0.0031624857801944017, -0.0005908176535740495, 0.00016865157522261143, 0.011522447690367699, -0.03126735985279083, -0.0068990313448011875, 0.02870986983180046, 0.0335223488509655, 0.0002016299549723044, 0.007589965593069792, 0.0012056976556777954, -0.006187472026795149, 0.021037405356764793, -0.03594233840703964, 0.025891132652759552, 6.235811451915652e-05, -0.0008292931015603244, 0.008380587212741375, -0.003746858099475503, -0.030249863862991333, 0.01736617088317871, 0.019689910113811493, -0.006173721980303526, 0.0314873568713665, 0.04630979150533676, -0.008903084322810173, -0.026331130415201187, 0.035034842789173126, -0.010834950953722, 0.010346828028559685, 0.0011395261390134692, 0.0020057721994817257, 0.01335806492716074, 0.020391156896948814, -0.024612389504909515, -0.6305171251296997, -0.014231185428798199, 0.010683701373636723, -0.01296619139611721, 0.01680242456495762, 0.021326154470443726, 0.024846138432621956, -0.0009710893500596285, -0.01893366500735283, 0.011164949275553226, -0.022467399016022682, 0.01060120202600956, 0.020336158573627472, -0.014767433516681194, -0.022756146267056465, -0.033824846148490906, 0.0008202697499655187, -0.013983686454594135, -0.013756812550127506, -0.002727643819525838, -0.01898866333067417, 0.036327335983514786, -0.005692474078387022, -0.010834950953722, 0.007589965593069792, 0.015661180019378662, 0.0010742138838395476, -0.015221181325614452, -0.00010258742986479774, 0.012354318983852863, -0.023828642442822456, 0.04003981873393059, 0.030112363398075104, 0.012711817398667336, 0.045402295887470245, 0.0024062390439212322, -0.012347443960607052, 0.005575599614530802, 0.004843415692448616, 0.023773642256855965, -0.023168645799160004, -0.010119954124093056, 0.02221990004181862, 0.012649943120777607, -0.0036918583791702986, -0.002722487784922123, 0.010889951139688492, 0.0034134220331907272, -0.0016757736448198557, 0.014148686081171036, 0.004881227854639292, 0.012952441349625587, -0.00967995636165142, -0.02287989668548107, 0.006304346490651369, 0.014671184122562408, 0.036327335983514786, -0.003387640928849578, -0.0022378023713827133, 0.01203807070851326, -0.003160767024382949, 0.021174903959035873, -0.012258069589734077, -0.031872354447841644, -0.019511161372065544, 0.021147403866052628, -0.002184521406888962, -0.013963062316179276, 0.031652357429265976, -0.016843674704432487, 0.002064209431409836, 0.021271154284477234, -0.041607312858104706, 0.02125740423798561, 0.013337439857423306, 0.04380730167031288, 0.003370453603565693, 0.0031693605706095695, 0.0027053002268075943, 0.010174954310059547, 0.008091838099062443, 0.013440564274787903, 0.000412283290643245, -0.00974870566278696, 0.03297235071659088, 0.024928636848926544, -0.017668670043349266, 0.022206149995326996, -0.018163667991757393, 0.002167333848774433, 0.014561183750629425, -0.01030557882040739, -0.014726183377206326, -0.01801241934299469, 0.014657434076070786, 0.003475296776741743, -0.008215587586164474, 0.017297422513365746, 0.022618647664785385, -0.00677184434607625, -2.665393185452558e-05, -0.006080910097807646, 0.00997557956725359, -0.010566826909780502, 0.035117343068122864, -0.015221181325614452, -0.001542571117170155, 0.020349908620119095, 0.021312404423952103, -0.01750367134809494, 0.0015778053784742951, -0.013550563715398312, -0.020817406475543976, -0.012044945731759071, -0.005482787732034922, -0.030992360785603523, -0.004733416251838207, 0.021449903026223183, -0.0008026526193134487, -0.01234056893736124, -0.002770612481981516, 0.0051596639677882195, -0.0006153096910566092, -0.004014981910586357, -0.01446493435651064, 0.02638613060116768, 0.02061115764081478, -0.010518702678382397, -0.0012667130213230848, -0.00035341636976227164, -0.0022653022315353155, 0.013804937712848186, 0.019896160811185837, -0.00458216667175293, -0.0031934231519699097, 0.009198708459734917, -0.0011180418077856302, -0.014671184122562408, 0.015826178714632988, -0.026881128549575806, -0.011164949275553226, 0.0038156076334416866, 0.004740291275084019, -0.012333693914115429, 0.006190909538418055, -0.010979325510561466, 0.0054724751971662045, -0.013406189158558846, -0.047162286937236786, 0.00010280226706527174, -0.0026142068672925234, -0.01973116025328636, -0.018906164914369583, -0.006819969043135643, 0.013069315813481808, -0.013688063248991966, 0.007617465686053038, -0.021559903398156166, -0.01773742027580738, -0.014272435568273067, -0.003220923012122512, 0.01342681422829628, -0.03486984223127365, -0.003250141628086567, 0.003516546683385968, -4.734597678179853e-05, 0.03665733337402344, -0.017297422513365746, 0.012203069403767586, -0.022893646731972694, 0.0007111295708455145, -0.03990231826901436, -0.02972736582159996, 0.017929919064044952, -0.028352372348308563, 0.008531836792826653, -0.018493667244911194, -0.013041815720498562, -0.005884973332285881, 0.013330564834177494, 0.011439948342740536, 0.00047609160537831485, -0.008119338192045689, 0.014891183003783226, 0.012869942001998425, -0.002758581191301346, 0.022151149809360504, 0.032944850623607635, -0.02939736656844616, 0.018122417852282524, 0.0026434254832565784, -0.0034563906956464052, -0.008270587772130966, -0.0030421738047152758, 0.005744036752730608, -0.007204967550933361, -0.0022653022315353155, 0.0058437236584723, -0.007191217504441738, 0.009762455709278584, 0.016004927456378937, -0.026592379435896873, 0.013488689437508583, -0.002468113787472248, 0.014973682351410389, -0.027279876172542572, 0.017558671534061432, -0.02699112705886364, 0.02125740423798561, -7.669887418160215e-05, 0.008813709951937199, -0.02223365008831024, -0.011769946664571762, -0.01764116995036602, 0.01662367396056652, 0.027802374213933945, -0.0006866375333629549, 0.006548407953232527, -0.006410908419638872, -0.00034160001087002456, -0.004843415692448616, -0.008971834555268288, 0.005970910657197237, 0.024199889972805977, -0.02360864356160164, 0.015001182444393635, 0.013138066045939922, -0.004719666205346584, 0.017888668924570084, -0.017146172001957893, -0.0023959267418831587, 0.029892364516854286, 0.03143235668540001, 0.02120240405201912, 0.005108101759105921, -0.03129485994577408, 0.025918632745742798, -0.0038121703546494246, 0.022989895194768906, -0.012808066792786121, 0.014767433516681194, 0.02212364971637726, 0.010635577142238617, 0.011742446571588516, -0.006984968204051256, 0.008813709951937199, 0.020116159692406654, 0.015936177223920822, -0.006434971000999212, 0.019662411883473396, -0.003019830211997032, 0.004953415133059025, -0.02329239435493946, 0.014588683843612671, 0.002377020427957177, -0.017283672466874123, -0.027101127430796623, -0.0070537179708480835, 0.04122231528162956, 0.006871531251817942, 0.007768714800477028, -0.00800933875143528, -0.009666206315159798, -0.024337390437722206, 0.026331130415201187, -0.014973682351410389, -0.017517421394586563, -0.03536484017968178, 0.0033206099178642035, -4.33378181696753e-06, -0.00653809541836381, -0.005919348448514938, -0.0002500769915059209, 0.013248065486550331, 0.007115592714399099, -0.002091709291562438, 0.01699492335319519, -0.01330993976444006, -0.012093069963157177, -5.145484465174377e-05, -0.03533734008669853, -0.008607461117208004, 0.021958651021122932, -0.01176307164132595, -0.010779951699078083, 0.02088615484535694, -0.0016465550288558006, -0.006421220954507589, -0.009542456828057766, 0.020253658294677734, 0.017214922234416008, 0.013976811431348324, -0.018094917759299278, -0.006637782324105501, 0.00391185749322176, 0.021229904145002365, 0.054009754210710526, -0.037207331508398056, 0.03495234251022339, -0.004011544398963451, 0.017063673585653305, -0.017929919064044952, -0.020308658480644226, -0.008139963261783123, 0.008380587212741375, 0.012938691303133965, 0.012326818890869617, 0.0024595202412456274, -0.007321842014789581, -0.01025745365768671, 0.007706840056926012, -0.002136396476998925, -0.005376225803047419, 0.01462993398308754, 0.030084863305091858, -0.015399930067360401, -0.010566826909780502, 0.013674313202500343, 0.018067417666316032, 0.011818071827292442, -0.013131191022694111, -0.040864814072847366, -0.03800482675433159, 0.014643684029579163, 0.12990941107273102, -0.0038327951915562153, -0.003269047709181905, 0.008236212655901909, 0.0022549896966665983, 0.02073490619659424, -0.01889241486787796, -0.009308707900345325, 0.012423069216310978, -0.014011186547577381, 0.020253658294677734, 0.0006823406438343227, -0.0006260518566705287, -0.008023088797926903, 0.030799860134720802, -0.021834900602698326, -0.013000566512346268, -0.02041865698993206, -0.015179931186139584, -0.0043827928602695465, 0.01597742736339569, 0.023031145334243774, 0.0007201529806479812, 0.011054949834942818, 0.018727416172623634, -0.017654919996857643, 0.04949977621436119, 0.014203685335814953, 0.0009994485881179571, -0.018603665754199028, -0.016128677874803543, -0.00021699120406992733, -0.008463086560368538, 0.01078682579100132, -0.01695367321372032, -0.01060120202600956, 0.009026833809912205, 0.027472374960780144, 0.016651174053549767, -0.01564742997288704, 0.006036222912371159, -0.0037571704015135765, 0.008263712748885155, -0.03156985715031624, 0.014519934542477131, -0.0024784263223409653, -0.020061159506440163, -0.004922477528452873, 0.01560617983341217, -0.007878714241087437, 0.05293726176023483, 0.00023933485499583185, 0.0038568575400859118, -0.01759991981089115, 0.027293626219034195, -0.005238726269453764, -0.02245364896953106, 0.012656818144023418, -0.008016213774681091, -0.005884973332285881, -0.0065655955113470554, -0.0331648513674736, -0.00847683660686016, -0.004826228134334087, -0.03357734903693199, -0.02972736582159996, -0.02397989109158516, -8.405723929172382e-05, -0.0039840443059802055, -0.016252426430583, -0.0033893596846610308, -0.005847161170095205, -0.010339953005313873, 0.011446823365986347, 0.05070976912975311, 0.01574367843568325, 0.0024956136476248503, 0.002751706400886178, 0.025629883632063866, -0.004186856094747782, 0.023278644308447838, -0.025011137127876282, -0.006919656414538622, -0.02149115316569805, 0.006857781670987606, 0.016004927456378937, 0.005493100266903639, -0.024791138246655464, -0.0012323381379246712, 0.005128726828843355, -0.01254681870341301, 0.012677442282438278, 0.01744867116212845, -0.009851830080151558, -0.0030817047227174044, -0.012539943680167198, -0.00379154528491199, 0.01893366500735283, -0.0008323009242303669, -0.00758309056982398, 0.011955571360886097, 0.031872354447841644, -0.009948080405592918, -0.02884737029671669, 0.001235775649547577, 0.014781183563172817, -0.0006303486879914999, 0.010147454217076302, -0.006273409351706505, -0.01828741654753685, 0.04089231416583061, -0.004393104929476976, 0.003189985640347004, 0.023883642628788948, -0.017764920368790627, 0.017847418785095215, -0.0029992051422595978, 0.019882410764694214, -0.021738652139902115, -0.02824237197637558, 0.010779951699078083, -0.043174803256988525, 0.0335223488509655, 0.0018012418877333403, -0.033082351088523865, 0.010752451606094837, -0.013021191582083702, -0.03330234810709953, 0.0033292036969214678, -0.01400431152433157, -0.017297422513365746, 0.004468729719519615, -0.0181086678057909, -0.009205583482980728, -0.03580483794212341, -0.011536197736859322, -0.025808634236454964, 0.024653637781739235, -0.0008997615659609437, -0.014011186547577381, -0.006300908979028463, -0.020776156336069107, -0.0031349859200417995, -0.004613104276359081, -0.014959932304918766, -0.034237343817949295, -0.00667903246358037, -0.0035268589854240417, 0.004200606141239405, 0.011261198669672012, 0.02351239323616028, 0.006892156321555376, -0.010814325883984566, -0.0008426134008914232, 0.00022794818505644798, -0.032889850437641144, 0.008146838285028934, 0.02829737216234207, 0.022659897804260254, 0.03580483794212341, 0.027994873002171516, 0.0015305399429053068, 0.019387412816286087, 0.006379971280694008, 0.024928636848926544, -0.0044652922078967094, -0.013028065674006939, 0.005761223845183849, -0.01703617349267006, -0.0023856142070144415, -0.009597457014024258, -0.0067924694158136845, -0.012759942561388016, 0.02139490284025669, 0.004602791741490364, 0.022494899109005928, -0.0033326412085443735, -0.02360864356160164, 0.0022085837554186583, -0.032889850437641144, 0.0064315334893763065, -0.006269971840083599, -0.013784312643110752, 0.032202355563640594, 0.011089324951171875, 0.005084039643406868, 0.040809813886880875, 0.006294033955782652, 0.007184342481195927, 0.009948080405592918, 0.04152481257915497, -0.013138066045939922, 0.0122718196362257, -0.011096199974417686, -0.03357734903693199, -0.0006922234315425158, -0.01695367321372032, -0.02806362323462963, -0.00858683604747057, 0.0026949879247695208, 0.026509881019592285, 0.012134320102632046, -0.017104921862483025, -0.01699492335319519, 0.0014317122986540198, 0.018782414495944977, -0.013440564274787903, -0.0009960110764950514, 0.00905433390289545, -0.009879330173134804, -0.019937409088015556, -0.016142427921295166, -0.005224976222962141, -0.01106182485818863, 0.012539943680167198, -0.0037262332625687122, -0.004052794072777033, 0.01680242456495762, -0.036932334303855896, -0.026262382045388222, -0.019002413377165794, -0.02069365605711937, 0.021133653819561005, 0.008091838099062443, 0.013633063063025475, 0.029782366007566452, 0.01014057919383049, -0.021779902279376984, -0.00549997529014945, 0.012629318051040173, 0.014492434449493885, 0.02241239883005619, 0.031789857894182205, 0.014451184310019016, 0.014973682351410389, -0.012436818331480026, -0.019882410764694214, -0.014588683843612671, -0.029809866100549698, 0.03640983626246452, 0.009116209112107754, 0.006837156601250172, -0.016376176849007607, -0.007514340803027153, -0.007418091408908367, 0.01828741654753685, -0.00530060101300478, -0.005809348542243242, -0.009707456454634666, -0.013901187106966972, -0.025038637220859528, 0.004021856933832169, 0.0036059212870895863, 0.013516188599169254, 0.014382435008883476, -0.01055307686328888, -0.03264235332608223, -0.007528090849518776, -0.023209895938634872, 0.008435586467385292, 0.015496180392801762, 0.003671233309432864, -0.00972120650112629, -0.008504336699843407, 0.00328623503446579, -0.009294957853853703, -0.019043663516640663, -0.006644657347351313, 0.004049356561154127, 0.031734857708215714, -0.02292114682495594, -0.009253707714378834, -0.010401828214526176, -0.0033910784404724836, 0.016128677874803543, -0.011048074811697006, -0.011543072760105133, -0.015812428668141365, -0.0025557696353644133, -0.027279876172542572, 0.006954031065106392, 0.014031811617314816, 0.026193631812930107, -0.013523063622415066, -0.006582782603800297, -0.004551229532808065, -0.04366980120539665, -0.01095182541757822, 0.00308514223434031, -0.01671992428600788, -0.023347394540905952, -0.024007391184568405, -0.0006080050370655954, 0.008222462609410286, 0.0058196610771119595, -0.015674928203225136, 0.006995280738919973, 0.019194914028048515, 0.0003087290679104626, 0.006761531811207533, -0.040122319012880325, 0.013447439298033714, -0.054202254861593246, -0.008208712562918663, -0.017201172187924385, -0.012725567445158958, 0.015083681792020798, 0.004386230371892452, -0.0052318512462079525, -0.03739983215928078, 0.01621117629110813, 0.009288082830607891, 0.0012753066839650273, 0.0010183547856286168, 0.004905290435999632, 0.01446493435651064, 0.02004740945994854, 0.014684933237731457, -0.02319614589214325, 0.023856142535805702, -0.021051155403256416, 0.022769896313548088, 0.01801241934299469, 0.001753976452164352, -0.01095182541757822, -0.0012177288299426436, 0.029782366007566452, 0.009006209671497345, -0.004045919049531221, 0.0011953852372244, -0.01666492410004139, 0.02925986796617508, -0.012505568563938141, 0.005661536939442158, -0.01296619139611721, -0.00154858676251024, -0.014781183563172817, 0.009343083016574383, -0.0009341363911516964, -0.009769330732524395, 0.02319614589214325, 0.02597363293170929, 0.002260145964100957, 0.008552460931241512, -0.011742446571588516, 0.003277641488239169, -0.025959882885217667, 0.015853678807616234, -0.030524861067533493, -0.02796737290918827, -0.01157744787633419, 0.04265230894088745, 0.002951080445200205, -0.02505238726735115, -0.026124881580471992, -0.007191217504441738, -0.016871172934770584, -0.010800575837492943, -0.027389876544475555, 0.022811146453022957, 0.007596840616315603, -0.0010346828494220972, -0.008538711816072464, 0.02611113153398037, -0.002110615372657776, 0.021656151860952377, -0.005957160610705614, -0.004905290435999632, -0.004870915319770575, 0.0006049972726032138, -0.020074909552931786, -0.009143708273768425, -0.02139490284025669, 0.0016199145466089249, -0.01722867228090763, 0.006857781670987606, -0.02448863908648491, 0.019882410764694214, -0.00033988128416240215, -0.001393040525726974, -0.03398984670639038, 0.026564879342913628, 0.008284337818622589, 0.02319614589214325, 0.005465600173920393, -0.005249038804322481, -0.005585912149399519, 0.016032427549362183, -0.020294908434152603, -0.002578113228082657, -0.015303680673241615, 0.010168079286813736, 0.01852116547524929, 0.007002155762165785, -0.007369966711848974, -0.0015992895932868123, 0.00204874062910676, -0.018741164356470108, 0.02143615297973156, 0.011075574904680252, -0.02912236750125885, 0.023966141045093536, 0.034787341952323914, 0.013096815906465054, -0.02397989109158516, 0.013296189717948437, -0.021381152793765068, -0.012368069030344486, 0.01428618561476469, -0.01215494517236948, -0.01111682504415512, -0.004413729999214411, 0.014671184122562408, 0.010931200347840786, 0.0029029555153101683, -0.0001269720814889297, 0.01513868197798729, -0.00824308767914772, 0.025011137127876282, 0.03459484502673149, -0.0252311360090971, 0.008896210230886936, 0.003884357400238514, -0.007699965033680201, 0.024667387828230858, -0.014588683843612671, 0.0005461303517222404, -0.02912236750125885, 0.023347394540905952, 0.032614853233098984, -0.032339852303266525, -0.04122231528162956, 0.0027774875052273273, -0.012374944053590298, -0.019153663888573647, 0.013639938086271286, 0.19425912201404572, -0.004114668816328049, 0.02212364971637726, 0.036602333188056946, -0.012299319729208946, -0.001428274787031114, 0.02579488418996334, 0.01754492148756981, -0.03305485099554062, 0.014506184495985508, -0.003435765625908971, -0.007672465406358242, -0.015798678621649742, -0.011879946105182171, 0.01842491701245308, -0.0032140479888767004, -0.02129865437746048, -0.04427479952573776, -0.014327434822916985, -0.018782414495944977, -0.018438667058944702, 0.011632447130978107, -0.01076620165258646, -0.008380587212741375, 0.02292114682495594, 0.019744910299777985, 0.010944950394332409, 0.016128677874803543, -0.0079612135887146, 0.008669335395097733, -0.03046986274421215, -0.01560617983341217, -0.0027121752500534058, 0.023443644866347313, -0.018576165661215782, -0.002423426602035761, 0.034154847264289856, -0.01852116547524929, 0.003777795471251011, 0.012148070149123669, -0.00909558404237032, -0.002572957193478942, 0.008944334462285042, -0.033769845962524414, -0.04047981649637222, 0.03726233169436455, 0.012670567259192467, -0.0064796581864356995, -0.00532466359436512, 0.02149115316569805, -0.005018727388232946, -0.03943482041358948, 0.026867378503084183, 0.006644657347351313, 0.00824308767914772, -0.01366056315600872, 0.014712433330714703, -0.005156226456165314, -0.02851737104356289, 0.024529889225959778, -0.008676210418343544, 0.04531979560852051, -0.02542363479733467, 0.03385234624147415, -0.023278644308447838, 0.019648661836981773, -0.008793084882199764, -0.021931150928139687, 0.016444925218820572, -0.02630363032221794, 0.0009152302518486977, -0.012512443587183952, -0.00421435572206974, -0.002782643772661686, 0.0018785852007567883, -0.009673081338405609, 0.02305864542722702, 0.01750367134809494, 0.024021141231060028, 0.0362173356115818, -0.025781134143471718, -0.031047359108924866, 0.004080294165760279, 0.0037262332625687122, -0.008813709951937199, -0.055577248334884644, 0.025712383911013603, -0.0002545886964071542, -0.027761124074459076, -0.0281048733741045, 0.005486225243657827, -0.01245744340121746, -0.0045237294398248196, -0.006593095138669014, 0.006297471467405558, 0.00789246428757906, -0.01254681870341301, 0.009184958413243294, 0.015179931186139584, -0.014148686081171036, -0.016912423074245453, -0.010917450301349163, 0.007974963635206223, 0.009143708273768425, -0.002939049154520035, -0.00047995877685025334, 0.01897491328418255, 0.02628988027572632, 3.488509537419304e-05, 0.021724902093410492, 0.002801549853757024, -0.024364890530705452, 0.008579961024224758, 0.007081218063831329, -0.012938691303133965, 0.01042245328426361, -0.025712383911013603, -0.04105731472373009, 0.024296140298247337, 0.0017471014289185405, 0.007576215546578169, -0.028957368806004524, -0.008738085627555847, 0.023691141977906227, -0.01953866146504879, -0.01758616976439953, -0.023209895938634872, 0.014368684962391853, 0.01486368291079998, -0.015427430160343647, 0.008772460743784904, -0.01959366165101528, 0.0006741766119375825, 0.002469832543283701, -0.022288648411631584, -0.0004208770114928484, 0.006991843227297068, -0.00547591270878911, -0.009913705289363861, -0.011453698389232159, -0.007906214334070683, 0.01412118598818779, 0.014602433890104294, -0.012588067911565304, 0.007149967830628157, -0.03327484801411629, -0.0029631115030497313, -0.01940116286277771, -0.00789246428757906, -0.016926173120737076, 0.01254681870341301, 0.009494331665337086, 0.015248681418597698, -0.012079320847988129, 0.024048641324043274, -0.008717460557818413, -0.026647379621863365, -0.008628086186945438, 0.007479966152459383, 0.007418091408908367, -0.012299319729208946, 0.006651532370597124, 0.037757329642772675, -0.023072395473718643, -0.025409884750843048, 0.013131191022694111, -0.17621921002864838, 0.040067318826913834, 0.012677442282438278, -0.015716178342700005, 0.030909860506653786, -0.011797446757555008, 0.04834477975964546, 0.021463653072714806, -0.03374234586954117, 0.0010656202211976051, 0.013440564274787903, 0.007624340709298849, -0.01578492857515812, -0.008298087865114212, -0.0012521037133410573, -0.032669853419065475, -0.004228105768561363, 0.024186139926314354, 0.006177159491926432, 0.006115284748375416, 0.030909860506653786, 0.006355908699333668, 0.011247449554502964, -0.03209235519170761, -0.004427480045706034, 0.016403675079345703, -0.001406790572218597, -0.023347394540905952, 0.002571238437667489, -0.0019181163515895605, 0.0027448313776403666, 0.004410292487591505, 0.02536863461136818, -0.010738701559603214, 0.036327335983514786, -0.0033309224527329206, -0.003932482097297907, -0.013158690184354782, -0.0027482688892632723, 0.003908419981598854, 0.004482479766011238, 0.007858089171350002, 0.0010475734015926719, -0.02806362323462963, 0.0061324723064899445, -0.005493100266903639, -0.018809914588928223, -0.008593711070716381, -0.02045990712940693, -0.027609875425696373, -0.013571188785135746, -0.019208664074540138, 0.03363234922289848, 0.019896160811185837, -0.0009272614261135459, 0.01462993398308754, -0.004413729999214411, 0.0015013213269412518, -0.0010879638139158487, -0.0011541354469954967, 0.004128418862819672, -0.035309839993715286, -0.00046320102410390973, 0.0049706026911735535, -0.013942437246441841, -0.02634488046169281, -0.00963183119893074, 0.003114360850304365, 0.002916705561801791, 0.010477452538907528, -0.010154329240322113, -0.0003733967605512589, 0.0005615990376099944, -0.01042245328426361, 0.005008414853364229, 0.021889900788664818, -0.0122718196362257, 0.017146172001957893, 0.012024320662021637, 0.016321176663041115, -0.01703617349267006, 0.028352372348308563, -0.02393864095211029, 0.003435765625908971, -0.001983428606763482, 0.00010043899965239689, -0.00549997529014945, 0.01973116025328636, 0.011096199974417686, -0.016651174053549767, 0.007191217504441738, 0.003475296776741743, 0.00596059812232852, -0.00910245906561613, 0.027582375332713127, 0.015014932490885258, -0.027211127802729607, 0.0025798319838941097, 0.003315453650429845, -0.020817406475543976, 0.01516618113964796, 0.009315582923591137, -0.019552411511540413, 0.008903084322810173, 0.00990683026611805, 0.01791616901755333, -0.00394623214378953, 0.047519784420728683, 0.004056231584399939, -0.0017634294927120209, -0.013990561477839947, 0.028324872255325317, 0.020253658294677734, 0.01824616827070713, 0.015564929693937302, 0.034099847078323364, 0.009851830080151558, -0.02573988400399685, 0.05026977136731148, -0.012010570615530014, 0.011446823365986347, -0.0026107693556696177, -0.011158074252307415, -0.00454779202118516, -0.002019522013142705, -0.019208664074540138, -0.09861455112695694, -0.003434046870097518, 0.003643733449280262, 0.008710585534572601, -0.035914838314056396, -9.071735985344276e-05, 0.023828642442822456, 0.012065570801496506, -0.0020968655589967966, 0.03805982694029808, 0.001032104715704918, -0.02149115316569805, 0.027279876172542572, 0.010525577701628208, 0.0208311565220356, 0.0061187222599983215, 0.015262430533766747, -0.009246833622455597, -0.021821150556206703, 0.02370489202439785, -0.00805746391415596, 0.004479042254388332, -0.0064315334893763065, -0.010979325510561466, 0.028214871883392334, -0.028091123327612877, -0.012842441909015179, 0.004960290156304836, 0.030057363212108612, -0.0009306989377364516, 0.0024784263223409653, 0.00898558460175991, 0.01634867675602436, -0.013062440790235996, 0.01621117629110813, -0.0026451442390680313, -0.009968704544007778, -0.021917400881648064, 0.0287923701107502, -0.05967472866177559, -0.0023907704744488, -0.01847991719841957, -0.031239857897162437, -0.029204867780208588, 0.03640983626246452, 0.004197168629616499, -0.020679906010627747, 0.007253092247992754, 0.018122417852282524, -0.003358422312885523, 0.015496180392801762, 0.009948080405592918, -0.023553643375635147, -0.01574367843568325, 0.025437384843826294, -0.02518988586962223, 0.012698067352175713, 0.010168079286813736, -0.02620738185942173, 0.0018012418877333403, 0.002334051998332143, -0.014794932678341866, -0.018507415428757668, 0.04177230969071388, 0.03156985715031624, 0.020487407222390175, -0.012746192514896393, -0.011639322154223919, -0.01568867824971676, 0.00997557956725359, 0.0014411653392016888, 0.014134936034679413, -0.010779951699078083, 0.002859987085685134, -0.017861168831586838, -0.01319306530058384, -0.032009854912757874, -0.0033498285338282585, -0.0054724751971662045, -0.003636858658865094, 0.00986558012664318, -0.023828642442822456, -0.01746242120862007, -0.00044171675108373165, 0.015936177223920822, 0.015551179647445679, -0.015124931931495667, 0.012175570242106915, -0.01676117442548275, -0.02407614141702652, -0.0059399730525910854, 0.023966141045093536, 0.006978093646466732, 0.015482430346310139, -0.01893366500735283, 0.018438667058944702, -0.004066544119268656, 0.019566161558032036, 0.03668483346700668, 0.016279926523566246, -0.035447340458631516, -0.026001133024692535, -0.05873973295092583, 0.017063673585653305, 0.005671849474310875, -0.015399930067360401, 0.00030958844581618905, -0.024804888293147087, 0.011268073692917824, -0.021752402186393738, -0.0050771646201610565, 0.0122718196362257, -0.0009728081058710814, 0.013371814973652363, -0.005613412242382765, 0.034154847264289856, 0.0062871589325368404, -0.023127395659685135, 0.0062390342354774475, 0.007974963635206223, 0.005194039084017277, 0.014231185428798199, -0.005080602131783962, -0.005259351339191198, 0.024928636848926544, -0.02227489836513996, -0.012560567818582058, 0.027376126497983932, -0.017434921115636826, 0.04014981910586357, -0.004595916718244553, -0.004936227574944496, 0.026551129296422005, -0.013048690743744373, 0.003098892280831933, 0.03481484204530716, -0.002939049154520035, 0.004265918396413326, 0.0032552978955209255, 0.022756146267056465, 0.023553643375635147, -0.023113645613193512, -0.024062391370534897, -0.01435493491590023, 0.027747374027967453, 0.004767790902405977, -0.008731210604310036, -0.00373310805298388, -0.010704326443374157, -0.0007033952861092985, 0.013949312269687653, 0.016183676198124886, -0.0011781977955251932, 0.02773362398147583, -0.002531707286834717, -0.017806168645620346, -0.0037124832160770893, -0.007809964474290609, 0.01740742102265358, 0.011247449554502964, 0.00026962769334204495, -0.048124782741069794, 0.027211127802729607, 0.01759991981089115, 0.0026657693088054657, 0.0037812329828739166, 0.015111181885004044, -0.011219949461519718, -0.01680242456495762, 0.012835566885769367, 0.0009212458389811218, -0.025176135823130608, -0.00329826632514596, 0.010147454217076302, 0.02139490284025669, -0.013028065674006939, 0.013956187292933464, -0.01963491179049015, -0.01967616192996502, -0.017077423632144928, -0.01852116547524929, 0.004228105768561363, 0.035914838314056396, 0.009157458320260048, -0.06253471970558167, 0.024516139179468155, 0.00400466937571764, 0.022563647478818893, 0.00951495673507452, 0.006177159491926432, 0.0022498336620628834, 0.012189320288598537, -0.0032759227324277163, -0.011013700626790524, 0.016706174239516258, 0.0029149868059903383, -0.0074387164786458015, 0.020336158573627472, 0.0018906164914369583, -0.02111990377306938, 0.015221181325614452, 0.026881128549575806, 0.013756812550127506, 0.007768714800477028, 0.020391156896948814, -0.014011186547577381, -0.038087327033281326, -0.005750911310315132, -0.02754112519323826, -0.03239485248923302, -0.0035715464036911726, -0.011213074438273907, 0.0042246682569384575, -0.017847418785095215, 0.008765585720539093, 0.0065140328370034695, -0.02171115204691887, 0.018259918317198753, -0.006991843227297068, -0.00840808730572462, -0.025176135823130608, 0.004454979673027992, 0.006613720208406448, 0.011233699508011341, 0.016279926523566246, -0.016266176477074623, -0.0314873568713665, 0.006758094299584627, 0.0033034225925803185, -0.0067065320909023285, 0.008717460557818413, -0.01164619717746973, -0.012574317865073681, -0.011522447690367699, -0.009769330732524395, -0.007149967830628157, -0.01568867824971676, 0.009810580871999264, 0.019854910671710968, -0.005087477155029774, -0.032807350158691406, 0.04441229999065399, -0.0065655955113470554, -0.015936177223920822, 0.008538711816072464, 0.016362426802515984, 0.0348973423242569, -0.001806398038752377, 0.0017634294927120209, -0.017957419157028198, -0.009274332784116268, -0.0015228056581690907, -0.02628988027572632, -0.002689831657335162, -0.03731733188033104, -0.03398984670639038, 0.01527618058025837, -0.023168645799160004, -0.01607367768883705, -0.0135436886921525, 0.005465600173920393, 0.01671992428600788, 0.01865866594016552, 0.0028548308182507753, -0.001274447306059301, -0.019153663888573647, -0.015674928203225136, 0.015014932490885258, -0.012670567259192467, -0.00842871144413948, -0.04353230446577072, 0.007796214893460274, 0.011886821128427982, -0.04218481108546257, -0.014767433516681194, 0.006569033022969961, -0.00963183119893074, -0.011027449741959572, 0.009961829520761967, 0.02125740423798561, -0.002842799760401249, -0.0033446722663939, 0.02157365158200264, -0.03264235332608223, -0.002664050552994013, 0.029094869270920753, 0.0015253836754709482, -0.007005593273788691, -0.021876150742173195, -0.037564828991889954]'"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["df_embedded[\"embedding\"][0]"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["import ast\n", "\n", "# 将字符串转换为向量\n", "df_embedded[\"embedding_vec\"] = df_embedded[\"embedding\"].apply(ast.literal_eval)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["1536"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["len(df_embedded[\"embedding_vec\"][0])"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ProductId</th>\n", "      <th>UserId</th>\n", "      <th>Score</th>\n", "      <th>Summary</th>\n", "      <th>Text</th>\n", "      <th>combined</th>\n", "      <th>n_tokens</th>\n", "      <th>embedding</th>\n", "      <th>embedding_vec</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>B000K8T3OQ</td>\n", "      <td>AK43Y4WT6FFR3</td>\n", "      <td>1</td>\n", "      <td>Broken in a million pieces</td>\n", "      <td>Chips were broken into small pieces. Problem i...</td>\n", "      <td>Title: Broken in a million pieces; Content: Ch...</td>\n", "      <td>120</td>\n", "      <td>[-0.0005399271612986922, -0.004124758299440145...</td>\n", "      <td>[-0.0005399271612986922, -0.004124758299440145...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>B0051C0J6M</td>\n", "      <td>AWFA8N9IXELVH</td>\n", "      <td>1</td>\n", "      <td>Deceptive description</td>\n", "      <td>On Oct 9 I ordered from a different vendor the...</td>\n", "      <td>Title: Deceptive description; Content: On Oct ...</td>\n", "      <td>157</td>\n", "      <td>[0.0068963742814958096, 0.0167608093470335, -0...</td>\n", "      <td>[0.0068963742814958096, 0.0167608093470335, -0...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     ProductId         UserId  Score                     Summary  \\\n", "12  B000K8T3OQ  AK43Y4WT6FFR3      1  Broken in a million pieces   \n", "13  B0051C0J6M  AWFA8N9IXELVH      1       Deceptive description   \n", "\n", "                                                 Text  \\\n", "12  Chips were broken into small pieces. Problem i...   \n", "13  On Oct 9 I ordered from a different vendor the...   \n", "\n", "                                             combined  n_tokens  \\\n", "12  Title: Broken in a million pieces; Content: Ch...       120   \n", "13  Title: Deceptive description; Content: On Oct ...       157   \n", "\n", "                                            embedding  \\\n", "12  [-0.0005399271612986922, -0.004124758299440145...   \n", "13  [0.0068963742814958096, 0.0167608093470335, -0...   \n", "\n", "                                        embedding_vec  \n", "12  [-0.0005399271612986922, -0.004124758299440145...  \n", "13  [0.0068963742814958096, 0.0167608093470335, -0...  "]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["df_embedded.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 使用 t-SNE 可视化 1536 维 Embedding 美食评论"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["# 导入 NumPy 包，NumPy 是 Python 的一个开源数值计算扩展。这种工具可用来存储和处理大型矩阵，\n", "# 比 Python 自身的嵌套列表（nested list structure)结构要高效的多。\n", "import numpy as np\n", "# 从 matplotlib 包中导入 pyplot 子库，并将其别名设置为 plt。\n", "# matplotlib 是一个 Python 的 2D 绘图库，pyplot 是其子库，提供了一种类似 MATLAB 的绘图框架。\n", "import matplotlib.pyplot as plt\n", "import matplotlib\n", "\n", "# 从 sklearn.manifold 模块中导入 TSNE 类。\n", "# TSNE (t-Distributed Stochastic Neighbor Embedding) 是一种用于数据可视化的降维方法，尤其擅长处理高维数据的可视化。\n", "# 它可以将高维度的数据映射到 2D 或 3D 的空间中，以便我们可以直观地观察和理解数据的结构。\n", "from sklearn.manifold import TSNE"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["pandas.core.series.Series"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["type(df_embedded[\"embedding_vec\"])"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["# 首先，确保你的嵌入向量都是等长的\n", "assert df_embedded['embedding_vec'].apply(len).nunique() == 1"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["# 将嵌入向量列表转换为二维 numpy 数组\n", "matrix = np.vstack(df_embedded['embedding_vec'].values)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["# 创建一个 t-SNE 模型，t-SNE 是一种非线性降维方法，常用于高维数据的可视化。\n", "# n_components 表示降维后的维度（在这里是2D）\n", "# perplexity 可以被理解为近邻的数量\n", "# random_state 是随机数生成器的种子\n", "# init 设置初始化方式\n", "# learning_rate 是学习率。\n", "tsne = TSNE(n_components=2, perplexity=15, random_state=42, init='random', learning_rate=200)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["# 使用 t-SNE 对数据进行降维，得到每个数据点在新的2D空间中的坐标\n", "vis_dims = tsne.fit_transform(matrix)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["# 定义了五种不同的颜色，用于在可视化中表示不同的等级\n", "colors = [\"red\", \"darkorange\", \"gold\", \"turquoise\", \"darkgreen\"]"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["# 从降维后的坐标中分别获取所有数据点的横坐标和纵坐标\n", "x = [x for x,y in vis_dims]\n", "y = [y for x,y in vis_dims]\n", "\n", "# 根据数据点的评分（减1是因为评分是从1开始的，而颜色索引是从0开始的）获取对应的颜色索引\n", "color_indices = df_embedded.Score.values - 1\n", "\n", "# 确保你的数据点和颜色索引的数量匹配\n", "assert len(vis_dims) == len(df_embedded.Score.values)"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0.5, 1.0, 'Amazon ratings visualized in language using t-SNE')"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 创建一个基于预定义颜色的颜色映射对象\n", "colormap = matplotlib.colors.ListedColormap(colors)\n", "# 使用 matplotlib 创建散点图，其中颜色由颜色映射对象和颜色索引共同决定，alpha 是点的透明度\n", "plt.scatter(x, y, c=color_indices, cmap=colormap, alpha=0.3)\n", "\n", "# 为图形添加标题\n", "plt.title(\"Amazon ratings visualized in language using t-SNE\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**t-SNE降维后，评论大致分为3个大类。**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 使用 K-Means 聚类，然后使用 t-SNE 可视化"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "# 从 scikit-learn中导入 KMeans 类。KMeans 是一个实现 K-Means 聚类算法的类。\n", "from sklearn.cluster import KMeans\n", "\n", "# np.vstack 是一个将输入数据堆叠到一个数组的函数（在垂直方向）。\n", "# 这里它用于将所有的 ada_embedding 值堆叠成一个矩阵。\n", "# matrix = np.vstack(df.ada_embedding.values)\n", "\n", "# 定义要生成的聚类数。\n", "n_clusters = 3\n", "\n", "# 创建一个 KMeans 对象，用于进行 K-Means 聚类。\n", "# n_clusters 参数指定了要创建的聚类的数量；\n", "# init 参数指定了初始化方法（在这种情况下是 'k-means++'）；\n", "# random_state 参数为随机数生成器设定了种子值，用于生成初始聚类中心。\n", "# n_init=10 消除警告 'FutureWarning: The default value of `n_init` will change from 10 to 'auto' in 1.4'\n", "kmeans = KMeans(n_clusters = n_clusters, init='k-means++', random_state=42, n_init=10)\n", "\n", "# 使用 matrix（我们之前创建的矩阵）来训练 KMeans 模型。这将执行 K-Means 聚类算法。\n", "kmeans.fit(matrix)\n", "\n", "# kmeans.labels_ 属性包含每个输入数据点所属的聚类的索引。\n", "# 这里，我们创建一个新的 'Cluster' 列，在这个列中，每个数据点都被赋予其所属的聚类的标签。\n", "df_embedded['Cluster'] = kmeans.labels_"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/plain": ["12     0\n", "13     0\n", "14     0\n", "15     2\n", "16     0\n", "      ..\n", "447    0\n", "436    0\n", "437    1\n", "438    0\n", "439    0\n", "Name: Cluster, Length: 1000, dtype: int32"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["df_embedded['Cluster']"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ProductId</th>\n", "      <th>UserId</th>\n", "      <th>Score</th>\n", "      <th>Summary</th>\n", "      <th>Text</th>\n", "      <th>combined</th>\n", "      <th>n_tokens</th>\n", "      <th>embedding</th>\n", "      <th>embedding_vec</th>\n", "      <th>Cluster</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>B000K8T3OQ</td>\n", "      <td>AK43Y4WT6FFR3</td>\n", "      <td>1</td>\n", "      <td>Broken in a million pieces</td>\n", "      <td>Chips were broken into small pieces. Problem i...</td>\n", "      <td>Title: Broken in a million pieces; Content: Ch...</td>\n", "      <td>120</td>\n", "      <td>[-0.0005399271612986922, -0.004124758299440145...</td>\n", "      <td>[-0.0005399271612986922, -0.004124758299440145...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>B0051C0J6M</td>\n", "      <td>AWFA8N9IXELVH</td>\n", "      <td>1</td>\n", "      <td>Deceptive description</td>\n", "      <td>On Oct 9 I ordered from a different vendor the...</td>\n", "      <td>Title: Deceptive description; Content: On Oct ...</td>\n", "      <td>157</td>\n", "      <td>[0.0068963742814958096, 0.0167608093470335, -0...</td>\n", "      <td>[0.0068963742814958096, 0.0167608093470335, -0...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     ProductId         UserId  Score                     Summary  \\\n", "12  B000K8T3OQ  AK43Y4WT6FFR3      1  Broken in a million pieces   \n", "13  B0051C0J6M  AWFA8N9IXELVH      1       Deceptive description   \n", "\n", "                                                 Text  \\\n", "12  Chips were broken into small pieces. Problem i...   \n", "13  On Oct 9 I ordered from a different vendor the...   \n", "\n", "                                             combined  n_tokens  \\\n", "12  Title: Broken in a million pieces; Content: Ch...       120   \n", "13  Title: Deceptive description; Content: On Oct ...       157   \n", "\n", "                                            embedding  \\\n", "12  [-0.0005399271612986922, -0.004124758299440145...   \n", "13  [0.0068963742814958096, 0.0167608093470335, -0...   \n", "\n", "                                        embedding_vec  Cluster  \n", "12  [-0.0005399271612986922, -0.004124758299440145...        0  \n", "13  [0.0068963742814958096, 0.0167608093470335, -0...        0  "]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["df_embedded.head(2)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 首先为每个聚类定义一个颜色。\n", "colors = [\"red\", \"green\", \"purple\"]\n", "\n", "# 然后，你可以使用 t-SNE 来降维数据。这里，我们只考虑 'embedding_vec' 列。\n", "tsne_model = TSNE(n_components=2, random_state=42)\n", "vis_data = tsne_model.fit_transform(matrix)\n", "\n", "# 现在，你可以从降维后的数据中获取 x 和 y 坐标。\n", "x = vis_data[:, 0]\n", "y = vis_data[:, 1]\n", "\n", "# 'Cluster' 列中的值将被用作颜色索引。\n", "color_indices = df_embedded['Cluster'].values\n", "\n", "# 创建一个基于预定义颜色的颜色映射对象\n", "colormap = matplotlib.colors.ListedColormap(colors)\n", "\n", "# 使用 matplotlib 创建散点图，其中颜色由颜色映射对象和颜色索引共同决定\n", "plt.scatter(x, y, c=color_indices, cmap=colormap)\n", "\n", "# 为图形添加标题\n", "plt.title(\"Clustering visualized in 2D using t-SNE\")\n", "\n", "# 显示图形\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**K-MEANS 聚类可视化效果，4类（官方介绍：一个专注于狗粮，一个专注于负面评论，两个专注于正面评论）。**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 使用 Embedding 进行文本搜索"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![cosine](images/cosine.png)"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["# cosine_similarity 函数计算两个嵌入向量之间的余弦相似度。\n", "def cosine_similarity(a, b):\n", "    return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/plain": ["list"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["type(df_embedded[\"embedding_vec\"][0])"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["# 定义一个名为 search_reviews 的函数，\n", "# Pandas DataFrame 产品描述，数量，以及一个 pprint 标志（默认值为 True）。\n", "def search_reviews(df, product_description, n=3, pprint=True):\n", "    product_embedding = embedding_text(product_description)\n", "    \n", "    df[\"similarity\"] = df.embedding_vec.apply(lambda x: cosine_similarity(x, product_embedding))\n", "\n", "    results = (\n", "        df.sort_values(\"similarity\", ascending=False)\n", "        .head(n)\n", "        .combined.str.replace(\"Title: \", \"\")\n", "        .str.replace(\"; Content:\", \": \")\n", "    )\n", "    if pprint:\n", "        for r in results:\n", "            print(r[:200])\n", "            print()\n", "    return results"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Good Buy:  I liked the beans. They were vacuum sealed, plump and moist. Would recommend them for any use. I personally split and stuck them in some vodka to make vanilla extract. Yum!\n", "\n", "Jamaican Blue beans:  Excellent coffee bean for roasting. Our family just purchased another 5 pounds for more roasting. Plenty of flavor and mild on acidity when roasted to a dark brown bean and befor\n", "\n", "Delicious!:  I enjoy this white beans seasoning, it gives a rich flavor to the beans I just love it, my mother in law didn't know about this Zatarain's brand and now she is traying different seasoning\n", "\n"]}], "source": ["# 使用 'delicious beans' 作为产品描述和 3 作为数量，\n", "# 调用 search_reviews 函数来查找与给定产品描述最相似的前3条评论。\n", "# 其结果被存储在 res 变量中。\n", "res = search_reviews(df_embedded, 'delicious beans', n=3)"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Healthy Dog Food:  This is a very healthy dog food. Good for their digestion. Also good for small puppies. My dog eats her required amount at every feeding.\n", "\n", "Doggy snacks:  My dog loves these snacks. However they are made in China and as far as I am concerned, suspect!!!! I found an abundance of American made ,human grade chicken dog snacks. Just Google fo\n", "\n", "Dogs Love Them!:  My Maltese and Cavalier King <PERSON> love these treats!  I feel good about feeding them a healthier treat.<br />Not made in China!\n", "\n"]}], "source": ["res = search_reviews(df_embedded, 'dog food', n=3)"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["God Awful:  As a dabbler who enjoys spanning the entire spectrum of taste, I am more than willing to try anything once.  Both as a food aficionado and a lover of bacon, I just had to pick this up.  On\n", "\n", "Disappointed:  The metal cover has severely disformed. And most of the cookies inside have been crushed into small pieces. Shopping experience is awful. I'll never buy it online again.\n", "\n", "Just Bad:  Watery and unpleasant.  Like <PERSON><PERSON><PERSON> mixed with dirty dish water.  I find it quite odd that <PERSON><PERSON><PERSON> would release a product like this.  I'm sure they can come up with a decent hot chocolate a\n", "\n", "Arrived in pieces:  Not pleased at all. When I opened the box, most of the rings were broken in pieces. A total waste of money.\n", "\n", "Awesome:  They arrived before the expected time and were of fantastic quality. Would recommend to any one looking for a awesome treat\n", "\n"]}], "source": ["res = search_reviews(df_embedded, 'awful', n=5)"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Healthy Dog Food:  This is a very healthy dog food. Good for their digestion. Also good for small puppies. My dog eats her required amount at every feeding.\n", "\n", "Doggy snacks:  My dog loves these snacks. However they are made in China and as far as I am concerned, suspect!!!! I found an abundance of American made ,human grade chicken dog snacks. Just Google fo\n", "\n", "Dogs Love Them!:  My Maltese and Cavalier King <PERSON> love these treats!  I feel good about feeding them a healthier treat.<br />Not made in China!\n", "\n"]}], "source": ["def search_reviews(df, product_description, n=3, pprint=True):\n", "    product_embedding = embedding_text(product_description)\n", "\n", "    df[\"similarity\"] = df.embedding_vec.apply(lambda x: cosine_similarity(x, product_embedding))\n", "\n", "    results = (\n", "        df.sort_values(\"similarity\", ascending=False)\n", "        .head(n)\n", "        .combined.str.replace(\"Title: \", \"\")\n", "        .str.replace(\"; Content:\", \": \")\n", "    )\n", "    if pprint:\n", "        for r in results:\n", "            print(r[:200])\n", "            print()\n", "    return results\n", "\n", "res = search_reviews(df_embedded, 'dog food', n=3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python (pytorch)", "language": "python", "name": "pytorvh"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}, "vscode": {"interpreter": {"hash": "365536dcbde60510dc9073d6b991cd35db2d9bac356a11f5b64279a5e6708b97"}}}, "nbformat": 4, "nbformat_minor": 4}