{"cells": [{"cell_type": "markdown", "id": "f177ebe3-e889-4dfc-af8c-caf12a97ed20", "metadata": {}, "source": ["# 快速入门 文字配音模型 Text-To-Speech(TTS)\n", "\n", "文字配音 API 提供了一个基于 TTS（文本到语音）模型的服务。它内置了 6 种语音，并可用于：\n", "\n", "- 朗读书面博客文章\n", "- 用多种语言制作口语音频\n", "- 使用流媒体实时提供音频输出\n", "\n", "**配音**\n", "\n", "TTS 目前支持6个不同的配音：alloy, echo, fable, onyx, nova, and shimmer\n", "\n", "发音示例参考：https://platform.openai.com/docs/guides/text-to-speech/voice-options\n", "\n", "**输出格式**\n", "\n", "默认响应格式是 \"mp3\"，但也支持其他格式，如 \"opus\"、\"aac\"、\"flac\" 和 \"pcm\"。\n", "\n", "- Opus：适用于互联网流媒体和通讯，低延迟。\n", "- AAC：用于数字音频压缩，受 YouTube、Android、iOS 的偏好。\n", "- FLAC：用于无损音频压缩，受音频爱好者喜爱，适用于存档。\n", "- WAV：未压缩的 WAV 音频，适用于低延迟应用以避免解码开销。\n", "- PCM：类似于 WAV，但包含未带头部的原始样本，24kHz（16位有符号，小端）。\n", "\n", "**支持语言**\n", "\n", "TTS 模型在语言支持方面非常广泛（针对英语发音做了优化）：\n", "\n", "阿非利卡语、阿拉伯语、亚美尼亚语、阿塞拜疆语、白俄罗斯语、波斯尼亚语、保加利亚语、加泰罗尼亚语、中文、克罗地亚语、捷克语、丹麦语、荷兰语、英语、爱沙尼亚语、芬兰语、法语、加利西亚语、德语、希腊语、希伯来语、印地语、匈牙利语、冰岛语、印度尼西亚语、意大利语、日本语、卡纳达语、哈萨克语、韩语、拉脱维亚语、立陶宛语、马其顿语、马来语、马拉提语、毛利语、尼泊尔语、挪威语、波斯语、波兰语、葡萄牙语、罗马尼亚语、俄语、塞尔维亚语、斯洛伐克语、斯洛文尼亚语、西班牙语、斯瓦希里语、瑞典语、他加禄语、泰米尔语、泰语、土耳其语、乌克兰语、乌尔都语、越南语和威尔士语。\n", "\n", "## 文字配音 API\n", "\n", "从输入文本生成音频。\n", "\n", "参数：\n", "- **model**（'tts-1' 或 'tts-1-hd'）：使用的 TTS 模型，默认为 'tts-1'。\n", "- **input**（文本）：要为其生成音频的文本，最大长度为4096个字符。\n", "- **voice**（'alloy', 'echo', 'fable', 'onyx', 'nova', 'shimmer'）：生成音频时使用的声音，支持声音预览在“文本到语音指南”中提供。\n", "- **response_format**（'mp3', 'opus', 'aac', 'flac', 'wav', 'pcm'）：音频的输出格式，默认为 'mp3'。\n", "- **speed**（0.25到4.0）：生成音频的速度，默认速度为 1.0。\n", "\n", "返回：\n", "- **audio_file**: 音频文件内容"]}, {"cell_type": "markdown", "id": "0f86615f-a8b9-4f39-bb4c-fc72719c9eed", "metadata": {}, "source": ["### 使用 TTS 给李云龙台词配音"]}, {"cell_type": "code", "execution_count": 1, "id": "18805d01-38dc-4863-b94e-17849438c20d", "metadata": {}, "outputs": [], "source": ["from openai import OpenAI\n", "client = OpenAI()\n", "\n", "speech_file_path = \"./audio/liyunlong.mp3\"\n", "\n", "# 官方示例的用法会触发 Deprecated 警告⚠️，已替换为最佳实践\n", "with client.audio.speech.with_streaming_response.create(\n", "    model=\"tts-1\",\n", "    voice=\"echo\",\n", "    input=\"二营长！你他娘的意大利炮呢？给我拉来！\"\n", ") as response:\n", "    response.stream_to_file(speech_file_path)"]}, {"cell_type": "markdown", "id": "01a765ff-b587-4a83-8e34-cdff67f3db0b", "metadata": {}, "source": ["### 使用 TTS 替换语音聊天的音色"]}, {"cell_type": "code", "execution_count": 3, "id": "fd6de1c7-b99d-4521-a2c1-78cd945f17c6", "metadata": {}, "outputs": [], "source": ["speech_file_path = \"./audio/quewang.mp3\"\n", "\n", "with client.audio.speech.with_streaming_response.create(\n", "    model=\"tts-1\",\n", "    voice=\"onyx\",\n", "    input=\"周三早上11点，雀王争霸赛，老地方23号房，经典三缺一！\"\n", ") as response:\n", "    response.stream_to_file(speech_file_path)"]}, {"cell_type": "markdown", "id": "5b1fa2cc-7d2e-4571-954a-a36a520add3c", "metadata": {}, "source": ["### 使用 TTS 播报新闻\n", "\n", "[上海F1赛车时隔五年回归 首位中国车手周冠宇：我渴望站上领奖台](https://www.bbc.com/zhongwen/simp/chinese-news-68834565)（来源：BBC 中文网）"]}, {"cell_type": "code", "execution_count": 4, "id": "66572bb3-75fc-46f3-8c98-a9a13c128a01", "metadata": {}, "outputs": [], "source": ["speech_file_path = \"./audio/boyin.mp3\"\n", "\n", "with client.audio.speech.with_streaming_response.create(\n", "    model=\"tts-1\",\n", "    voice=\"onyx\",\n", "    input=\"\"\"\n", "    上海F1赛车时隔五年回归 首位中国车手周冠宇：我渴望站上领奖台\n", "    2024年4月17日\n", "    阔别五年的世界一级方程式（F1）赛车中国站即将于2024年4月19至21日在上海国际赛车场举行，并首次有中国籍赛车手参赛。\n", "    \n", "    作为中国第一位F1赛车手，24岁的上海小伙周冠宇称自己“渴望站上领奖台”。\n", "    \"\"\"\n", ") as response:\n", "    response.stream_to_file(speech_file_path)"]}, {"cell_type": "code", "execution_count": null, "id": "d74732ef-3099-4893-abb1-fc1994ba00f4", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}