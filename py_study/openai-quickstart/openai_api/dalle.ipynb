{"cells": [{"cell_type": "markdown", "id": "80ce6f92-09b4-42b6-a6f1-2e8b1f33aec7", "metadata": {}, "source": ["## 快速入门 文生图模型 DALL·E\n", "\n", "OpenAI **Images API** 提供了三种与图像交互的方法：\n", "\n", "1. 基于文本提示生成图像（DALL·E 3 和 DALL·E 2）\n", "2. 通过模型编辑（替换）已存在图像的某些区域，根据新的文本提示创建编辑过的图像版本（仅限 DALL·E 2）\n", "3. 创建现有图像的变体（仅限 DALL·E 2）\n", "\n", "本指南主要介绍第一种文生图像的使用方法"]}, {"cell_type": "markdown", "id": "a966211c-9683-4119-ad75-5966d96bdc37", "metadata": {}, "source": ["## 使用 DALL·E 3 生成图像\n", "\n", "**关于 `DALL·E 3` 模型更新的更多内容，请参考 [OpenAI Cookbook](https://cookbook.openai.com/articles/what_is_new_with_dalle_3)**\n", "\n", "\n", "### 图像生成 API\n", "\n", "新参数：\n", "- model（'dall-e-2' 或 'dall-e-3'）：您正在使用的模型。请注意将其设置为 'dall-e-3'，因为如果为空，默认为 'dall-e-2'。\n", "- style（'natural' 或 'vivid'）：生成图像的风格。必须是 'vivid' 或 'natural' 之一。'vivid' 会使模型倾向于生成超现实和戏剧性的图像。'natural' 会使模型产生更自然、不那么超现实的图像。默认为 'vivid'。\n", "- quality（'standard' 或 'hd'）：将生成的图像质量。'hd' 创建细节更精细、整体一致性更高的图像。默认为 'standard'。\n", "\n", "其他参数：\n", "- prompt（str）：所需图像的文本描述。最大长度为1000个字符。必填字段。\n", "- n（int）：要生成的图像数量。必须在1到10之间。默认为1。对于 dall-e-3，只支持 n=1。\n", "- size（...）：生成图像的尺寸。对于 DALL·E-2 模型，必须是 256x256、512x512 或 1024x1024 之一。对于 DALL·E-3 模型，必须是 1024x1024、1792x1024 或 1024x1792 之一。\n", "- response_format（'url' 或 'b64_json'）：返回生成图像的格式。必须是 \"url\" 或 \"b64_json\" 之一。默认为 \"url\"。\n", "- user（str）：代表您的终端用户的唯一标识符，将帮助 OpenAI 监控和检测滥用。了解更多。\n", "\n"]}, {"cell_type": "code", "execution_count": 1, "id": "394827cc-6824-460d-a4c7-8d0f821ccf41", "metadata": {}, "outputs": [], "source": ["from openai import OpenAI\n", "client = OpenAI()\n", "\n", "response = client.images.generate(\n", "    model=\"dall-e-3\",\n", "    prompt=\"a white siamese cat\",\n", "    size=\"1024x1024\",\n", "    quality=\"standard\",\n", "    n=1,\n", ")\n", "\n", "image_url = response.data[0].url"]}, {"cell_type": "code", "execution_count": 2, "id": "fcbf4f70-68ab-445e-b83e-797cc7a2e427", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["https://oaidalleapiprodscus.blob.core.windows.net/private/org-2sFP0xzqD8S7a8Nl0TVYoVF5/user-fhTAqEGBJ62fdTxxDM54d1v0/img-1VzjEFD7Z5WOeew0zUc5aiPU.png?st=2024-04-23T12%3A30%3A28Z&se=2024-04-23T14%3A30%3A28Z&sp=r&sv=2021-08-06&sr=b&rscd=inline&rsct=image/png&skoid=6aaadede-4fb3-4698-a8f6-684d7786b067&sktid=a48cca56-e6da-484e-a814-9c849652bcb3&skt=2024-04-23T01%3A12%3A46Z&ske=2024-04-24T01%3A12%3A46Z&sks=b&skv=2021-08-06&sig=J2CDkSjl2odQgJnjBdkdla9KBmXYGSSHbFFx/Tuw%2Brc%3D\n"]}], "source": ["print(image_url)"]}, {"cell_type": "markdown", "id": "702cc01b-cd92-4549-8e82-059e9d8edaf2", "metadata": {}, "source": ["### 高清模式（quality=\"hd\")"]}, {"cell_type": "code", "execution_count": 3, "id": "cfaeb3ae-0a6f-4532-9990-da6a6f6ab919", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["https://oaidalleapiprodscus.blob.core.windows.net/private/org-2sFP0xzqD8S7a8Nl0TVYoVF5/user-fhTAqEGBJ62fdTxxDM54d1v0/img-RsAcOMmtjpDeO11oc4q0ESVP.png?st=2024-04-23T12%3A31%3A59Z&se=2024-04-23T14%3A31%3A59Z&sp=r&sv=2021-08-06&sr=b&rscd=inline&rsct=image/png&skoid=6aaadede-4fb3-4698-a8f6-684d7786b067&sktid=a48cca56-e6da-484e-a814-9c849652bcb3&skt=2024-04-22T23%3A04%3A49Z&ske=2024-04-23T23%3A04%3A49Z&sks=b&skv=2021-08-06&sig=7v6CtZRTgudE3eaIqNPBfUy%2B2suifnLqGWn8jtAquR0%3D\n"]}], "source": ["response = client.images.generate(\n", "    model=\"dall-e-3\",\n", "    prompt=\"a white siamese cat\",\n", "    size=\"1024x1024\",\n", "    quality=\"hd\",\n", "    n=1,\n", ")\n", "\n", "print(response.data[0].url)"]}, {"cell_type": "markdown", "id": "fec3cdee-8dd8-430c-990e-4c1db0116827", "metadata": {}, "source": ["### 自然风格(style=\"natural\")"]}, {"cell_type": "code", "execution_count": 4, "id": "7a970cc6-2e10-4e98-aaae-ddc313e4178b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["https://oaidalleapiprodscus.blob.core.windows.net/private/org-2sFP0xzqD8S7a8Nl0TVYoVF5/user-fhTAqEGBJ62fdTxxDM54d1v0/img-JmbmM55EL02zaz2OatEKGzbf.png?st=2024-04-23T12%3A32%3A23Z&se=2024-04-23T14%3A32%3A23Z&sp=r&sv=2021-08-06&sr=b&rscd=inline&rsct=image/png&skoid=6aaadede-4fb3-4698-a8f6-684d7786b067&sktid=a48cca56-e6da-484e-a814-9c849652bcb3&skt=2024-04-22T23%3A23%3A02Z&ske=2024-04-23T23%3A23%3A02Z&sks=b&skv=2021-08-06&sig=a6Pr6he%2BfAPAK4GidPeDy6xBNvVPHBJmVVQseYr0crY%3D\n"]}], "source": ["response = client.images.generate(\n", "    model=\"dall-e-3\",\n", "    prompt=\"a white siamese cat\",\n", "    size=\"1024x1024\",\n", "    quality=\"standard\",\n", "    n=1,\n", "    style=\"natural\"\n", ")\n", "print(response.data[0].url)"]}, {"cell_type": "markdown", "id": "6171e20f-0f28-4605-a7ac-3e84a7a66414", "metadata": {}, "source": ["### 戏剧风格(style=\"vivid\")"]}, {"cell_type": "code", "execution_count": 5, "id": "dde0752b-08c4-4f68-8e23-dbd536a72de5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["https://oaidalleapiprodscus.blob.core.windows.net/private/org-2sFP0xzqD8S7a8Nl0TVYoVF5/user-fhTAqEGBJ62fdTxxDM54d1v0/img-IjRfuBqKOmXXMX3t7PliMDLK.png?st=2024-04-23T12%3A32%3A41Z&se=2024-04-23T14%3A32%3A41Z&sp=r&sv=2021-08-06&sr=b&rscd=inline&rsct=image/png&skoid=6aaadede-4fb3-4698-a8f6-684d7786b067&sktid=a48cca56-e6da-484e-a814-9c849652bcb3&skt=2024-04-23T07%3A58%3A50Z&ske=2024-04-24T07%3A58%3A50Z&sks=b&skv=2021-08-06&sig=j/oCGxxtFx01Cpxoe2j3Mjy/7nromvCliTxobrURjr4%3D\n"]}], "source": ["response = client.images.generate(\n", "    model=\"dall-e-3\",\n", "    prompt=\"a white siamese cat\",\n", "    size=\"1024x1024\",\n", "    quality=\"standard\",\n", "    n=1,\n", "    style=\"vivid\"\n", ")\n", "print(response.data[0].url)"]}, {"cell_type": "code", "execution_count": null, "id": "b2eaa2a8-6466-43d0-818c-ac8cc34979f6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}