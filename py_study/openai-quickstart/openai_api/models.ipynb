{"cells": [{"cell_type": "markdown", "id": "bb04f1a2-1f87-42e6-a5ad-453eec40215f", "metadata": {}, "source": ["# Models API\n", "\n", "使用 Models API 查看和访问 OpenAI 提供的预训练大语言模型"]}, {"cell_type": "markdown", "id": "7466990e-c2ac-441c-a545-fdc122e752ca", "metadata": {}, "source": ["## List Models\n", "\n", "列出当前可用的模型，并提供每个模型的基本信息，如所有者和可用性。"]}, {"cell_type": "code", "execution_count": 6, "id": "f453ab0c-b262-4fa5-993e-b4b341e46a94", "metadata": {}, "outputs": [], "source": ["import os\n", "from openai import OpenAI\n", "client = OpenAI(api_key=\"***************************************************\",base_url=\"https://api.openai.com/v1\")\n", "\n", "models = client.models.list()"]}, {"cell_type": "code", "execution_count": 7, "id": "87dbc132-41cd-4ebc-928f-9edf6a4befcd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SyncPage[Model](data=[Model(id='gpt-4o-mini-audio-preview-2024-12-17', created=1734115920, object='model', owned_by='system'), Model(id='dall-e-3', created=1698785189, object='model', owned_by='system'), Model(id='dall-e-2', created=1698798177, object='model', owned_by='system'), Model(id='gpt-4o-audio-preview-2024-10-01', created=1727389042, object='model', owned_by='system'), Model(id='gpt-4o-audio-preview', created=1727460443, object='model', owned_by='system'), Model(id='o1-mini-2024-09-12', created=1725648979, object='model', owned_by='system'), Model(id='o1-mini', created=1725649008, object='model', owned_by='system'), Model(id='omni-moderation-latest', created=1731689265, object='model', owned_by='system'), Model(id='gpt-4o-mini-audio-preview', created=1734387424, object='model', owned_by='system'), Model(id='omni-moderation-2024-09-26', created=1732734466, object='model', owned_by='system'), Model(id='gpt-4o-mini-2024-07-18', created=1721172717, object='model', owned_by='system'), Model(id='gpt-4o-mini', created=1721172741, object='model', owned_by='system'), Model(id='gpt-4o-realtime-preview-2024-10-01', created=1727131766, object='model', owned_by='system'), Model(id='babbage-002', created=1692634615, object='model', owned_by='system'), Model(id='tts-1-hd-1106', created=1699053533, object='model', owned_by='system'), Model(id='whisper-1', created=1677532384, object='model', owned_by='openai-internal'), Model(id='text-embedding-3-large', created=1705953180, object='model', owned_by='system'), Model(id='gpt-4o-audio-preview-2024-12-17', created=1734034239, object='model', owned_by='system'), Model(id='gpt-4', created=1687882411, object='model', owned_by='openai'), Model(id='gpt-4o-2024-05-13', created=1715368132, object='model', owned_by='system'), Model(id='tts-1-hd', created=1699046015, object='model', owned_by='system'), Model(id='o1-preview', created=1725648897, object='model', owned_by='system'), Model(id='o1-preview-2024-09-12', created=1725648865, object='model', owned_by='system'), Model(id='gpt-3.5-turbo-instruct-0914', created=1694122472, object='model', owned_by='system'), Model(id='gpt-4o-mini-search-preview', created=1741391161, object='model', owned_by='system'), Model(id='tts-1-1106', created=1699053241, object='model', owned_by='system'), Model(id='davinci-002', created=1692634301, object='model', owned_by='system'), Model(id='gpt-3.5-turbo-1106', created=1698959748, object='model', owned_by='system'), Model(id='gpt-4o-search-preview', created=1741388720, object='model', owned_by='system'), Model(id='gpt-4-turbo', created=1712361441, object='model', owned_by='system'), Model(id='gpt-4o-realtime-preview-2024-12-17', created=1733945430, object='model', owned_by='system'), Model(id='gpt-3.5-turbo-instruct', created=1692901427, object='model', owned_by='system'), Model(id='gpt-4o-mini-search-preview-2025-03-11', created=1741390858, object='model', owned_by='system'), Model(id='gpt-4o-2024-11-20', created=1739331543, object='model', owned_by='system'), Model(id='gpt-3.5-turbo-0125', created=1706048358, object='model', owned_by='system'), Model(id='gpt-4o-2024-08-06', created=1722814719, object='model', owned_by='system'), Model(id='gpt-3.5-turbo', created=1677610602, object='model', owned_by='openai'), Model(id='gpt-4-turbo-2024-04-09', created=1712601677, object='model', owned_by='system'), Model(id='gpt-4o-realtime-preview', created=1727659998, object='model', owned_by='system'), Model(id='gpt-3.5-turbo-16k', created=1683758102, object='model', owned_by='openai-internal'), Model(id='gpt-4o', created=1715367049, object='model', owned_by='system'), Model(id='text-embedding-3-small', created=1705948997, object='model', owned_by='system'), Model(id='chatgpt-4o-latest', created=1723515131, object='model', owned_by='system'), Model(id='gpt-4-1106-preview', created=1698957206, object='model', owned_by='system'), Model(id='text-embedding-ada-002', created=1671217299, object='model', owned_by='openai-internal'), Model(id='gpt-4-0613', created=1686588896, object='model', owned_by='openai'), Model(id='gpt-4.5-preview', created=1740623059, object='model', owned_by='system'), Model(id='gpt-4o-mini-realtime-preview', created=1734387380, object='model', owned_by='system'), Model(id='gpt-4.5-preview-2025-02-27', created=1740623304, object='model', owned_by='system'), Model(id='gpt-4o-mini-realtime-preview-2024-12-17', created=1734112601, object='model', owned_by='system'), Model(id='gpt-4-0125-preview', created=1706037612, object='model', owned_by='system'), Model(id='gpt-4o-search-preview-2025-03-11', created=1741388170, object='model', owned_by='system'), Model(id='gpt-4-turbo-preview', created=1706037777, object='model', owned_by='system'), Model(id='tts-1', created=1681940951, object='model', owned_by='openai-internal')], object='list')\n"]}], "source": ["print(models)"]}, {"cell_type": "markdown", "id": "6b69d74e-a79d-4832-9bda-2a5b9df7e68d", "metadata": {}, "source": ["#### 查看 OpenAI 最新提供的模型 API 信息\n", "\n", "`models.data`: 目前OpenAI提供的大语言模型列表，列表中的每一项都对应着一个模型实例。\n", "\n", "以`GPT-3.5-Turbo`模型为例，解释说明各项参数：\n", "\n", "1. `created`: 这是模型创建的时间戳，单位为 Unix 时间戳（自1970年1月1日（00:00:00 GMT）以后的秒数）。\n", "2. `id`: 这是模型的唯一标识符。在这个例子中，模型的 ID 是 \"text-davinci-003\"。\n", "3. `object`: 这个字段表示的是当前对象的类型，在这个例子中，对象是 \"model\"，说明这个 JSON 对象是一个模型。\n", "4. `owned_by`: 这个字段表示的是模型的所有者，在这个例子中，模型的所有者是 \"openai-internal\"。"]}, {"cell_type": "code", "execution_count": 3, "id": "82922dd9-f38e-4ec1-8be0-361aefb99d7b", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Model(id='babbage-002', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='babbage-002', parent=None),\n", " Model(id='chatgpt-4o-latest', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='chatgpt-4o-latest', parent=None),\n", " Model(id='claude-3-5-haiku-20241022', created=1626777600, object='model', owned_by='aws', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='claude-3-5-haiku-20241022', parent=None),\n", " Model(id='claude-3-5-sonnet-20240620', created=1626777600, object='model', owned_by='aws', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='claude-3-5-sonnet-20240620', parent=None),\n", " Model(id='claude-3-5-sonnet-20241022', created=1626777600, object='model', owned_by='aws', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='claude-3-5-sonnet-20241022', parent=None),\n", " Model(id='claude-3-7-sonnet-20250219', created=1677649963, object='model', owned_by='claude-3-7-sonnet-20250219', permission=None, root='claude-3-7-sonnet-20250219', parent=None),\n", " Model(id='claude-3-7-sonnet-20250219-thinking', created=1677649963, object='model', owned_by='claude-3-7-sonnet-20250219-thinking', permission=None, root='claude-3-7-sonnet-20250219-thinking', parent=None),\n", " Model(id='claude-3-haiku-20240307', created=1626777600, object='model', owned_by='aws', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='claude-3-haiku-20240307', parent=None),\n", " Model(id='claude-3-opus-20240229', created=1626777600, object='model', owned_by='aws', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='claude-3-opus-20240229', parent=None),\n", " Model(id='claude-3-sonnet-20240229', created=1626777600, object='model', owned_by='aws', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='claude-3-sonnet-20240229', parent=None),\n", " Model(id='cld-3-5-sonnet-20240620', created=1677649963, object='model', owned_by='cld-3-5-sonnet-20240620', permission=None, root='cld-3-5-sonnet-20240620', parent=None),\n", " Model(id='cld-3-5-sonnet-20241022', created=1677649963, object='model', owned_by='cld-3-5-sonnet-20241022', permission=None, root='cld-3-5-sonnet-20241022', parent=None),\n", " Model(id='cld-3-7-sonnet-20250219', created=1677649963, object='model', owned_by='cld-3-7-sonnet-20250219', permission=None, root='cld-3-7-sonnet-20250219', parent=None),\n", " Model(id='cld-3-7-sonnet-20250219-thinking', created=1677649963, object='model', owned_by='cld-3-7-sonnet-20250219-thinking', permission=None, root='cld-3-7-sonnet-20250219-thinking', parent=None),\n", " Model(id='code-davinci-edit-001', created=1677649963, object='model', owned_by='code-davinci-edit-001', permission=None, root='code-davinci-edit-001', parent=None),\n", " Model(id='dall-e-2', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='dall-e-2', parent=None),\n", " Model(id='dall-e-3', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='dall-e-3', parent=None),\n", " Model(id='davinci-002', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='davinci-002', parent=None),\n", " Model(id='deepseek-chat', created=1626777600, object='model', owned_by='deepseek', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='deepseek-chat', parent=None),\n", " Model(id='deepseek-r1', created=1677649963, object='model', owned_by='deepseek-r1', permission=None, root='deepseek-r1', parent=None),\n", " Model(id='deepseek-reasoner', created=1677649963, object='model', owned_by='deepseek-reasoner', permission=None, root='deepseek-reasoner', parent=None),\n", " Model(id='deepseek-v3', created=1677649963, object='model', owned_by='deepseek-v3', permission=None, root='deepseek-v3', parent=None),\n", " Model(id='gemini-1.5-flash', created=1626777600, object='model', owned_by='google gemini', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gemini-1.5-flash', parent=None),\n", " Model(id='gemini-1.5-flash-002', created=1626777600, object='model', owned_by='vertexai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gemini-1.5-flash-002', parent=None),\n", " Model(id='gemini-1.5-flash-8b', created=1677649963, object='model', owned_by='gemini-1.5-flash-8b', permission=None, root='gemini-1.5-flash-8b', parent=None),\n", " Model(id='gemini-1.5-flash-latest', created=1677649963, object='model', owned_by='gemini-1.5-flash-latest', permission=None, root='gemini-1.5-flash-latest', parent=None),\n", " Model(id='gemini-1.5-pro', created=1626777600, object='model', owned_by='google gemini', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gemini-1.5-pro', parent=None),\n", " Model(id='gemini-1.5-pro-001', created=1626777600, object='model', owned_by='vertexai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gemini-1.5-pro-001', parent=None),\n", " Model(id='gemini-1.5-pro-002', created=1626777600, object='model', owned_by='vertexai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gemini-1.5-pro-002', parent=None),\n", " Model(id='gemini-1.5-pro-latest', created=1677649963, object='model', owned_by='gemini-1.5-pro-latest', permission=None, root='gemini-1.5-pro-latest', parent=None),\n", " Model(id='gemini-2.0-flash', created=1677649963, object='model', owned_by='gemini-2.0-flash', permission=None, root='gemini-2.0-flash', parent=None),\n", " Model(id='gemini-2.0-flash-exp', created=1626777600, object='model', owned_by='vertexai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gemini-2.0-flash-exp', parent=None),\n", " Model(id='gemini-2.0-flash-thinking-exp', created=1626777600, object='model', owned_by='vertexai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gemini-2.0-flash-thinking-exp', parent=None),\n", " Model(id='gemini-2.0-flash-thinking-exp-01-21', created=1626777600, object='model', owned_by='vertexai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gemini-2.0-flash-thinking-exp-01-21', parent=None),\n", " Model(id='gemini-2.0-flash-thinking-exp-1219', created=1677649963, object='model', owned_by='gemini-2.0-flash-thinking-exp-1219', permission=None, root='gemini-2.0-flash-thinking-exp-1219', parent=None),\n", " Model(id='gemini-2.0-pro-exp-02-05', created=1677649963, object='model', owned_by='gemini-2.0-pro-exp-02-05', permission=None, root='gemini-2.0-pro-exp-02-05', parent=None),\n", " Model(id='gemini-exp-1114', created=1677649963, object='model', owned_by='gemini-exp-1114', permission=None, root='gemini-exp-1114', parent=None),\n", " Model(id='gemini-exp-1121', created=1677649963, object='model', owned_by='gemini-exp-1121', permission=None, root='gemini-exp-1121', parent=None),\n", " Model(id='gemini-exp-1206', created=1677649963, object='model', owned_by='gemini-exp-1206', permission=None, root='gemini-exp-1206', parent=None),\n", " Model(id='gpt-3.5-turbo', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gpt-3.5-turbo', parent=None),\n", " Model(id='gpt-3.5-turbo-0125', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gpt-3.5-turbo-0125', parent=None),\n", " Model(id='gpt-3.5-turbo-0301', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gpt-3.5-turbo-0301', parent=None),\n", " Model(id='gpt-3.5-turbo-0613', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gpt-3.5-turbo-0613', parent=None),\n", " Model(id='gpt-3.5-turbo-1106', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gpt-3.5-turbo-1106', parent=None),\n", " Model(id='gpt-3.5-turbo-16k', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gpt-3.5-turbo-16k', parent=None),\n", " Model(id='gpt-3.5-turbo-16k-0613', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gpt-3.5-turbo-16k-0613', parent=None),\n", " Model(id='gpt-3.5-turbo-instruct', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gpt-3.5-turbo-instruct', parent=None),\n", " Model(id='gpt-4', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gpt-4', parent=None),\n", " Model(id='gpt-4-0125-preview', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gpt-4-0125-preview', parent=None),\n", " Model(id='gpt-4-0314', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gpt-4-0314', parent=None),\n", " Model(id='gpt-4-0613', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gpt-4-0613', parent=None),\n", " Model(id='gpt-4-1106-preview', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gpt-4-1106-preview', parent=None),\n", " Model(id='gpt-4-32k', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gpt-4-32k', parent=None),\n", " Model(id='gpt-4-32k-0314', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gpt-4-32k-0314', parent=None),\n", " Model(id='gpt-4-32k-0613', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gpt-4-32k-0613', parent=None),\n", " Model(id='gpt-4-all', created=1677649963, object='model', owned_by='gpt-4-all', permission=None, root='gpt-4-all', parent=None),\n", " Model(id='gpt-4-gizmo-*', created=1677649963, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gpt-4-gizmo-*', parent=None),\n", " Model(id='gpt-4-turbo', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gpt-4-turbo', parent=None),\n", " Model(id='gpt-4-turbo-2024-04-09', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gpt-4-turbo-2024-04-09', parent=None),\n", " Model(id='gpt-4-turbo-preview', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gpt-4-turbo-preview', parent=None),\n", " Model(id='gpt-4-vision-preview', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gpt-4-vision-preview', parent=None),\n", " Model(id='gpt-4.5-preview', created=1677649963, object='model', owned_by='gpt-4.5-preview', permission=None, root='gpt-4.5-preview', parent=None),\n", " Model(id='gpt-4.5-preview-2025-02-27', created=1677649963, object='model', owned_by='gpt-4.5-preview-2025-02-27', permission=None, root='gpt-4.5-preview-2025-02-27', parent=None),\n", " Model(id='gpt-4o', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gpt-4o', parent=None),\n", " Model(id='gpt-4o-2024-05-13', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gpt-4o-2024-05-13', parent=None),\n", " Model(id='gpt-4o-2024-08-06', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gpt-4o-2024-08-06', parent=None),\n", " Model(id='gpt-4o-2024-11-20', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gpt-4o-2024-11-20', parent=None),\n", " Model(id='gpt-4o-all', created=1677649963, object='model', owned_by='gpt-4o-all', permission=None, root='gpt-4o-all', parent=None),\n", " Model(id='gpt-4o-audio-preview', created=1677649963, object='model', owned_by='gpt-4o-audio-preview', permission=None, root='gpt-4o-audio-preview', parent=None),\n", " Model(id='gpt-4o-audio-preview-2024-10-01', created=1677649963, object='model', owned_by='gpt-4o-audio-preview-2024-10-01', permission=None, root='gpt-4o-audio-preview-2024-10-01', parent=None),\n", " Model(id='gpt-4o-gizmo-*', created=1677649963, object='model', owned_by='gpt-4o-gizmo-*', permission=None, root='gpt-4o-gizmo-*', parent=None),\n", " Model(id='gpt-4o-lite', created=1677649963, object='model', owned_by='gpt-4o-lite', permission=None, root='gpt-4o-lite', parent=None),\n", " Model(id='gpt-4o-mini', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gpt-4o-mini', parent=None),\n", " Model(id='gpt-4o-mini-2024-07-18', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gpt-4o-mini-2024-07-18', parent=None),\n", " Model(id='gpt-4o-mini-audio-preview', created=1677649963, object='model', owned_by='gpt-4o-mini-audio-preview', permission=None, root='gpt-4o-mini-audio-preview', parent=None),\n", " Model(id='gpt-4o-mini-audio-preview-2024-12-17', created=1677649963, object='model', owned_by='gpt-4o-mini-audio-preview-2024-12-17', permission=None, root='gpt-4o-mini-audio-preview-2024-12-17', parent=None),\n", " Model(id='gpt-4o-realtime-preview', created=1677649963, object='model', owned_by='gpt-4o-realtime-preview', permission=None, root='gpt-4o-realtime-preview', parent=None),\n", " Model(id='gpt-4o-realtime-preview-2024-10-01', created=1677649963, object='model', owned_by='gpt-4o-realtime-preview-2024-10-01', permission=None, root='gpt-4o-realtime-preview-2024-10-01', parent=None),\n", " Model(id='grok-2', created=1677649963, object='model', owned_by='grok-2', permission=None, root='grok-2', parent=None),\n", " Model(id='grok-2-1212', created=1677649963, object='model', owned_by='grok-2-1212', permission=None, root='grok-2-1212', parent=None),\n", " Model(id='grok-2-imageGen', created=1677649963, object='model', owned_by='grok-2-imageGen', permission=None, root='grok-2-imageGen', parent=None),\n", " Model(id='grok-2-search', created=1677649963, object='model', owned_by='grok-2-search', permission=None, root='grok-2-search', parent=None),\n", " Model(id='grok-2-vision-1212', created=1677649963, object='model', owned_by='grok-2-vision-1212', permission=None, root='grok-2-vision-1212', parent=None),\n", " Model(id='grok-3', created=1677649963, object='model', owned_by='grok-3', permission=None, root='grok-3', parent=None),\n", " Model(id='grok-3-deepsearch', created=1677649963, object='model', owned_by='grok-3-deepsearch', permission=None, root='grok-3-deepsearch', parent=None),\n", " Model(id='grok-3-imageGen', created=1677649963, object='model', owned_by='grok-3-imageGen', permission=None, root='grok-3-imageGen', parent=None),\n", " Model(id='grok-3-reasoning', created=1677649963, object='model', owned_by='grok-3-reasoning', permission=None, root='grok-3-reasoning', parent=None),\n", " Model(id='grok-3-search', created=1677649963, object='model', owned_by='grok-3-search', permission=None, root='grok-3-search', parent=None),\n", " Model(id='grok-beta', created=1626777600, object='model', owned_by='xai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='grok-beta', parent=None),\n", " Model(id='grok-vision-beta', created=1677649963, object='model', owned_by='grok-vision-beta', permission=None, root='grok-vision-beta', parent=None),\n", " Model(id='ministral-3b-latest', created=1677649963, object='model', owned_by='ministral-3b-latest', permission=None, root='ministral-3b-latest', parent=None),\n", " Model(id='ministral-8b-latest', created=1677649963, object='model', owned_by='ministral-8b-latest', permission=None, root='ministral-8b-latest', parent=None),\n", " Model(id='mistral-embed', created=1626777600, object='model', owned_by='mistralai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='mistral-embed', parent=None),\n", " Model(id='mistral-large-2502-15-1-rc2', created=1677649963, object='model', owned_by='mistral-large-2502-15-1-rc2', permission=None, root='mistral-large-2502-15-1-rc2', parent=None),\n", " Model(id='mistral-large-latest', created=1626777600, object='model', owned_by='mistralai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='mistral-large-latest', parent=None),\n", " Model(id='mistral-large-pixtral-2411', created=1677649963, object='model', owned_by='mistral-large-pixtral-2411', permission=None, root='mistral-large-pixtral-2411', parent=None),\n", " Model(id='mistral-moderation-latest', created=1677649963, object='model', owned_by='mistral-moderation-latest', permission=None, root='mistral-moderation-latest', parent=None),\n", " Model(id='mistral-small', created=1677649963, object='model', owned_by='mistral-small', permission=None, root='mistral-small', parent=None),\n", " Model(id='mistral-small-2501', created=1677649963, object='model', owned_by='mistral-small-2501', permission=None, root='mistral-small-2501', parent=None),\n", " Model(id='mistral-small-latest', created=1626777600, object='model', owned_by='mistralai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='mistral-small-latest', parent=None),\n", " Model(id='moonshot-v1-128k', created=1626777600, object='model', owned_by='moonshot', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='moonshot-v1-128k', parent=None),\n", " Model(id='moonshot-v1-128k-vision-preview', created=1677649963, object='model', owned_by='moonshot-v1-128k-vision-preview', permission=None, root='moonshot-v1-128k-vision-preview', parent=None),\n", " Model(id='moonshot-v1-32k', created=1626777600, object='model', owned_by='moonshot', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='moonshot-v1-32k', parent=None),\n", " Model(id='moonshot-v1-32k-vision-preview', created=1677649963, object='model', owned_by='moonshot-v1-32k-vision-preview', permission=None, root='moonshot-v1-32k-vision-preview', parent=None),\n", " Model(id='moonshot-v1-8k', created=1626777600, object='model', owned_by='moonshot', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='moonshot-v1-8k', parent=None),\n", " Model(id='moonshot-v1-8k-vision-preview', created=1677649963, object='model', owned_by='moonshot-v1-8k-vision-preview', permission=None, root='moonshot-v1-8k-vision-preview', parent=None),\n", " Model(id='moonshot-v1-auto', created=1677649963, object='model', owned_by='moonshot-v1-auto', permission=None, root='moonshot-v1-auto', parent=None),\n", " Model(id='o1', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='o1', parent=None),\n", " Model(id='o1-2024-12-17', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='o1-2024-12-17', parent=None),\n", " Model(id='o1-mini', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='o1-mini', parent=None),\n", " Model(id='o1-mini-2024-09-12', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='o1-mini-2024-09-12', parent=None),\n", " Model(id='o1-preview', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='o1-preview', parent=None),\n", " Model(id='o1-preview-2024-09-12', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='o1-preview-2024-09-12', parent=None),\n", " Model(id='o3-mini', created=1677649963, object='model', owned_by='o3-mini', permission=None, root='o3-mini', parent=None),\n", " Model(id='o3-mini-2025-01-31', created=1677649963, object='model', owned_by='o3-mini-2025-01-31', permission=None, root='o3-mini-2025-01-31', parent=None),\n", " Model(id='omni-moderation-2024-09-26', created=1677649963, object='model', owned_by='omni-moderation-2024-09-26', permission=None, root='omni-moderation-2024-09-26', parent=None),\n", " Model(id='omni-moderation-latest', created=1677649963, object='model', owned_by='omni-moderation-latest', permission=None, root='omni-moderation-latest', parent=None),\n", " Model(id='pixtral-12b', created=1677649963, object='model', owned_by='pixtral-12b', permission=None, root='pixtral-12b', parent=None),\n", " Model(id='pixtral-12b-latest', created=1677649963, object='model', owned_by='pixtral-12b-latest', permission=None, root='pixtral-12b-latest', parent=None),\n", " Model(id='pixtral-large-2411', created=1677649963, object='model', owned_by='pixtral-large-2411', permission=None, root='pixtral-large-2411', parent=None),\n", " Model(id='pixtral-large-latest', created=1677649963, object='model', owned_by='pixtral-large-latest', permission=None, root='pixtral-large-latest', parent=None),\n", " Model(id='qwen-max', created=1626777600, object='model', owned_by='ali', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='qwen-max', parent=None),\n", " Model(id='qwen-max-0125', created=1677649963, object='model', owned_by='qwen-max-0125', permission=None, root='qwen-max-0125', parent=None),\n", " Model(id='qwen-max-2025-01-25', created=1677649963, object='model', owned_by='qwen-max-2025-01-25', permission=None, root='qwen-max-2025-01-25', parent=None),\n", " Model(id='qwen-max-latest', created=1626777600, object='model', owned_by='ali', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='qwen-max-latest', parent=None),\n", " Model(id='qwen-plus', created=1626777600, object='model', owned_by='ali', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='qwen-plus', parent=None),\n", " Model(id='qwen-plus-0125', created=1677649963, object='model', owned_by='qwen-plus-0125', permission=None, root='qwen-plus-0125', parent=None),\n", " Model(id='qwen-plus-2025-01-25', created=1677649963, object='model', owned_by='qwen-plus-2025-01-25', permission=None, root='qwen-plus-2025-01-25', parent=None),\n", " Model(id='qwen-plus-latest', created=1626777600, object='model', owned_by='ali', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='qwen-plus-latest', parent=None),\n", " Model(id='qwen-turbo', created=1626777600, object='model', owned_by='ali', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='qwen-turbo', parent=None),\n", " Model(id='qwen-turbo-2024-11-01', created=1677649963, object='model', owned_by='qwen-turbo-2024-11-01', permission=None, root='qwen-turbo-2024-11-01', parent=None),\n", " Model(id='qwen-turbo-latest', created=1626777600, object='model', owned_by='ali', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='qwen-turbo-latest', parent=None),\n", " Model(id='text-ada-001', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='text-ada-001', parent=None),\n", " Model(id='text-babbage-001', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='text-babbage-001', parent=None),\n", " Model(id='text-curie-001', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='text-curie-001', parent=None),\n", " Model(id='text-davinci-002', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='text-davinci-002', parent=None),\n", " Model(id='text-davinci-003', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='text-davinci-003', parent=None),\n", " Model(id='text-davinci-edit-001', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='text-davinci-edit-001', parent=None),\n", " Model(id='text-embedding-3-large', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='text-embedding-3-large', parent=None),\n", " Model(id='text-embedding-3-small', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='text-embedding-3-small', parent=None),\n", " Model(id='text-embedding-ada-002', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='text-embedding-ada-002', parent=None),\n", " Model(id='text-moderation-007', created=1677649963, object='model', owned_by='text-moderation-007', permission=None, root='text-moderation-007', parent=None),\n", " Model(id='text-moderation-latest', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='text-moderation-latest', parent=None),\n", " Model(id='text-moderation-stable', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='text-moderation-stable', parent=None),\n", " Model(id='tts-1', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='tts-1', parent=None),\n", " Model(id='tts-1-1106', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='tts-1-1106', parent=None),\n", " Model(id='tts-1-hd', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='tts-1-hd', parent=None),\n", " Model(id='tts-1-hd-1106', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='tts-1-hd-1106', parent=None),\n", " Model(id='whisper-1', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='whisper-1', parent=None)]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["models.data"]}, {"cell_type": "markdown", "id": "774e3a12-01f6-44ad-8829-03510f5d9df6", "metadata": {}, "source": ["### 获取模型 ID 列表"]}, {"cell_type": "code", "execution_count": 4, "id": "dd351597-b055-4d30-aa0b-b5e468f7b924", "metadata": {}, "outputs": [{"data": {"text/plain": ["'babbage-002'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["models.data[0].id"]}, {"cell_type": "code", "execution_count": 5, "id": "fdb8752e-bcb8-4732-a037-1835861c20a7", "metadata": {}, "outputs": [], "source": ["model_list = [model.id for model in models.data]"]}, {"cell_type": "code", "execution_count": 6, "id": "3372f33d-4302-4fc6-9eac-e1d34cf216be", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['babbage-002', 'chatgpt-4o-latest', 'claude-3-5-haiku-20241022', 'claude-3-5-sonnet-20240620', 'claude-3-5-sonnet-20241022', 'claude-3-7-sonnet-20250219', 'claude-3-7-sonnet-20250219-thinking', 'claude-3-haiku-20240307', 'claude-3-opus-20240229', 'claude-3-sonnet-20240229', 'cld-3-5-sonnet-20240620', 'cld-3-5-sonnet-20241022', 'cld-3-7-sonnet-20250219', 'cld-3-7-sonnet-20250219-thinking', 'code-davinci-edit-001', 'dall-e-2', 'dall-e-3', 'davinci-002', 'deepseek-chat', 'deepseek-r1', 'deepseek-reasoner', 'deepseek-v3', 'gemini-1.5-flash', 'gemini-1.5-flash-002', 'gemini-1.5-flash-8b', 'gemini-1.5-flash-latest', 'gemini-1.5-pro', 'gemini-1.5-pro-001', 'gemini-1.5-pro-002', 'gemini-1.5-pro-latest', 'gemini-2.0-flash', 'gemini-2.0-flash-exp', 'gemini-2.0-flash-thinking-exp', 'gemini-2.0-flash-thinking-exp-01-21', 'gemini-2.0-flash-thinking-exp-1219', 'gemini-2.0-pro-exp-02-05', 'gemini-exp-1114', 'gemini-exp-1121', 'gemini-exp-1206', 'gpt-3.5-turbo', 'gpt-3.5-turbo-0125', 'gpt-3.5-turbo-0301', 'gpt-3.5-turbo-0613', 'gpt-3.5-turbo-1106', 'gpt-3.5-turbo-16k', 'gpt-3.5-turbo-16k-0613', 'gpt-3.5-turbo-instruct', 'gpt-4', 'gpt-4-0125-preview', 'gpt-4-0314', 'gpt-4-0613', 'gpt-4-1106-preview', 'gpt-4-32k', 'gpt-4-32k-0314', 'gpt-4-32k-0613', 'gpt-4-all', 'gpt-4-gizmo-*', 'gpt-4-turbo', 'gpt-4-turbo-2024-04-09', 'gpt-4-turbo-preview', 'gpt-4-vision-preview', 'gpt-4.5-preview', 'gpt-4.5-preview-2025-02-27', 'gpt-4o', 'gpt-4o-2024-05-13', 'gpt-4o-2024-08-06', 'gpt-4o-2024-11-20', 'gpt-4o-all', 'gpt-4o-audio-preview', 'gpt-4o-audio-preview-2024-10-01', 'gpt-4o-gizmo-*', 'gpt-4o-lite', 'gpt-4o-mini', 'gpt-4o-mini-2024-07-18', 'gpt-4o-mini-audio-preview', 'gpt-4o-mini-audio-preview-2024-12-17', 'gpt-4o-realtime-preview', 'gpt-4o-realtime-preview-2024-10-01', 'grok-2', 'grok-2-1212', 'grok-2-imageGen', 'grok-2-search', 'grok-2-vision-1212', 'grok-3', 'grok-3-deepsearch', 'grok-3-imageGen', 'grok-3-reasoning', 'grok-3-search', 'grok-beta', 'grok-vision-beta', 'ministral-3b-latest', 'ministral-8b-latest', 'mistral-embed', 'mistral-large-2502-15-1-rc2', 'mistral-large-latest', 'mistral-large-pixtral-2411', 'mistral-moderation-latest', 'mistral-small', 'mistral-small-2501', 'mistral-small-latest', 'moonshot-v1-128k', 'moonshot-v1-128k-vision-preview', 'moonshot-v1-32k', 'moonshot-v1-32k-vision-preview', 'moonshot-v1-8k', 'moonshot-v1-8k-vision-preview', 'moonshot-v1-auto', 'o1', 'o1-2024-12-17', 'o1-mini', 'o1-mini-2024-09-12', 'o1-preview', 'o1-preview-2024-09-12', 'o3-mini', 'o3-mini-2025-01-31', 'omni-moderation-2024-09-26', 'omni-moderation-latest', 'pixtral-12b', 'pixtral-12b-latest', 'pixtral-large-2411', 'pixtral-large-latest', 'qwen-max', 'qwen-max-0125', 'qwen-max-2025-01-25', 'qwen-max-latest', 'qwen-plus', 'qwen-plus-0125', 'qwen-plus-2025-01-25', 'qwen-plus-latest', 'qwen-turbo', 'qwen-turbo-2024-11-01', 'qwen-turbo-latest', 'text-ada-001', 'text-babbage-001', 'text-curie-001', 'text-davinci-002', 'text-davinci-003', 'text-davinci-edit-001', 'text-embedding-3-large', 'text-embedding-3-small', 'text-embedding-ada-002', 'text-moderation-007', 'text-moderation-latest', 'text-moderation-stable', 'tts-1', 'tts-1-1106', 'tts-1-hd', 'tts-1-hd-1106', 'whisper-1']\n"]}], "source": ["print(model_list)"]}, {"cell_type": "markdown", "id": "75d2a270-949c-4ad2-a568-e254e6231333", "metadata": {}, "source": ["## Retrieve Model\n", "\n", "根据前面查询到当前支持的模型ID列表，获取指定模型实例，如`gpt-3.5-turbo`。"]}, {"cell_type": "code", "execution_count": 7, "id": "6fd7eca9-ed5a-4bca-8f9c-a0bf36177d31", "metadata": {}, "outputs": [], "source": ["from openai import OpenAI\n", "client = OpenAI()\n", "\n", "# 将模型 ID 传入 retrieve 接口\n", "gpt_3 = client.models.retrieve(\"gpt-3.5-turbo\")"]}, {"cell_type": "code", "execution_count": 8, "id": "51078b58-67db-467a-b09f-ef40562f1fad", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model(id='gpt-3.5-turbo', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gpt-3.5-turbo', parent=None)\n"]}], "source": ["print(gpt_3)"]}, {"cell_type": "markdown", "id": "67bd0916-d771-4745-85bb-28be33806d89", "metadata": {}, "source": ["### 获取指定模型，如 GPT-4V"]}, {"cell_type": "code", "execution_count": 9, "id": "2ea16de8-add9-4ad4-b5b9-ebb959d7ab7f", "metadata": {}, "outputs": [{"data": {"text/plain": ["Model(id='gpt-4-vision-preview', created=1626777600, object='model', owned_by='openai', permission=[{'id': 'modelperm-LwHkVFn8AcMItP432fKKDIKJ', 'object': 'model_permission', 'created': 1626777600, 'allow_create_engine': True, 'allow_sampling': True, 'allow_logprobs': True, 'allow_search_indices': False, 'allow_view': True, 'allow_fine_tuning': False, 'organization': '*', 'group': None, 'is_blocking': False}], root='gpt-4-vision-preview', parent=None)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["client.models.retrieve(\"gpt-4-vision-preview\")"]}, {"cell_type": "code", "execution_count": null, "id": "fa107114-28cd-435a-a4b0-c9ceabddf415", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "028f4f7e-3be1-44d7-bfcd-c65a13e85093", "metadata": {}, "source": ["# 文本内容补全初探（Completions API）[Legacy]\n", "\n", "使用 Completions API 实现各类文本生成任务\n", "\n", "\n", "主要请求参数说明：\n", "\n", "\n", "- **`model`** （string，必填）\n", "\n", "  要使用的模型的 ID。可以参考 **模型端点兼容性表**。\n", "\n", "- **`prompt`** （string or array，必填，Defaults to ）\n", "\n", "  生成补全的提示，编码为字符串、字符串数组、token数组或token数组数组。\n", "\n", "  注意，这是模型在训练过程中看到的文档分隔符，所以如果没有指定提示符，模型将像从新文档的开头一样生成。\n", "\n", "- **`stream`** （boolean，选填，默认 false）\n", "\n", "  当它设置为 true 时，API 会以 SSE（ Server Side Event ）方式返回内容，即会不断地输出内容直到完成响应，流通过 `data: [DONE]` 消息终止。\n", "\n", "- **`max_tokens`** （integer，选填，默认是 16）\n", "\n", "  补全时要生成的最大 token 数。\n", "\n", "  提示 `max_tokens` 的 token 计数不能超过模型的上下文长度。大多数模型的上下文长度为 2048 个token（最新模型除外，它支持 4096）\n", "\n", "- **`temperature`** （number，选填，默认是1）\n", "\n", "  使用哪个采样温度，在 **0和2之间**。\n", "\n", "  较高的值，如0.8会使输出更随机，而较低的值，如0.2会使其更加集中和确定性。\n", "\n", "  通常建议修改这个（`temperature` ）或 `top_p` 但两者不能同时存在，二选一。\n", "\n", "- **`n`** （integer，选填，默认为 1）\n", "\n", "  每个 `prompt` 生成的补全次数。\n", "\n", "  注意：由于此参数会生成许多补全，因此它会快速消耗token配额。小心使用，并确保对 `max_tokens` 和 `stop` 进行合理的设置。\n", "\n", "\n", "## 生成英文文本"]}, {"cell_type": "code", "execution_count": 10, "id": "0526acb1-5741-48b6-ae64-917ad0d064b4", "metadata": {}, "outputs": [], "source": ["import os\n", "from openai import OpenAI\n", "\n", "client = OpenAI()\n", "\n", "data = client.completions.create(\n", "  model=\"gpt-3.5-turbo-instruct\",\n", "  prompt=\"Say this is a test\",\n", "  max_tokens=7,\n", "  temperature=0\n", ")"]}, {"cell_type": "code", "execution_count": 11, "id": "af7a8d1f-c2f7-421d-bd0d-0890d06a0c34", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Completion(id='chatcmpl-89Cw39lWdKcnEWMrzCo9gE8FrEnSI', choices=[CompletionChoice(finish_reason='stop', index=0, logprobs=None, text=None, message={})], created=1740967345, model='gpt-3.5-turbo-instruct', object='chat.completion', system_fingerprint='fp_0165350fbb', usage=CompletionUsage(completion_tokens=6, prompt_tokens=5, total_tokens=11, completion_tokens_details=None, prompt_tokens_details=None))\n"]}], "source": ["print(data)"]}, {"cell_type": "code", "execution_count": 12, "id": "63cddd0f-a8f3-4f62-891a-8146306a5d95", "metadata": {}, "outputs": [], "source": ["text = data.choices[0].text"]}, {"cell_type": "code", "execution_count": 13, "id": "49cbd498-bfa5-4d11-a6ee-30822b24dcc8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["None\n"]}], "source": ["print(text)"]}, {"cell_type": "code", "execution_count": null, "id": "c43d83bd-375f-4256-925a-e894d0e155a0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 21, "id": "159f1bf9-1604-414c-af74-45b22c33a7f9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ChatCompletion(id='chatcmpl-B6pda3wOvKPeE4NWLqXhFZLyYweYh', choices=[Choice(finish_reason='length', index=0, logprobs=None, message=ChatCompletionMessage(content='This is a test. How can', refusal=None, role='assistant', audio=None, function_call=None, tool_calls=None))], created=1740968162, model='gpt-4o-mini-2024-07-18', object='chat.completion', service_tier='default', system_fingerprint='fp_06737a9306', usage=CompletionUsage(completion_tokens=7, prompt_tokens=12, total_tokens=19, completion_tokens_details=CompletionTokensDetails(accepted_prediction_tokens=0, audio_tokens=0, reasoning_tokens=0, rejected_prediction_tokens=0), prompt_tokens_details=PromptTokensDetails(audio_tokens=0, cached_tokens=0)))\n", "This is a test. How can\n"]}], "source": ["### 【新增】/v1/completions 调用也即将过期； 更新为 /v1/chat/completions ，使用gpt-4o-mini模型\n", "\n", "from openai import OpenAI\n", "\n", "client = OpenAI()\n", "\n", "data = client.chat.completions.create(\n", "    model=\"gpt-4o-mini\",\n", "    max_tokens=7,\n", "temperature=0,\n", "    messages=[\n", "        {\"role\": \"user\", \"content\": \"Say this is a test\"}\n", "    ]\n", ")\n", "print(data)\n", "# 内容输出数据结构，和/v1/completions 接口有变化，相应调整\n", "text = data.choices[0].message.content\n", "print(text)"]}, {"cell_type": "markdown", "id": "5c79be11-b2b4-4580-ad10-b7111cb950ea", "metadata": {}, "source": ["## 生成中文文本\n", "\n", "调整 `max_tokens` "]}, {"cell_type": "code", "execution_count": 14, "id": "3e3a4018-7c72-49a1-942b-167c3941fb96", "metadata": {}, "outputs": [], "source": ["data = client.completions.create(\n", "  model=\"gpt-3.5-turbo-instruct\",\n", "  prompt=\"讲10个给程序员听得笑话\",\n", "  max_tokens=1000,\n", "  temperature=0.5\n", ")"]}, {"cell_type": "code", "execution_count": 15, "id": "f04959ba-5781-4e9e-bdfb-a55f0698acd9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["None\n"]}], "source": ["text = data.choices[0].text\n", "print(text)"]}, {"cell_type": "code", "execution_count": null, "id": "ad54c353-64bf-44dc-ad55-1d2c4a783443", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 22, "id": "faae1644-8877-4ce8-9e38-0c3bff2b44f6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ChatCompletion(id='chatcmpl-B6pf33o5CSpvjjjUhvV0njVNbXSnm', choices=[Choice(finish_reason='stop', index=0, logprobs=None, message=ChatCompletionMessage(content='当然可以！以下是10个适合程序员的笑话：\\n\\n1. **为什么程序员喜欢在沙滩上工作？**\\n   因为他们喜欢处理“海量数据”！\\n\\n2. **程序员的最爱饮料是什么？**\\n   Java！\\n\\n3. **有两个程序员走进酒吧，喝了两杯。**\\n   他们决定写个程序来记录他们的饮酒量，结果他们只喝了一个杯子。\\n\\n4. **为什么程序员总是混淆万圣节和圣诞节？**\\n   因为Oct 31 = Dec 25！\\n\\n5. **程序员的女朋友跟他分手了，为什么？**\\n   因为她觉得他总是“调试”她的情绪。\\n\\n6. **程序员最怕什么？**\\n   代码的“bug”！尤其是那些“隐藏的”！\\n\\n7. **为什么程序员喜欢看星星？**\\n   因为他们总是在寻找“星际”错误！\\n\\n8. **程序员的工作与魔术师有什么相似之处？**\\n   他们都能把“错误”变成“惊喜”！\\n\\n9. **为什么程序员不喜欢户外活动？**\\n   因为他们讨厌“离线”！\\n\\n10. **程序员的座右铭是什么？**\\n   “如果一切都失败了，试试重启！”\\n\\n希望这些笑话能给你带来欢乐！', refusal=None, role='assistant', audio=None, function_call=None, tool_calls=None))], created=1740968253, model='gpt-4o-mini-2024-07-18', object='chat.completion', service_tier='default', system_fingerprint='fp_06737a9306', usage=CompletionUsage(completion_tokens=319, prompt_tokens=17, total_tokens=336, completion_tokens_details=CompletionTokensDetails(accepted_prediction_tokens=0, audio_tokens=0, reasoning_tokens=0, rejected_prediction_tokens=0), prompt_tokens_details=PromptTokensDetails(audio_tokens=0, cached_tokens=0)))\n", "当然可以！以下是10个适合程序员的笑话：\n", "\n", "1. **为什么程序员喜欢在沙滩上工作？**\n", "   因为他们喜欢处理“海量数据”！\n", "\n", "2. **程序员的最爱饮料是什么？**\n", "   Java！\n", "\n", "3. **有两个程序员走进酒吧，喝了两杯。**\n", "   他们决定写个程序来记录他们的饮酒量，结果他们只喝了一个杯子。\n", "\n", "4. **为什么程序员总是混淆万圣节和圣诞节？**\n", "   因为Oct 31 = Dec 25！\n", "\n", "5. **程序员的女朋友跟他分手了，为什么？**\n", "   因为她觉得他总是“调试”她的情绪。\n", "\n", "6. **程序员最怕什么？**\n", "   代码的“bug”！尤其是那些“隐藏的”！\n", "\n", "7. **为什么程序员喜欢看星星？**\n", "   因为他们总是在寻找“星际”错误！\n", "\n", "8. **程序员的工作与魔术师有什么相似之处？**\n", "   他们都能把“错误”变成“惊喜”！\n", "\n", "9. **为什么程序员不喜欢户外活动？**\n", "   因为他们讨厌“离线”！\n", "\n", "10. **程序员的座右铭是什么？**\n", "   “如果一切都失败了，试试重启！”\n", "\n", "希望这些笑话能给你带来欢乐！\n"]}], "source": ["### 【新增】/v1/completions 调用也即将过期； 更新为 /v1/chat/completions ，使用gpt-4o-mini模型\n", "\n", "from openai import OpenAI\n", "\n", "client = OpenAI()\n", "\n", "data = client.chat.completions.create(\n", "    model=\"gpt-4o-mini\",\n", "    max_tokens=1000,\n", "    temperature=0.5,\n", "    messages=[\n", "        {\"role\": \"user\", \"content\": \"讲10个给程序员听得笑话\"}\n", "    ]\n", ")\n", "print(data)\n", "# 内容输出数据结构，和/v1/completions 接口有变化，相应调整\n", "text = data.choices[0].message.content\n", "print(text)"]}, {"cell_type": "markdown", "id": "3e5bdaff-764f-4fbf-b2c2-a8afbdaca8c6", "metadata": {}, "source": ["## 生成 Python 代码，并执行和验证\n", "\n", "以面试中考察的典型的试题 `快速排序` 为例"]}, {"cell_type": "code", "execution_count": 16, "id": "3d5a6a51-57cd-4890-8978-b07efa2c28e2", "metadata": {}, "outputs": [], "source": ["data = client.completions.create(\n", "  model=\"gpt-3.5-turbo-instruct\",\n", "  prompt=\"生成可执行的快速排序 Python 代码\",\n", "  max_tokens=1000,\n", "  temperature=0\n", ")\n"]}, {"cell_type": "code", "execution_count": 17, "id": "45e24e7d-72d7-491a-a60f-0e8031a6fc7d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["None\n"]}], "source": ["text = data.choices[0].text\n", "print(text)"]}, {"cell_type": "code", "execution_count": 44, "id": "97817353-3c8d-4dcd-ab52-844ae9ba35dc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "【完整内容】：\n", " ChatCompletion(id='chatcmpl-89CwCU6NpvGOnA34KTQPE5V8cbXMG', choices=[Choice(finish_reason='stop', index=0, logprobs=None, message=ChatCompletionMessage(content='```python\\ndef quick_sort(arr):\\n    if len(arr) <= 1:\\n        return arr\\n    pivot = arr[len(arr) // 2]\\n    left = [x for x in arr if x < pivot]\\n    middle = [x for x in arr if x == pivot]\\n    right = [x for x in arr if x > pivot]\\n    return quick_sort(left) + middle + quick_sort(right)\\n```', refusal=None, role='assistant', audio=None, function_call=None, tool_calls=None))], created=1740969097, model='gpt-4o-mini', object='chat.completion', service_tier=None, system_fingerprint='fp_b705f0c291', usage=CompletionUsage(completion_tokens=88, prompt_tokens=26, total_tokens=114, completion_tokens_details=CompletionTokensDetails(accepted_prediction_tokens=None, audio_tokens=None, reasoning_tokens=None, rejected_prediction_tokens=None), prompt_tokens_details=PromptTokensDetails(audio_tokens=None, cached_tokens=None)))\n", "\n", "【文本内容】：\n", " ```python\n", "def quick_sort(arr):\n", "    if len(arr) <= 1:\n", "        return arr\n", "    pivot = arr[len(arr) // 2]\n", "    left = [x for x in arr if x < pivot]\n", "    middle = [x for x in arr if x == pivot]\n", "    right = [x for x in arr if x > pivot]\n", "    return quick_sort(left) + middle + quick_sort(right)\n", "```\n"]}], "source": ["### 【新增】/v1/completions 调用也即将过期； 更新为 /v1/chat/completions ，使用gpt-4o-mini模型\n", "\n", "from openai import OpenAI\n", "\n", "client = OpenAI()\n", "\n", "data = client.chat.completions.create(\n", "    model=\"gpt-4o-mini\",\n", "    max_tokens=1000,\n", "    temperature=0,\n", "    messages=[\n", "        {\"role\": \"user\", \"content\": \"生成可执行的快速排序 Python 代码，只输出代码块内容，函数名quick_sort\"}\n", "    ]\n", ")\n", "print(\"\\n【完整内容】：\\n\",data)\n", "# 内容输出数据结构，和/v1/completions 接口有变化，相应调整\n", "text = data.choices[0].message.content\n", "print(\"\\n【文本内容】：\\n\",text)"]}, {"cell_type": "code", "execution_count": 45, "id": "88f30239-d07a-48eb-ba76-7a441aeffb54", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "【清理后的文本内容】：\n", " def quick_sort(arr):\n", "    if len(arr) <= 1:\n", "        return arr\n", "    pivot = arr[len(arr) // 2]\n", "    left = [x for x in arr if x < pivot]\n", "    middle = [x for x in arr if x == pivot]\n", "    right = [x for x in arr if x > pivot]\n", "    return quick_sort(left) + middle + quick_sort(right)\n"]}], "source": ["text = data.choices[0].message.content\n", "text = text.replace(\"`python\", \"\").replace(\"`\", \"\").strip() # 去除标记并去除首尾空白\n", "print(\"\\n【清理后的文本内容】：\\n\", text)"]}, {"cell_type": "markdown", "id": "49c6675c-611e-4933-9fca-804200eef339", "metadata": {}, "source": ["#### Prompt：Jupyter Notebook 中执行生成的代码\n", "\n", "Prompt：\n", "\n", "```\n", "我现在用 Completion API 生成了 Python 代码，并以字符串形式存放在 text 中，如下所示：\n", "\n", "text = data.choices[0].text\n", "print(text)\n", "\n", "def quick_sort(arr):\n", "    if len(arr) <= 1:\n", "        return arr\n", "    pivot = arr[0]\n", "    left = [x for x in arr[1:] if x <= pivot]\n", "    right = [x for x in arr[1:] if x > pivot]\n", "    return quick_sort(left) + [pivot] + quick_sort(right)\n", "\n", "如何在 Jupyter notebook 中执行text中存放的这段代码\n", "```\n"]}, {"cell_type": "code", "execution_count": 46, "id": "21707523-bde6-48b9-ac9d-da693045f7c5", "metadata": {}, "outputs": [], "source": ["# `exec` 函数会执行传入的字符串作为 Python 代码。\n", "# 在这个例子中，我们使用 `exec` 来定义了一个 `quick_sort` 函数，然后你就可以调用这个函数了。\n", "# 请注意，`exec` 可以执行任何 Python 代码，因此在使用它的时候一定要小心，特别是当你执行的代码来自不可信的来源时。\n", "exec(text)"]}, {"cell_type": "code", "execution_count": 47, "id": "15918392-560b-4826-8800-4c7a11167579", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1, 1, 2, 3, 6, 8, 10, 12]\n"]}], "source": ["# 现在你可以调用这个函数了\n", "print(quick_sort([12,3,6,8,10,1,2,1]))"]}, {"cell_type": "markdown", "id": "cd669a78-125f-478f-b3d9-677f137d9398", "metadata": {}, "source": ["# 聊天机器人初探（Chat Completions API）\n", "\n", "使用 Chat Completions API 实现对话任务\n", "\n", "聊天补全(Chat Completions API)以消息列表作为输入，并返回模型生成的消息作为输出。尽管聊天格式旨在使多轮对话变得简单，但它同样适用于没有任何对话的单轮任务。\n", "\n", "主要请求参数说明：\n", "\n", "\n", "- **`model` （string，必填）**\n", "\n", "  要使用的模型ID。有关哪些模型适用于Chat API的详细信息\n", "\n", "- **`messages` （array，必填）**\n", "\n", "  迄今为止描述对话的消息列表\n", "    - **`role` （string，必填）**\n", "\n", "  发送此消息的角色。`system` 、`user` 或 `assistant` 之一（一般用 user 发送用户问题，system 发送给模型提示信息）\n", "\n", "    - **`content` （string，必填）**\n", "    \n", "      消息的内容\n", "    \n", "    - **`name` （string，选填）**\n", "    \n", "      此消息的发送者姓名。可以包含 a-z、A-Z、0-9 和下划线，最大长度为 64 个字符\n", "\n", "- **`stream` （boolean，选填，是否按流的方式发送内容）**\n", "\n", "  当它设置为 true 时，API 会以 SSE（ Server Side Event ）方式返回内容。SSE 本质上是一个长链接，会持续不断地输出内容直到完成响应。如果不是做实时聊天，默认false即可。\n", "\n", "- **`max_tokens` （integer，选填）**\n", "\n", "  在聊天补全中生成的最大 **tokens** 数。\n", "\n", "  输入token和生成的token的总长度受模型上下文长度的限制。\n", "\n", "- **`temperature` （number，选填，默认是 1）**\n", "\n", "  采样温度，在 0和 2 之间。\n", "\n", "  较高的值，如0.8会使输出更随机，而较低的值，如0.2会使其更加集中和确定性。\n", "\n", "  通常建议修改这个（`temperature` ）或者 `top_p` ，但两者不能同时存在，二选一。\n"]}, {"cell_type": "markdown", "id": "d00108e4-b98e-4d55-a1e2-78845b321fec", "metadata": {}, "source": ["## 开启聊天模式\n", "\n", "使用 `messages` 记录迄今为止对话的消息列表"]}, {"cell_type": "code", "execution_count": 48, "id": "172f2eb6-2d2b-4d10-9887-5e4af011c77f", "metadata": {}, "outputs": [], "source": ["from openai import OpenAI\n", "client = OpenAI()\n", "\n", "messages=[\n", "    {\n", "        \"role\": \"user\", \n", "        \"content\": \"Hello!\"\n", "    }\n", "]\n", "\n", "\n", "data = client.chat.completions.create(\n", "  model=\"gpt-3.5-turbo\",\n", "  messages = messages\n", ")\n"]}, {"cell_type": "code", "execution_count": 49, "id": "158a8a3b-0a73-4157-9025-8282527fd692", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ChatCompletion(id='chatcmpl-4f9a182e8e594aada8da1daf48dc6600', choices=[Choice(finish_reason='stop', index=0, logprobs=None, message=ChatCompletionMessage(content='你好！有什么我可以帮助你的吗？\\n', refusal=None, role='assistant', audio=None, function_call=None, tool_calls=None))], created=1740969152, model='gpt-3.5-turbo-0125', object='chat.completion', service_tier=None, system_fingerprint='', usage=CompletionUsage(completion_tokens=9, prompt_tokens=21, total_tokens=30, completion_tokens_details=CompletionTokensDetails(accepted_prediction_tokens=None, audio_tokens=0, reasoning_tokens=None, rejected_prediction_tokens=None, text_tokens=0), prompt_tokens_details=PromptTokensDetails(audio_tokens=0, cached_tokens=0, text_tokens=0, image_tokens=0)))\n"]}], "source": ["print(data)"]}, {"cell_type": "code", "execution_count": 50, "id": "d872f47e-b768-4d3e-850f-c2034e2e2263", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ChatCompletionMessage(content='你好！有什么我可以帮助你的吗？\\n', refusal=None, role='assistant', audio=None, function_call=None, tool_calls=None)\n"]}], "source": ["# 从返回的数据中获取生成的消息\n", "new_message = data.choices[0].message\n", "# 打印 new_message\n", "print(new_message)"]}, {"cell_type": "code", "execution_count": 51, "id": "ee8c6048-2fed-4a81-90cc-45f526630aad", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'role': 'user', 'content': 'Hello!'}, ChatCompletionMessage(content='你好！有什么我可以帮助你的吗？\\n', refusal=None, role='assistant', audio=None, function_call=None, tool_calls=None)]\n"]}], "source": ["# 将消息追加到 messages 列表中\n", "messages.append(new_message)\n", "print(messages)"]}, {"cell_type": "code", "execution_count": 52, "id": "1f88a3a6-c6ad-47a2-a187-40f4e921b54f", "metadata": {}, "outputs": [{"data": {"text/plain": ["openai.types.chat.chat_completion_message.ChatCompletionMessage"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["type(new_message)"]}, {"cell_type": "code", "execution_count": 53, "id": "4dc4a02b-2c19-4114-bfad-7b8364d1f8fd", "metadata": {}, "outputs": [{"data": {"text/plain": ["'assistant'"]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["new_message.role"]}, {"cell_type": "code", "execution_count": 54, "id": "843e1454-85b9-409f-8a6a-c84fadcab05a", "metadata": {}, "outputs": [{"data": {"text/plain": ["'你好！有什么我可以帮助你的吗？\\n'"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["new_message.content"]}, {"cell_type": "code", "execution_count": 55, "id": "626586e5-55c0-4b1c-8dc4-175edab9b7c4", "metadata": {}, "outputs": [{"data": {"text/plain": ["ChatCompletionMessage(content='你好！有什么我可以帮助你的吗？\\n', refusal=None, role='assistant', audio=None, function_call=None, tool_calls=None)"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["messages.pop()"]}, {"cell_type": "code", "execution_count": 56, "id": "bbf5b445-f0b2-422c-befd-2738982903d4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'role': 'user', 'content': 'Hello!'}]\n"]}], "source": ["print(messages)"]}, {"cell_type": "markdown", "id": "5a9d6f7b-09fd-4bed-bcde-2ab50d3d415c", "metadata": {}, "source": ["#### Prompt: OpenAIObject -> Dict\n", "\n", "```\n", "打印 messages 列表后发现数据类型不对，messages 输出如下：\n", "\n", "print(messages)\n", "\n", "[{'role': 'user', 'content': 'Hello!'}, <OpenAIObject at 0x7f27582c13f0> JSON: {\n", "  \"content\": \"Hello! How can I assist you today?\",\n", "  \"role\": \"assistant\"\n", "}]\n", "\n", "将OpenAIObject 转换为一个如下数据类型格式：\n", "\n", "    {\n", "        \"role\": \"user\", \n", "        \"content\": \"Hello!\"\n", "    }\n", "```"]}, {"cell_type": "code", "execution_count": 57, "id": "8d9052f8-88ef-4b71-8fa8-42bff7304537", "metadata": {}, "outputs": [{"data": {"text/plain": ["dict"]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["new_message = data.choices[0].message\n", "new_message_dict = {\"role\": new_message.role, \"content\": new_message.content}\n", "type(new_message_dict)"]}, {"cell_type": "code", "execution_count": 58, "id": "699416f3-ee26-432a-8ad5-27b151f24846", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'role': 'assistant', 'content': '你好！有什么我可以帮助你的吗？\\n'}\n"]}], "source": ["print(new_message_dict)"]}, {"cell_type": "code", "execution_count": 59, "id": "7a11a2c6-3f2b-4c35-b987-f396f839da7c", "metadata": {}, "outputs": [], "source": ["# 将消息追加到 messages 列表中\n", "messages.append(new_message_dict)"]}, {"cell_type": "code", "execution_count": 60, "id": "532671cb-6ceb-472f-98c8-c86a38c08bc6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'role': 'user', 'content': 'Hello!'}, {'role': 'assistant', 'content': '你好！有什么我可以帮助你的吗？\\n'}]\n"]}], "source": ["print(messages)"]}, {"cell_type": "markdown", "id": "f7a5d741-5b2e-4f1a-8a74-e3b559874079", "metadata": {}, "source": ["#### 新一轮对话"]}, {"cell_type": "code", "execution_count": 61, "id": "ad2e089b-ba80-477b-92d1-c183f11082c8", "metadata": {}, "outputs": [], "source": ["new_chat = {\n", "    \"role\": \"user\",\n", "    \"content\": \"1.讲一个程序员才听得懂的冷笑话；2.今天是几号？3.明天星期几？\"\n", "}"]}, {"cell_type": "code", "execution_count": 62, "id": "780f361d-b9e1-4343-8851-089d4c0aecde", "metadata": {}, "outputs": [], "source": ["messages.append(new_chat)"]}, {"cell_type": "code", "execution_count": 63, "id": "32b195bb-52ca-4b7f-a1f6-5b183ffbd58f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'content': 'Hello!', 'role': 'user'},\n", " {'content': '你好！有什么我可以帮助你的吗？\\n', 'role': 'assistant'},\n", " {'content': '1.讲一个程序员才听得懂的冷笑话；2.今天是几号？3.明天星期几？', 'role': 'user'}]\n"]}], "source": ["from pprint import pprint\n", "\n", "pprint(messages)"]}, {"cell_type": "code", "execution_count": 64, "id": "54b0532a-0099-40e3-8a3d-9dc72d6eccbd", "metadata": {}, "outputs": [], "source": ["data = client.chat.completions.create(\n", "  model=\"gpt-3.5-turbo\",\n", "  messages=messages\n", ")"]}, {"cell_type": "code", "execution_count": 65, "id": "edad9b8d-2b46-4cd4-ba03-51c96cc4c175", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ChatCompletionMessage(content='好的，没问题！\\n\\n1.  **程序员冷笑话：** \\n    *   程序员A：“我昨天梦见自己变成了架构师。”\\n    *   程序员B：“然后呢？”\\n    *   程序员A：“然后我就醒了。”\\n\\n2.  今天的时间是2024年03月16日。\\n\\n3.  明天是星期日。\\n\\n希望您喜欢我的回答！\\n', refusal=None, role='assistant', audio=None, function_call=None, tool_calls=None)\n"]}], "source": ["new_message = data.choices[0].message\n", "# 打印 new_messages \n", "print(new_message)"]}, {"cell_type": "code", "execution_count": 66, "id": "bedc91c2-4216-4686-b66c-df012a9778c8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["好的，没问题！\n", "\n", "1.  **程序员冷笑话：** \n", "    *   程序员A：“我昨天梦见自己变成了架构师。”\n", "    *   程序员B：“然后呢？”\n", "    *   程序员A：“然后我就醒了。”\n", "\n", "2.  今天的时间是2024年03月16日。\n", "\n", "3.  明天是星期日。\n", "\n", "希望您喜欢我的回答！\n", "\n"]}], "source": ["# 打印 new_messages 内容\n", "print(new_message.content)"]}, {"cell_type": "markdown", "id": "877f2ff6-8bac-43a9-a827-e472c5698ed4", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "id": "903ec93e-fba9-403e-bfe4-b749f91abb0c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "7f417580-7810-4f0e-ad5c-6d2e0f9e7f42", "metadata": {}, "source": ["## 使用多种身份聊天对话\n", "\n", "目前`role`参数支持3类身份： `system`, `user` `assistant`:\n", "\n", "\n", "![](images/chat_completion_api.png)\n", "\n"]}, {"cell_type": "code", "execution_count": 67, "id": "0981fe4b-410d-4d70-b07c-78a88e46b7af", "metadata": {}, "outputs": [], "source": ["# 构造聊天记录\n", "messages=[\n", "    {\"role\": \"system\", \"content\": \"你是一个乐于助人的体育界专家。\"},\n", "    {\"role\": \"user\", \"content\": \"2008年奥运会是在哪里举行的？\"},\n", "]"]}, {"cell_type": "code", "execution_count": 68, "id": "7d135caf-b778-4d50-b738-a429398c6eaa", "metadata": {}, "outputs": [], "source": ["import openai\n", "\n", "data = client.chat.completions.create(\n", "  model=\"gpt-3.5-turbo\",\n", "  messages=messages\n", ")\n"]}, {"cell_type": "code", "execution_count": 69, "id": "e97b0145-af2f-47bb-865f-9b2a8f4e6353", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2008年夏季奥运会在北京举行。\n"]}], "source": ["message = data.choices[0].message.content\n", "print(message)"]}, {"cell_type": "code", "execution_count": 70, "id": "fd56c185-b6e0-4ade-8236-9e2549182ab2", "metadata": {}, "outputs": [], "source": ["# 添加 GPT 返回结果到聊天记录\n", "messages.append({\"role\": \"assistant\", \"content\": message})"]}, {"cell_type": "code", "execution_count": 71, "id": "a860210e-be0d-468e-8845-8ffceaecb034", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'role': 'system', 'content': '你是一个乐于助人的体育界专家。'},\n", " {'role': 'user', 'content': '2008年奥运会是在哪里举行的？'},\n", " {'role': 'assistant', 'content': '2008年夏季奥运会在北京举行。'}]"]}, "execution_count": 71, "metadata": {}, "output_type": "execute_result"}], "source": ["messages"]}, {"cell_type": "code", "execution_count": 72, "id": "99bae857-2854-4090-8a4d-8e1930f70464", "metadata": {}, "outputs": [], "source": ["# 第二轮对话\n", "messages.append({\"role\": \"user\", \"content\": \"1.金牌最多的是哪个国家？2.奖牌最多的是哪个国家？\"})"]}, {"cell_type": "code", "execution_count": 73, "id": "f07bbb71-fe19-4582-b1f3-3ee994fcec0b", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'role': 'system', 'content': '你是一个乐于助人的体育界专家。'},\n", " {'role': 'user', 'content': '2008年奥运会是在哪里举行的？'},\n", " {'role': 'assistant', 'content': '2008年夏季奥运会在北京举行。'},\n", " {'role': 'user', 'content': '1.金牌最多的是哪个国家？2.奖牌最多的是哪个国家？'}]"]}, "execution_count": 73, "metadata": {}, "output_type": "execute_result"}], "source": ["messages"]}, {"cell_type": "code", "execution_count": 74, "id": "a138da7f-b5b3-40a1-b30b-4e6b0c8e249f", "metadata": {}, "outputs": [], "source": ["data = client.chat.completions.create(\n", "  model=\"gpt-3.5-turbo\",\n", "  messages=messages\n", ")"]}, {"cell_type": "code", "execution_count": 75, "id": "f531bc4d-3eeb-49f7-b717-0986f2b4af2c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["好的，我来解答一下：\n", "\n", "1. **金牌榜第一名：**中国以51枚金牌位列金牌榜第一。\n", "2. **奖牌榜第一名：**美国以110枚奖牌位列奖牌榜第一。\n"]}], "source": ["message = data.choices[0].message.content\n", "print(message)"]}, {"cell_type": "code", "execution_count": 76, "id": "3500811e-3a80-4ed0-9770-a1f761dfab6b", "metadata": {}, "outputs": [], "source": ["data = client.chat.completions.create(\n", "  model=\"gpt-3.5-turbo\",\n", "  messages=[{'role': 'user', 'content': '1.金牌最多的是哪个国家？2.奖牌最多的是哪个国家？'}]\n", ")"]}, {"cell_type": "code", "execution_count": 77, "id": "cd59b92d-8362-4215-8865-82fe5cafce56", "metadata": {}, "outputs": [{"data": {"text/plain": ["'1. 历届夏季奥运会中，获得金牌总数最多的国家是**美国**。\\n\\n2. 历届夏季奥运会中，获得奖牌总数最多的国家也是**美国**。\\n'"]}, "execution_count": 77, "metadata": {}, "output_type": "execute_result"}], "source": ["data.choices[0].message.content"]}], "metadata": {"kernelspec": {"display_name": "Python (pytorch)", "language": "python", "name": "pytorch"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}