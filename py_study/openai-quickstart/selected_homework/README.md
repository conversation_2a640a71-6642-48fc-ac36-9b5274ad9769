### 优秀作业展示

在本课程结束后，我们从所有提交的作业中挑选了一些特别出色的项目来展示。这些作业代表了学生们在学习过程中的努力和创新，也为其他学生提供了宝贵的学习资源。



| GitHub ID | 项目主题                  | 简要描述                                                     | 项目链接                                                     |
| -------- | ------------------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| 张三     | OpenAI-Translator 优化版  | 对OpenAI-Translator进行了一系列的优化和功能扩展，提高了翻译的准确性。 | [GitHub Repo]() |
| Hugh | OpenAI-Translator 2.0 | 1.基于 Antd 的图形化界面<br />2.以 API 形式提供翻译服务支持<br /> 3.对多种语言的支持 | [ Github - 后端API](https://github.com/Hughdw/openai-quickstart/blob/ai_translator_extend/openai-translator/ai_translator/app.py) <br />[Github - 前端图形化界面](https://github.com/Hughdw/openai-quickstart/tree/ai_translator_extend/openai-translator/webapp/web)|
| Hugh | OpenAI-Translator 风格化翻译 | 在 Gradio 图形化界面基础上，支持风格化翻译 | [GitHub Repo](https://github.com/Hughdw/openai-quickstart/blob/openai_translator_gradio_style_extend/langchain/openai-translator/ai_translator/gradio_server_by_blocks.py) |
| Hugh | 销售机器人多场景切换 | 生成多场景的向量数据，通过 Gradio 的 Radio 组件切换场景 | [GitHub Repo](https://github.com/Hughdw/openai-quickstart/blob/openai_translator_gradio_style_extend/langchain/sales_chatbot/chatbot.py) |
| wr-fenglei | 使用 ChatGLM2-6B 实现 AI-Translator | 封装 ChatGLM2 ChatModel，通过 transformers 加载 chatglm2-6b-int4 模型 | [GitHub Repo](https://github.com/wr-fenglei/openai-quickstart/pull/2/files) |
| richzw | OpenAI-Translator 2.0 | - 基于HTML图形用户界面 <br /> - 以 API 形式提供翻译服务 <br /> - 优化prompt支持多语言且稳定输出结果  |  [GitHub Repo](https://github.com/richzw/openai-quickstart/tree/feat/translator/openai-translator/ai_translator) |
| richzw | OpenAI-Translator 风格化翻译 | Gradio 图形化界面基础上，通过优化prompt支持风格化翻译 | [GitHub Repo](https://github.com/richzw/openai-quickstart/tree/main/langchain/openai-translator) |
| richzw | 销售机器人应用于不同的销售场景  | 通过MultiRetrievalQAChain来实现对于不同场景问题分别进行回答 | [GitHub Repo](https://github.com/richzw/openai-quickstart/blob/main/langchain/sales_chatbot/sales_chatbot.py) |
| Jean      | OpenAI-Translator 2.0 优化版 | 1.在 openai-translator gradio 图形化界面基础上，支持风格化翻译，如：小说、新闻稿、作家风格等。<br />2.添加一些按钮，按钮对应function，预置风格化的翻译，如：小说、新闻稿、特定作家风格(鲁迅)等<br />3.基于 ChatGLM2-6B 实现图形化界面 的 openai-translator<br /><br />新功能：  <br /> 1.支持多语言；  <br /> 2.支持多输出文件格式， 新增word格式输出；  <br /> 3.支持多翻译风格；  <br />4.新增下拉框选项<br /> | [GitHub Repo](https://github.com/ShengqinYang/AINote/blob/main/project/langchain_openai_translator/ai_translator/gradio_server.py)<br />[项目启动说明](https://github.com/ShengqinYang/AINote/blob/main/project/langchain_openai_translator/README.md)<br />[展示-Demo](https://github.com/ShengqinYang/AINote/blob/main/resource/homework_openai_translator_v2.0.png) 
| Jean      | LangChain版本的AutoGPT             | 实现 LangChain 版本的 AutoGPT 项目的图形化界面                                                                                                                                                                                                                                                    | [GitHub Repo](https://github.com/ShengqinYang/AINote/blob/main/project/langchain_autogpt/autogpt.py) <br /> [项目启动说明](https://github.com/ShengqinYang/AINote/blob/main/project/langchain_autogpt/README.md)                                  |
| Jean      | 扩展销售机器人（sales_chatbot）项目           | 在原有的房地产销售机器人的基础上，增加Apple销售机器人场景                                                                                                                                                                                                                                                      | [GitHub Repo](https://github.com/ShengqinYang/AINote/blob/main/project/langchain_sales_chatbot/sales_chatbot.py) <br /> [项目启动说明](https://github.com/ShengqinYang/AINote/blob/main/project/langchain_sales_chatbot/README.md)                                  |
| Jing      | 基于知识库的小朋友十万个为什么         | 通过十万个为什么数据源，生成回答小朋友的各种问题机器人                               | [GitHub PR](https://github.com/thornbird/openai-quickstart/pull/1)               |             
| Yang Cao| OpenAI-Translator V1 | 1.增加个Gradio图形界面 <br /> 2.增加多语言翻译 <br /> 3.增加风格化翻译<br /> 4. 批处理文件 <br /> 5. 保持原始文档布局和Table样式输出 <br /> 6.两个Prompt例子 <br /> | [GitHub Repo](https://github.com/sycao5/openai-quickstart/blob/yang-project1/openai-translator/ProjectSubmission.md) |
| Yang Cao| 电器销售机器人 notebook | 基于上课的例子，使用Gpt-3.5生成电器新销售QA数据源，增加空调和大屏液晶电视的销售机器人场景| [GitHub Repo](https://github.com/sycao5/openai-quickstart/blob/yang-translator-v2/langchain/sales_chatbot/sales_electrons.ipynb)|
