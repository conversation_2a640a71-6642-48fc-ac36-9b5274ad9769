{"cells": [{"cell_type": "code", "execution_count": 1, "id": "a4e32dd8-6716-463e-8120-91682dd65830", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: requests in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (2.31.0)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from requests) (1.26.16)\n", "Requirement already satisfied: idna<4,>=2.5 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from requests) (3.4)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from requests) (3.1.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /root/miniconda3/envs/langchain/lib/python3.10/site-packages (from requests) (2023.5.7)\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv\u001b[0m\u001b[33m\n", "\u001b[0m"]}], "source": ["!pip install requests"]}, {"cell_type": "markdown", "id": "eab5fa3d-3e27-4337-a891-85eda96ddf50", "metadata": {}, "source": ["# 使用 requests 库请求翻译服务 API"]}, {"cell_type": "code", "execution_count": 2, "id": "596c6c45-8b95-4beb-ae6f-b1c49aa4020c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Translation completed. Translated file saved as translated_output.md.\n"]}], "source": ["import requests\n", "\n", "# Flask服务器的地址\n", "FLASK_SERVER_URL = 'http://localhost:5000'\n", "\n", "# 翻译服务接口\n", "translation_url = f'{FLASK_SERVER_URL}/translation'\n", "\n", "# 要上传的文件路径\n", "file_path = '../tests/test.pdf'  # 修改为你的文件路径\n", "\n", "# 构建请求参数\n", "params = {\n", "    'source_language': 'English',  # 修改为你的源语言\n", "    'target_language': 'Chinese'   # 修改为你的目标语言\n", "}\n", "\n", "# 发送POST请求\n", "with open(file_path, 'rb') as file:\n", "    files = {'input_file': file}\n", "    response = requests.post(translation_url, files=files, data=params)\n", "\n", "\n", "# 翻译后文件\n", "output_filename = \"translated_output.md\"\n", "\n", "# 处理响应\n", "if response.status_code == 200:\n", "    # 保存翻译后的文件\n", "    with open(output_filename, 'wb') as output_file:\n", "        output_file.write(response.content)\n", "    print(f\"Translation completed. Translated file saved as {output_filename}.\")\n", "else:\n", "    print(f\"Translation failed. Status code: {response.status_code}\")\n", "    print(response.text)\n"]}, {"cell_type": "code", "execution_count": null, "id": "2da6286a-d92c-4eaf-abec-9f74cb7af599", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}