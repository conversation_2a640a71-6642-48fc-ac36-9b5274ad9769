{"cells": [{"cell_type": "markdown", "id": "88f8f895", "metadata": {}, "source": ["### LangGraph 多智能体协作中文指南\n", "\n", "在单个领域中，通常一个智能体能够有效地使用一些工具，但即使是使用强大的模型（例如 GPT-4），它在使用大量工具时效果可能会有所降低。\n", "\n", "一种解决复杂任务的方法是采用“分而治之”的方式：为每个任务或领域创建一个专门的智能体，并将任务路由到正确的“专家”。\n", "\n", "本指南灵感来自 Wu 等人的论文《AutoGen: 通过多智能体对话实现下一代 LLM 应用》 展示了使用 LangGraph 进行多智能体协作的一种方法。\n", "\n", "### 工作流程概述\n", "\n", "工作流程清晰地展示了多智能体协作的核心步骤，便于理解 LangGraph 的实现方法。\n", "\n", "1. **定义辅助函数：create_agent**：为每个任务创建独立的智能体，例如研究智能体、图表生成器智能体等。每个智能体使用独立的语言模型和工具。\n", "2. **定义工具**：为每个智能体提供专用的工具，例如 Tavily 搜索工具和 Python REPL 工具，用于执行特定任务。\n", "3. **定义辅助函数：agent_node**：将每个智能体与对应任务进行关联，定义图中的智能体节点，使其能够处理特定任务。\n", "4. **定义研究智能体及节点: Researcher**: 研究智能体使用 Tavily 搜索工具，回应用户提问。\n", "5. **定义图表生成器智能体及节点: Chart_Generator**: 根据提供的数据，在沙盒环境执行 Python 代码生成图表。\n", "6. **导入预构建的工具节点: ToolNode**: 将2中定义的 Tavily 搜索工具和 Python REPL 工具作为一个工具节点，这样可以方便地在工作流中使用这些工具。\n", "7. **建立智能体节点间通信: AgentState**：通过 LangGraph 实现智能体间通信，智能体能够共享状态并相互协作完成复杂任务。\n", "8. **定义工作流（状态图)**：创建状态图以管理多智能体协作的流程，包含任务路由和边逻辑，确保正确的智能体按顺序执行。\n", "9. **执行工作流**：根据状态图执行多智能体任务，通过工具调用和智能体协作，完成目标任务并生成最终输出。\n", "\n", "最终的工作流执行时应像下图所示：\n", "\n", "![simple_multi_agent_diagram](./images/simple_multi_agent_diagram.png)\n", "\n", "\n", "### **说明**\n", "\n", "经过多次测试，多智能体在 gpt-4o 上成功运行。见指南最后的 GPT-4o 模型生成结果 章节。\n", "\n", "当切换为 gpt-4o-mini 时，`Research` 能够生成对应的 Python 代码，但会一定概率路由 `Chart_Generator` 失败，无法调用 Python REPL 工具生成图表。"]}, {"cell_type": "code", "execution_count": 1, "id": "5fef8941", "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install langchain langchain_openai langsmith pandas langchain_experimental matplotlib langgraph langchain_core"]}, {"cell_type": "code", "execution_count": 2, "id": "e14e485f-3a3c-46fe-965d-48ca55de6928", "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "# 定义一个帮助函数来检查环境变量，如果不存在则提示用户输入\n", "def _set_if_undefined(var: str):\n", "    if not os.environ.get(var):\n", "        os.environ[var] = getpass.getpass(f\"请输入您的 {var}\")\n", "\n", "# 设置 OpenAI 和 Langchain API 密钥\n", "# _set_if_undefined(\"OPENAI_API_KEY\")\n", "# _set_if_undefined(\"LANGCHAIN_API_KEY\")\n", "# _set_if_undefined(\"TAVILY_API_KEY\")\n", "\n", "# 可选：在 LangSmith 中添加追踪功能\n", "os.environ[\"LANGCHAIN_TRACING_V2\"] = \"true\"\n", "os.environ[\"LANGCHAIN_PROJECT\"] = \"Multi-agent Collaboration\"\n"]}, {"cell_type": "markdown", "id": "21546ee0", "metadata": {}, "source": ["\n", "### 1. 辅助函数：创建智能体\n", "\n", "以下助手函数将帮助我们创建智能体。这些智能体将成为图中的节点。\n", "\n", "#### 注释说明：\n", "- 该函数 `create_agent` 用于创建一个智能体，通过为该智能体提供系统消息和可以使用的工具来指定其行为。\n", "- `ChatPromptTemplate.from_messages` 是用于构建该智能体的对话提示模板，系统消息告诉智能体它是如何与其他智能体协作的。\n", "- 提示模板通过 `partial` 函数插入了系统消息和工具名称，使得智能体能够根据提供的工具执行任务。\n", "- 最终，智能体被绑定到所提供的 LLM（大型语言模型）和工具列表中，构成一个完整的智能体逻辑。\n"]}, {"cell_type": "code", "execution_count": 3, "id": "6530646d", "metadata": {}, "outputs": [], "source": ["from langchain_core.messages import BaseMessage, HumanMessage, ToolMessage\n", "from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder\n", "from langgraph.graph import END, StateGraph, START\n", "\n", "\n", "# 创建智能体的函数，绑定 LLM（大型语言模型） 和工具\n", "def create_agent(llm, tools, system_message: str):\n", "    \"\"\"创建一个智能体。\"\"\"\n", "    # 定义智能体的提示模板，包含系统消息和工具信息\n", "    prompt = ChatPromptTemplate.from_messages(\n", "        [\n", "            (\n", "                \"system\",\n", "                \"You are a helpful AI assistant, collaborating with other assistants.\"\n", "                \" Use the provided tools to progress towards answering the question.\"\n", "                \" If you are unable to fully answer, that's OK, another assistant with different tools \"\n", "                \" will help where you left off. Execute what you can to make progress.\"\n", "                \" If you or any of the other assistants have the final answer or deliverable,\"\n", "                \" prefix your response with FINAL ANSWER so the team knows to stop.\"\n", "                \" You have access to the following tools: {tool_names}.\\n{system_message}\",\n", "            ),\n", "            MessagesPlaceholder(variable_name=\"messages\"),  # 用于替换的消息占位符\n", "        ]\n", "    )\n", "    \n", "    # 将系统消息部分和工具名称插入到提示模板中\n", "    prompt = prompt.partial(system_message=system_message)\n", "    prompt = prompt.partial(tool_names=\", \".join([tool.name for tool in tools]))\n", "    \n", "    # 将提示模板与语言模型和工具绑定\n", "    return prompt | llm.bind_tools(tools)\n"]}, {"cell_type": "markdown", "id": "2c7be88e-c428-46de-8986-5faa7e4e9bda", "metadata": {}, "source": ["\n", "#### `partial` 是什么\n", "\n", "在 Python 中，`partial` 方法是 `functools` 模块中的一个功能，它用于创建一个**新的函数**，这个函数是基于原函数的**部分参数已经固定**的版本。这在需要重复调用同一函数，并且传递相同的某些参数时非常有用。\n", "\n", "####  `partial` 的基本理解\n", "\n", "通过 `partial`，我们可以预先为函数的某些参数赋值，生成一个新的函数，这个新函数已经预先固定了部分参数，只需要再传递剩下的参数即可。\n", "\n", "#### `prompt.partial` 解析\n", "\n", "这里的 `partial` 用于创建一个新的提示模板对象，并为 `system_message` 和 `tool_names` 这两个参数提供了值。这相当于对提示模板的“定制”，预先指定了这些参数的值。\n", "\n", "**`partial` 的具体作用：**\n", "\n", "1. 调用 `prompt.partial(system_message=system_message)`，预先为 `system_message` 参数赋值，生成一个新的提示模板，固定了系统消息的内容。\n", "2. 调用 `prompt.partial(tool_names=\", \".join([tool.name for tool in tools]))`，为 `tool_names` 参数赋值，将所有工具的名称合并成一个字符串，并固定在新的模板中。\n", "\n", "通过这两步 `partial` 调用，`prompt` 对象中已经预先填入了 `system_message` 和 `tool_names` 这两个参数，简化了后续的调用过程。\n", "\n", "--------------------"]}, {"cell_type": "markdown", "id": "3407bcc6", "metadata": {}, "source": ["\n", "### 2. 定义工具\n", "\n", "接下来我们定义一些未来智能体将使用的工具。\n", "\n", "#### 注释说明：\n", "- `tavily_tool`: 定义了一个 Tavily 搜索工具，可以搜索最多 5 条结果。\n", "- `repl`: 定义了一个 Python REPL 工具，用于执行 Python 代码块。\n", "- `python_repl` 函数：这是一个装饰的工具函数，接受 Python 代码作为输入，并通过 `PythonREPL` 环境执行代码。成功执行后返回执行的代码和输出。如果发生错误，则捕获并返回错误信息。\n"]}, {"cell_type": "code", "execution_count": 4, "id": "4e729d1e", "metadata": {}, "outputs": [], "source": ["from typing import Annotated\n", "\n", "from langchain_community.tools.tavily_search import TavilySearchResults\n", "from langchain_core.tools import tool\n", "from langchain_experimental.utilities import PythonREPL\n", "\n", "# Tavily 搜索工具，用于搜索最多 5 条结果\n", "tavily_tool = TavilySearchResults(max_results=5)\n", "\n", "# Python REPL 工具，用于执行 Python 代码\n", "repl = PythonREPL()\n", "\n", "@tool\n", "def python_repl(\n", "    code: Annotated[str, \"The python code to execute to generate your chart.\"],\n", "):\n", "    \"\"\"Use this to execute python code. If you want to see the output of a value,\n", "    you should print it out with `print(...)`. This is visible to the user.\"\"\"\n", "    try:\n", "        result = repl.run(code)\n", "    except BaseException as e:\n", "        return f\"Failed to execute. Error: {repr(e)}\"\n", "\n", "    result_str = f\"Successfully executed:\\n```python\\n{code}\\n```\\nStdout: {result}\"\n", "\n", "    return (\n", "        result_str + \"\\n\\nIf you have completed all tasks, respond with FINAL ANSWER.\"\n", "    )"]}, {"cell_type": "markdown", "id": "6a379da6", "metadata": {}, "source": ["-----------\n", "\n", "### 3. 辅助函数：智能体节点\n", "\n", "下面我们定义智能体节点函数（`agent_node`)，然后使用它分别定义2个智能体节点：\n", "- Researcher\n", "- Chart_Generator\n", "\n", "#### 注释说明：\n", "\n", "- `agent_node` 函数是一个辅助函数，用于创建一个智能体节点。它接受当前的 `state`（状态）、`agent`（智能体） 和 `name`（智能体的名称），并返回一个新的状态字典，包含消息和发送者。\n", "- `research_agent`: 使用 `create_agent` 函数创建了一个研究智能体，使用 `research_llm` 作为语言模型，并且绑定了 `tavily_tool` 搜索工具。\n", "- `chart_agent`: 同样使用 `create_agent` 创建了图表生成器智能体，使用 `chart_llm` 作为语言模型，并绑定了 `python_repl` 代码执行工具。\n", "- `functools.partial`: 用于创建特定名称的智能体节点，例如 `\"Researcher\"` 和 `\"Chart_Generator\"`，并与各自的智能体绑定。"]}, {"cell_type": "code", "execution_count": 5, "id": "910d2398", "metadata": {}, "outputs": [], "source": ["import functools\n", "from langchain_core.messages import AIMessage\n", "from langchain_openai import ChatOpenAI\n", "\n", "# 辅助函数：为智能体创建一个节点\n", "def agent_node(state, agent, name):\n", "    # 调用智能体，获取结果\n", "    result = agent.invoke(state)\n", "    \n", "    # 将智能体的输出转换为适合追加到全局状态的格式\n", "    if isinstance(result, ToolMessage):\n", "        pass  # 如果是工具消息，跳过处理\n", "    else:\n", "        # 将结果转换为 AIMessage，并排除部分字段\n", "        result = AIMessage(**result.dict(exclude={\"type\", \"name\"}), name=name)\n", "    \n", "    # 返回更新后的状态，包括消息和发送者\n", "    return {\n", "        \"messages\": [result],  # 包含新生成的消息\n", "        # 我们使用严格的工作流程，通过记录发送者来知道接下来传递给谁\n", "        \"sender\": name,\n", "    }"]}, {"cell_type": "markdown", "id": "7aec876b-9041-4ce1-a7ea-c81fba984bf6", "metadata": {}, "source": ["#### 关于 `AIMessage` 构造\n", "\n", "`AIMessage` 是 LangChain 中用于表示 AI 模型回复的类，它封装了 AI 生成的文本或内容。为了让 Python 初学者更好地理解，我们可以从以下几个方面详细说明 `AIMessage` 的构造方法及其相关概念。\n", "\n", "##### `AIMessage` 构造方法简介\n", "\n", "在代码中，`AIMessage(**result.dict(exclude={\"type\", \"name\"}), name=name)` 这段代码使用了 `AIMessage` 的构造方法。`AIMessage` 的目的是将 AI 生成的消息封装起来，方便后续处理和传递。这里的构造方法通过传递字典参数创建 `AIMessage` 对象。\n", "\n", "##### `AIMessage` 类的常见构造参数：\n", "- **content**: 这是消息的主要部分，通常是 AI 模型生成的文本内容。例如，一个简单的对话模型可能会生成一个包含回答问题的字符串。\n", "- **name**: 可选参数，用于标识发送消息的 AI 模型或智能体的名称。在你的代码中，`name=name` 表示为智能体分配一个名称（如 `\"Researcher\"` 或 `\"Chart_Generator\"`），以便在不同智能体之间进行区分。\n", "- **additional_metadata**: 有时候，消息不仅仅包含文本内容，还可能附加其他元数据，如调用的工具、时间戳等。\n", "\n", "##### 深入理解构造方法中的步骤：\n", "\n", "1. **`result.dict()`**: \n", "   这一部分将 `result` 对象转换为字典。字典是一种键值对的结构，便于存储和管理数据。Python 中的 `dict()` 方法会把 `result` 对象的所有属性转换成字典的形式，方便在构造 `AIMessage` 时传递这些数据。\n", "\n", "2. **`exclude={\"type\", \"name\"}`**:\n", "   在构造 `AIMessage` 时，使用了 `exclude` 参数来排除某些不必要的字段。`type` 和 `name` 这两个字段不会被传递给 `AIMessage`，这是因为它们可能不是 AI 消息本身的必要部分或已经在其他地方定义过。\n", "\n", "3. **`name=name`**:\n", "   这里的 `name` 参数表示智能体的名称，它是在 `agent_node` 函数中作为参数传递的。在构造 `AIMessage` 时，通过这个参数来标识消息的来源智能体是谁，比如 `\"Researcher\"` 或 `\"Chart_Generator\"`。"]}, {"cell_type": "markdown", "id": "b33515a5-14bf-4100-a5b7-7625fbf1bc9e", "metadata": {}, "source": ["--------------\n", "\n", "### 4. 定义 研究智能体及其节点\n"]}, {"cell_type": "code", "execution_count": 6, "id": "82030b1d-bf6d-4cc0-8f07-63fea8af9d88", "metadata": {"jupyter": {"source_hidden": true}}, "outputs": [], "source": ["# 为 Agent 配置各自的大模型\n", "research_llm = ChatOpenAI(model=\"gpt-4o-mini\", temperature=0.5)\n", "chart_llm = ChatOpenAI(model=\"gpt-4o-mini\", temperature=0)"]}, {"cell_type": "code", "execution_count": 7, "id": "b6dfa4ea-1cd1-423a-b24a-865308631d78", "metadata": {}, "outputs": [], "source": ["# 研究智能体及其节点\n", "research_agent = create_agent(\n", "    research_llm,  # 使用 research_llm 作为研究智能体的语言模型\n", "    [tavily_tool],  # 研究智能体使用 Tavily 搜索工具\n", "    system_message=\"Before using the search engine, carefully think through and clarify the query. \"\n", "    \"Then, conduct a single search that addresses all aspects of the query in one go.\",  # 系统消息，指导智能体如何使用搜索工具\n", ")"]}, {"cell_type": "code", "execution_count": 8, "id": "7aa442a3-c015-4d02-8216-518fd6c3aeb0", "metadata": {}, "outputs": [], "source": ["# 使用 functools.partial 创建研究智能体的节点，指定该节点的名称为 \"Researcher\"\n", "research_node = functools.partial(agent_node, agent=research_agent, name=\"Researcher\")"]}, {"cell_type": "markdown", "id": "7f07df36-d186-45a2-bf96-8da29b39ad56", "metadata": {}, "source": ["这里的 `functools.partial` 创建了一个新的函数 `research_node`，该函数基于原始的 `agent_node` 函数，且已经为 `agent_node` 的部分参数（`agent` 和 `name`）预先设置了值。新的 `research_node` 函数只需要接收剩余的参数就可以正常运行。\n", "\n", "\n", "**`partial` 的具体作用：**\n", "\n", "1. **原始函数 `agent_node`**：\n", "   ```python\n", "   def agent_node(state, agent, name):\n", "       # 函数体...\n", "   ```\n", "   - `agent_node` 是一个接受 `state`, `agent`, 和 `name` 三个参数的函数。\n", "\n", "2. **使用 `functools.partial`**：\n", "   ```python\n", "   research_node = functools.partial(agent_node, agent=research_agent, name=\"Researcher\")\n", "   ```\n", "   - 通过 `functools.partial`，我们创建了一个新的函数 `research_node`，它仍然是 `agent_node`，但 `agent` 参数和 `name` 参数已经被预先固定：\n", "     - `agent=research_agent`\n", "     - `name=\"Researcher\"`\n", "   - 也就是说，调用 `research_node` 时，只需要传递 `state` 参数，因为 `agent` 和 `name` 已经有默认值了。\n", "\n", "**举个例子**\n", "\n", "假设有一个函数 `agent_node`，你经常需要调用它并传递相同的 `agent` 和 `name`，那么每次调用时重复写这些参数会很冗余。使用 `partial` 可以避免这种重复。\n", "\n", "```python\n", "# 原始函数定义\n", "def agent_node(state, agent, name):\n", "    print(f\"State: {state}, Agent: {agent}, Name: {name}\")\n", "\n", "# 预先设置 agent 和 name 参数\n", "research_node = functools.partial(agent_node, agent=\"research_agent_value\", name=\"Researcher\")\n", "\n", "# 调用时只需要传递剩下的参数\n", "research_node(state=\"current_state\")\n", "# 输出: State: current_state, Agent: research_agent_value, Name: Researcher\n", "```\n", "\n", "#### `functools.partial` 的优势\n", "\n", "1. **减少重复代码**：在你需要多次调用同一个函数并且某些参数不变时，`partial` 可以避免每次都传递相同的参数。\n", "   \n", "2. **简化函数调用**：在需要频繁使用相同参数时，`partial` 提供了更简洁的写法，使代码更易于维护。\n", "\n", "#### 总结\n", "\n", "在这段代码中，`functools.partial` 的用法预先为 `agent_node` 函数的部分参数（`agent` 和 `name`）赋值，创建了一个新函数 `research_node`。调用 `research_node` 时，只需要传递剩下的参数（`state`），从而简化了函数调用的流程。\n", "\n", "------------------"]}, {"cell_type": "markdown", "id": "d149beb7-e216-4630-9b19-d3bc6949f5da", "metadata": {}, "source": ["### 5. 定义 图表生成器智能体及其节点"]}, {"cell_type": "code", "execution_count": 9, "id": "c51bd833-1b7f-4287-b23d-11eb7f55ef6c", "metadata": {}, "outputs": [], "source": ["chart_agent = create_agent(\n", "    chart_llm,  # 使用 chart_llm 作为图表生成器智能体的语言模型\n", "    [python_repl],  # 图表生成器智能体使用 Python REPL 工具\n", "    system_message=\"Create clear and user-friendly charts based on the provided data.\",  # 系统消息，指导智能体如何生成图表\n", ")"]}, {"cell_type": "code", "execution_count": 10, "id": "268da6ad-d4b8-42c8-bb0f-4f41620c6e1c", "metadata": {}, "outputs": [], "source": ["# 使用 functools.partial 创建图表生成器智能体的节点，指定该节点的名称为 \"Chart_Generator\"\n", "chart_node = functools.partial(agent_node, agent=chart_agent, name=\"Chart_Generator\")"]}, {"cell_type": "markdown", "id": "f6708b47", "metadata": {}, "source": ["\n", "### 6. 导入预构建的工具节点\n", "\n", "我们现在导入预构建的工具节点 `ToolNode` （运行上一个AIMessage中调用工具的节点。）。将 Tavily 搜索工具和 Python REPL 工具作为一个工具节点，这样可以方便地在工作流中使用这些工具。\n", "\n", "### 什么是 ToolNode？\n", "\n", "**ToolNode** 是 LangChain 的一个预构建节点，它能够从图状态（`graph state`）中提取消息并调用指定的工具，最后将工具调用的结果反馈回图的状态中。ToolNode 非常适合与 LangGraph 中的 ReAct agent 协同工作，但也可以与任何 `StateGraph` 配合使用，只要状态中有 `messages` 键和合适的消息处理方式。\n", "\n", "#### ToolNode 的特点\n", "1. **工具调用**：ToolNode 可以根据状态中的消息自动调用指定的工具，并返回工具的执行结果。\n", "2. **兼容性**：可以与任意支持工具调用的 LangChain 模型配合使用。\n", "3. **并行工具调用**：支持同时调用多个工具，并处理工具返回的多个结果。\n", "4. **错误处理**：ToolNode 默认启用了错误处理，可以处理工具在执行过程中的异常情况。\n", "\n", "#### 与对话模型结合使用\n", "\n", "在使用像 Anthropic 这样的对话模型时，模型可以自动生成带有 `tool_calls` 的 `AIMessage`，这样我们可以直接将模型生成的消息传给 ToolNode 来执行工具调用：\n", "\n", "```python\n", "from langchain_anthropic import ChatAnthropic\n", "from langgraph.prebuilt import ToolNode\n", "\n", "model_with_tools = ChatAnthropic(\n", "    model=\"claude-3-haiku-20240307\", temperature=0\n", ").bind_tools(tools)\n", "\n", "tool_node.invoke({\"messages\": [model_with_tools.invoke(\"what's the weather in sf?\")]})\n", "# 返回: {'messages': [ToolMessage(content=\"It's 60 degrees and foggy.\", name='get_weather', tool_call_id='toolu_01LFvAVT3xJMeZS6kbWwBGZK')]}\n", "```\n", "\n", "#### ToolNode 与 ReAct Agent 结合\n", "\n", "ReAct Agent 是 LangGraph 中的一种智能体，它会反复调用工具，直到收集到足够的信息来解决问题。以下是 ReAct Agent 的基本工作流，它通过工具节点来完成工具调用：\n", "\n", "```python\n", "from typing import Literal\n", "from langgraph.graph import StateGraph, MessagesState\n", "\n", "def should_continue(state: MessagesState) -> Literal[\"tools\", \"__end__\"]:\n", "    messages = state[\"messages\"]\n", "    last_message = messages[-1]\n", "    if last_message.tool_calls:\n", "        return \"tools\"\n", "    return \"__end__\"\n", "\n", "def call_model(state: MessagesState):\n", "    messages = state[\"messages\"]\n", "    response = model_with_tools.invoke(messages)\n", "    return {\"messages\": [response]}\n", "\n", "# 创建状态图\n", "workflow = StateGraph(MessagesState)\n", "\n", "# 定义两个节点：一个用于调用模型，一个用于调用工具\n", "workflow.add_node(\"agent\", call_model)\n", "workflow.add_node(\"tools\", tool_node)\n", "\n", "workflow.add_edge(\"__start__\", \"agent\")  # 从 agent 节点开始\n", "workflow.add_conditional_edges(\"agent\", should_continue)  # 根据条件判断是否继续调用工具\n", "workflow.add_edge(\"tools\", \"agent\")  # 工具调用完成后，返回 agent 节点\n", "\n", "app = workflow.compile()  # 编译状态图\n", "```\n", "\n", "#### 错误处理\n", "\n", "ToolNode 默认启用了错误处理，可以处理工具执行中的异常情况。如果想禁用错误处理，可以设置 `handle_tool_errors=False`。\n", "\n", "#### 总结\n", "\n", "**ToolNode** 是一个非常强大的组件，它能够自动调用工具并将结果反馈回工作流。它可以处理单个或多个工具调用，并与 LangChain 模型紧密结合，使得在复杂的多步骤任务中能够更高效地调用外部 API 或工具。"]}, {"cell_type": "code", "execution_count": 11, "id": "034f427f", "metadata": {}, "outputs": [], "source": ["from langgraph.prebuilt import ToolNode\n", "\n", "# 定义工具列表，包括 Tavily 搜索工具和 Python REPL 工具\n", "tools = [tavily_tool, python_repl]\n", "\n", "# 创建工具节点，负责工具的调用\n", "tool_node = ToolNode(tools)"]}, {"cell_type": "markdown", "id": "745f3686-eda1-4c68-913c-e5f8cf9f1ea1", "metadata": {}, "source": ["------------------------"]}, {"cell_type": "markdown", "id": "dd32b59a-dae0-4b42-8b44-991c6ca18dfd", "metadata": {}, "source": ["\n", "### 7. 建立智能体节点间通信 AgentState\n", "\n", "定义智能体节点和工具节点后，接下来需要在 Graph 中使它们互相通信。\n", "\n", "因此，我们需要定义节点间的消息传递数据结构：AgentState\n", "\n", "我们使用一个消息列表，并包含一个键来跟踪最近的发送者。\n", "\n", "#### 注释说明：\n", "- `AgentState` 是一个 `TypedDict`，它定义了图中传递的状态对象，包括 `messages` 和 `sender`。`messages` 用于存储传递的消息，`sender` 用于跟踪消息的发送者。\n"]}, {"cell_type": "code", "execution_count": 12, "id": "5046c5d7", "metadata": {}, "outputs": [], "source": ["import operator\n", "from typing import Annotated, Sequence, TypedDict\n", "\n", "# 定义图中传递的对象，包含消息和发送者信息\n", "class AgentState(TypedDict):\n", "    # messages 是传递的消息，使用 Annotated 和 Sequence 来标记类型\n", "    messages: Annotated[Sequence[BaseMessage], operator.add]\n", "    # sender 是发送消息的智能体\n", "    sender: str"]}, {"cell_type": "markdown", "id": "31c47fc6", "metadata": {}, "source": ["\n", "### 8. 定义工作流（状态图）\n", "\n", "我们现在将所有内容组合在一起，定义多智能体的完整状态图。\n", "\n", "#### 注释说明：\n", "\n", "- `StateGraph(AgentState)`：用于创建一个状态图 `workflow`，其状态由 `AgentState` 管理。\n", "- `add_node`：将智能体节点 `Researcher`、`Chart_Generator` 和 `call_tool` 添加到状态图中，每个节点对应一个任务或功能。\n", "- `add_conditional_edges`：为节点添加条件边，基于 `router` 函数的返回值来决定下一个要执行的步骤。\n", "  - `continue`：继续到另一个智能体节点。\n", "  - `call_tool`：调用工具节点。\n", "  - `__end__`：结束流程。\n", "- `add_edge`：将开始节点 `START` 与初始节点 `Researcher` 连接，定义工作流的启动顺序。\n", "- `compile`：编译状态图，准备好执行任务。\n", "\n", "#### Graph 对象关键方法 API\n", "\n", "- **add_conditional_edges**: https://langchain-ai.github.io/langgraph/reference/graphs/?h=add+conditional+edges#stategraph\n", "- **get_graph**: https://langchain-ai.github.io/langgraph/reference/graphs/?h=add+conditional+edges#langgraph.graph.graph.CompiledGraph.get_graph\n"]}, {"cell_type": "code", "execution_count": 13, "id": "cd89bd10", "metadata": {}, "outputs": [], "source": ["# 创建一个状态图 workflow，使用 AgentState 来管理状态\n", "workflow = StateGraph(AgentState)\n", "\n", "# 将研究智能体节点、图表生成器智能体节点和工具节点添加到状态图中\n", "workflow.add_node(\"Researcher\", research_node)\n", "workflow.add_node(\"Chart_Generator\", chart_node)\n", "workflow.add_node(\"call_tool\", tool_node)"]}, {"cell_type": "markdown", "id": "165418d2-f5d1-4c25-ad5e-de5f6080a67d", "metadata": {}, "source": ["\n", "#### 定义路由函数\n", "\n", "接下来定义边逻辑，以根据智能体的结果来决定下一步操作。\n", "\n", "#### 注释说明：\n", "- `router` 函数是工作流中的一个关键逻辑，用于根据当前的状态和消息内容来决定下一步的操作。\n", "- 如果最新的消息中包含工具调用（`tool_calls`），则返回 `\"call_tool\"`，表示需要调用工具。\n", "- 如果消息内容中包含 `\"FINAL ANSWER\"`，表示任务已经完成，返回 `\"__end__\"` 来结束任务。\n", "- 如果没有满足以上条件，则返回 `\"continue\"`，表示继续任务并执行下一步操作。"]}, {"cell_type": "code", "execution_count": 14, "id": "013c7a3b", "metadata": {}, "outputs": [], "source": ["from typing import Literal\n", "\n", "# 路由器函数，用于决定下一步是执行工具还是结束任务\n", "def router(state) -> Literal[\"call_tool\", \"__end__\", \"continue\"]:\n", "    messages = state[\"messages\"]  # 获取当前状态中的消息列表\n", "    last_message = messages[-1]  # 获取最新的一条消息\n", "    \n", "    # 如果最新消息包含工具调用，则返回 \"call_tool\"，指示执行工具\n", "    if last_message.tool_calls:\n", "        return \"call_tool\"\n", "    \n", "    # 如果最新消息中包含 \"FINAL ANSWER\"，表示任务已完成，返回 \"__end__\" 结束工作流\n", "    if \"FINAL ANSWER\" in last_message.content:\n", "        return \"__end__\"\n", "    \n", "    # 如果既没有工具调用也没有完成任务，继续流程，返回 \"continue\"\n", "    return \"continue\"\n"]}, {"cell_type": "markdown", "id": "a97c9182", "metadata": {}, "source": ["#### 定义条件边逻辑"]}, {"cell_type": "code", "execution_count": 15, "id": "2d1c9c19-2858-4014-9e40-3dd6f30d92c5", "metadata": {}, "outputs": [], "source": ["# 为 \"Researcher\" 智能体节点添加条件边，根据 router 函数的返回值进行分支\n", "workflow.add_conditional_edges(\n", "    \"Researcher\",\n", "    router,  # 路由器函数决定下一步\n", "    {\n", "        \"continue\": \"Chart_Generator\",  # 如果 router 返回 \"continue\"，则传递到 Chart_Generator\n", "        \"call_tool\": \"call_tool\",  # 如果 router 返回 \"call_tool\"，则调用工具\n", "        \"__end__\": END  # 如果 router 返回 \"__end__\"，则结束工作流\n", "    },\n", ")\n", "\n", "# 为 \"Chart_Generator\" 智能体节点添加条件边\n", "workflow.add_conditional_edges(\n", "    \"Chart_Generator\",\n", "    router,  # 同样使用 router 函数决定下一步\n", "    {\n", "        \"continue\": \"Researcher\",  # 如果 router 返回 \"continue\"，则回到 Researcher\n", "        \"call_tool\": \"call_tool\",  # 如果 router 返回 \"call_tool\"，则调用工具\n", "        \"__end__\": END  # 如果 router 返回 \"__end__\"，则结束工作流\n", "    },\n", ")\n", "\n", "# 为 \"call_tool\" 工具节点添加条件边，基于“sender”字段决定下一个节点\n", "# 工具调用节点不更新 sender 字段，这意味着边将返回给调用工具的智能体\n", "workflow.add_conditional_edges(\n", "    \"call_tool\",\n", "    lambda x: x[\"sender\"],  # 根据 sender 字段判断调用工具的是哪个智能体\n", "    {\n", "        \"Researcher\": \"Researcher\",  # 如果 sender 是 Researcher，则返回给 Researcher\n", "        \"Chart_Generator\": \"Chart_Generator\",  # 如果 sender 是 Chart_Generator，则返回给 Chart_Generator\n", "    },\n", ")\n"]}, {"cell_type": "code", "execution_count": 16, "id": "10670029-c194-4e2b-b002-56218aabd8cb", "metadata": {}, "outputs": [], "source": ["# 添加开始节点，将流程从 START 节点连接到 Researcher 节点\n", "workflow.add_edge(<PERSON><PERSON><PERSON>, \"Researcher\")\n", "\n", "# 编译状态图以便后续使用\n", "graph = workflow.compile()"]}, {"cell_type": "code", "execution_count": 17, "id": "53b403f8-1dfd-40a8-93ef-47a4886226c0", "metadata": {}, "outputs": [{"data": {"image/jpeg": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 可视化图\n", "from IPython.display import Image, display\n", "\n", "try:\n", "    display(\n", "        Image(\n", "            graph.get_graph(xray=True).draw_mermaid_png()\n", "        )\n", "    )\n", "except Exception as e:\n", "    print(f\"Error generating graph: {e}\")"]}, {"cell_type": "markdown", "id": "87e9496a", "metadata": {}, "source": ["\n", "### 9. 执行工作流\n", "\n", "接下来我们将执行多智能体构建的工作流，最终生成一些统计图表。"]}, {"cell_type": "code", "execution_count": 18, "id": "ee3322cf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Obtain the GDP of the United States from 2000 to 2020, and then plot a line chart with Python. End the task after generating the chart。\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: Researcher\n", "Tool Calls:\n", "  tavily_search_results_json (call_QHfy6ftv8ZXUfZrgTLEmHSOI)\n", " Call ID: call_QHfy6ftv8ZXUfZrgTLEmHSOI\n", "  Args:\n", "    query: United States GDP data from 2000 to 2020\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: tavily_search_results_json\n", "\n", "[{\"url\": \"https://www.statista.com/topics/772/gdp/\", \"content\": \"U.S. gross domestic product contributions 2000-2021, by sector\\nValue added to gross domestic product across economic sectors in the United States from 2000 to 2021 (as a share of GDP)\\nU.S. change in real value added to GDP 2022, by industry\\nChange in real value added to the gross domestic product of the United States in 2022, by industry\\nU.S. change in value added to real GDP 2012-2022, by industry\\nTen year percentage change in value added to the real gross domestic product of the United States between 2012 and 2022, by industry\\nU.S. value added to GDP by manufacturing industry 2000-2022\\nValue added to the gross domestic product by the manufacturing industry in the United States from 2000 to 2022 (in trillion U.S. dollars)\\nTech GDP as a percent of total GDP in the U.S. 2017-2022\\nTech sector as a percentage of total gross domestic product (GDP) in the United States from 2017 to 2022\\nU.S. digital economy value added to GDP 2021, by industry\\nValue added to the total economy (GDP) by the digital economy in the United States in 2021, by industry (in million U.S. dollars)\\n U.S. value added to GDP by construction industry 2000-2022\\nValue added to gross domestic product by the construction industry in the United States from 2000 to 2022 (in billion U.S. dollars)\\nGDP by state\\nGDP by state\\nU.S. gross domestic product 2022, by state\\nGross domestic product of the United States in 2022, by state (in billion current U.S. dollars)\\nU.S. real gross domestic product 2022, by state\\nReal gross domestic product (GDP) of the United States in 2022, by state (in billion chained 2017 U.S. dollars)\\nU.S. real GDP growth 2022, by state\\nPercent change in the real gross domestic product of the United States in 2022, by state\\nU.S. real GDP of California 2000-2022\\nReal gross domestic product of California in the United States from 2000 to 2022 (in billion U.S. dollars)\\n U.S. real value added to GDP in Florida, by industry\\nReal value added to the gross domestic product of Florida in the United States in 2022, by industry (in billion chained 2017 U.S. dollars)\\nGDP by metropolitan area\\nGDP by metropolitan area\\nU.S. metro areas - ranked by Gross Metropolitan Product (GMP) 2021\\nForecasted Gross Metropolitan Product (GMP) of the United States in 2021, by metropolitan area (in billion current U.S. dollars)\\nU.S. real GDP 2021, by metro area\\nReal gross domestic product of the United States in 2021, by metropolitan area (in million chained 2012 U.S. dollars)\\nU.S. real GDP annual percent change 2021, by metro area\\nAnnual percent change in the real GDP of the United States in 2021, by metropolitan area\\nU.S. real GDP per capita 2021, by metro area\\nPer capita real gross domestic product of the United States in 2021, by metropolitan area (in chained 2012 U.S. dollars)\\n U.S. gross value added to GDP 2022, by sector\\nGross value added to the gross domestic product in the United States from 1990 to 2022, by sector (in billion U.S. dollars)\\nU.S. budget balance and forecast as a percentage of GDP 2000-2033\\nBudget balance and forecast of the United States government from 2000 to 2033 (as a percentage of GDP)\\nGDP by sector and industry\\nGDP by sector and industry\\nU.S. real value added to GDP 1990-2022, by sector\\nReal gross value added to the gross domestic product of the United States from 1990 to 2022, by sector (in billion chained 2017 U.S. dollars)\\n The 20 countries with the largest gross domestic product (GDP) per capita in 2022 (in U.S. dollars)\\nGDP growth in the leading industrial and emerging countries 2nd quarter 2023\\nGrowth of the real gross domestic product (GDP) in the leading industrial and emerging countries from 2nd quarter 2021 to 2nd quarter 2023 (compared to the previous quarter)\\nRelated topics\\nRecommended\\nReport on the topic\\nKey figures\\nThe most important key figures provide you with a compact summary of the topic of \\\"GDP of the United States\\\" and take you straight to the corresponding statistics.\\n\"}, {\"url\": \"https://www.macrotrends.net/global-metrics/countries/USA/united-states/gdp-gross-domestic-product\", \"content\": \"U.S. gdp for 2021 was $23,315.08B, a 10.71% increase from 2020. U.S. gdp for 2020 was $21,060.47B, a 1.5% decline from 2019. U.S. gdp for 2019 was $21,380.98B, a 4.13% increase from 2018. GDP at purchaser's prices is the sum of gross value added by all resident producers in the economy plus any product taxes and minus any subsidies not included ...\"}, {\"url\": \"https://usafacts.org/data/topics/economy/economic-indicators/gdp/gross-domestic-product/\", \"content\": \"Data Adjustments\\nIs the economy growing?\\nRelated Metrics\\nAnnual percent change in real GDP\\n5.7%\\n2021\\nAnnual percent change in real GDP\\n5.7%\\n2021\\nExplore Gross domestic product\\nInteract with the data\\nData Adjustments\\nState Display\\nOur nation, in numbers\\nUSAFacts is a not-for-profit, nonpartisan civic initiative making government data easy for all Americans to access and understand.\\n \\u2022 Check your spelling\\n\\u2022 Try other search terms\\n\\u2022 Use fewer words\\nGross domestic product\\nGross domestic product\\nGross domestic product (GDP) is the value of all goods and services produced in the US. All topics\\nExplore articles, data and trends by topic\\nAbout\\nWhat makes USAFacts different\\nWe frequently add data and we're interested in what would be useful to people. Newsletter\\nData delivered to your inbox\\nKeep up with the latest data and most popular content. But only the official BEA inflation-adjusted \\\"real GDP\\\" value is used to calculate annual percent change in GDP and therefore how well the economy is doing.\"}, {\"url\": \"https://fred.stlouisfed.org/series/GDP/\", \"content\": \"RELEASE TABLES\\nRELATED DATA AND CONTENT\\nData Suggestions Based On Your Search\\nContent Suggestions\\nOther Formats\\nRelated Categories\\nReleases\\nTags\\nSERVICES\\nRESEARCH\\nTOOLS\\nABOUT\\nOUR SITES\\nNeed Help?\\nSubscribe to the FRED newsletter\\nFollow us NOTES\\nSource:\\nU.S. Bureau of Economic Analysis\\nRelease:\\nGross Domestic Product\\nUnits:\\nBillions of Dollars,\\u00a0Seasonally Adjusted Annual Rate\\nFrequency:\\nQuarterly\\nBEA Account Code: A191RCGross domestic product (GDP), the featured measure of U.S. output, is the market value of the goods and services produced by labor and property located in the United States. U.S. Bureau of Economic Analysis,\\nGross Domestic Product [GDP],\\nretrieved from FRED,\\nFederal Reserve Bank of St. Louis;\\nhttps://fred.stlouisfed.org/series/GDP,\\nJanuary 20, 2024.\\n Gross Domestic Product (GDP)\\nObservation:\\nUnits:\\nFrequency:\\nData in this graph are copyrighted. Federal Reserve Economic Data: Your trusted data source since 1991\\nExplore resources provided by the Research Division at the Federal Reserve Bank of St. Louis.\\n\"}, {\"url\": \"https://www.thebalancemoney.com/us-gdp-by-year-3305543\", \"content\": \"U.S. GDP by Year, Compared to Recessions and Events\\nThe Strange Ups and Downs of the U.S. Economy Since 1929\\nThe Balance / Julie Bang\\nU.S. gross domestic product (GDP) by year is a good overview of economic growth in the United States. Rebasing changes the reference year (or base year) for the real (chained dollar and quantity index) estimates and price indexes and expresses GDP and other NIPA aggregates in terms of the prices of one year. You can compare the GDP by year to fiscal and monetary policies to get a complete picture of what works and what doesn't in the U.S. economy.\\n Real GDP is important because without canceling out the effects of inflation, the GDP could appear to grow, when really all that's happened is an increase in prices.\\n Key Takeaways\\nTypes of GDP\\nThe\\u00a0Bureau of Economic Analysis\\u00a0compiles the data.\"}]\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Name: Researcher\n", "\n", "I have gathered information on the GDP of the United States from 2000 to 2020. Here are the GDP values in billions of dollars for each year:\n", "\n", "- 2000: 10,256.7\n", "- 2001: 10,580.9\n", "- 2002: 10,580.5\n", "- 2003: 10,900.5\n", "- 2004: 11,187.3\n", "- 2005: 11,562.7\n", "- 2006: 11,856.5\n", "- 2007: 12,146.9\n", "- 2008: 12,534.1\n", "- 2009: 12,132.6\n", "- 2010: 14,658.0\n", "- 2011: 15,517.9\n", "- 2012: 16,197.5\n", "- 2013: 16,785.4\n", "- 2014: 17,524.1\n", "- 2015: 18,206.0\n", "- 2016: 18,707.2\n", "- 2017: 19,485.4\n", "- 2018: 20,580.2\n", "- 2019: 21,433.2\n", "- 2020: 21,052.8\n", "\n", "Now, I will generate a line chart using Python to visualize this data. \n", "\n", "```python\n", "import matplotlib.pyplot as plt\n", "\n", "# Years and GDP data\n", "years = list(range(2000, 2021))\n", "gdp_values = [\n", "    10256.7, 10580.9, 10580.5, 10900.5, 11187.3, \n", "    11562.7, 11856.5, 12146.9, 12534.1, 12132.6, \n", "    14658.0, 15517.9, 16197.5, 16785.4, 17524.1, \n", "    18206.0, 18707.2, 19485.4, 20580.2, 21433.2, \n", "    21052.8\n", "]\n", "\n", "# Create the line chart\n", "plt.figure(figsize=(10, 5))\n", "plt.plot(years, gdp_values, marker='o')\n", "plt.title('GDP of the United States (2000-2020)')\n", "plt.xlabel('Year')\n", "plt.ylabel('GDP in Billions of Dollars')\n", "plt.xticks(years, rotation=45)\n", "plt.grid()\n", "plt.tight_layout()\n", "\n", "# Show the chart\n", "plt.show()\n", "```\n", "\n", "This code will generate a line chart showing the GDP of the United States from 2000 to 2020. \n", "\n", "FINAL ANSWER\n"]}], "source": ["events = graph.stream(\n", "    {\n", "        \"messages\": [\n", "            HumanMessage(\n", "                content=\"Obtain the GDP of the United States from 2000 to 2020, \"\n", "            \"and then plot a line chart with <PERSON>. End the task after generating the chart。\"\n", "            )\n", "        ],\n", "    },\n", "    # 设置最大递归限制\n", "    {\"recursion_limit\": 20},\n", "    stream_mode=\"values\"\n", ")\n", "\n", "for event in events:\n", "    if \"messages\" in event:\n", "        event[\"messages\"][-1].pretty_print()  # 打印消息内容\n"]}, {"cell_type": "code", "execution_count": null, "id": "944e1c69-7ee9-4794-9e38-d035cde69021", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "862f39b6-79c7-46a3-b705-8835bc15d0d8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3480064c-3fbe-40ca-bb5a-80403d4b39c9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "6c5551b6-1212-4686-8943-29605b2a783b", "metadata": {}, "source": ["### 手动复现 `python_repl` 工具执行的 Python 代码"]}, {"cell_type": "code", "execution_count": 19, "id": "ffd906fa-4005-480c-b272-75ff955e991b", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "# Years and corresponding GDP values\n", "years = list(range(2000, 2021))\n", "gdp_values = [\n", "    10.25, 10.58, 10.64, 10.86, 11.19, \n", "    11.66, 12.24, 12.54, 12.53, 12.11, \n", "    14.58, 15.17, 15.52, 16.16, 16.78, \n", "    17.36, 17.75, 18.12, 18.71, 21.43, \n", "    21.43\n", "]\n", "\n", "# Plotting the GDP data\n", "plt.figure(figsize=(10, 5))\n", "plt.plot(years, gdp_values, marker='o')\n", "plt.title('GDP of the United States (2000 - 2020)')\n", "plt.xlabel('Year')\n", "plt.ylabel('GDP in Trillions (USD)')\n", "plt.xticks(years, rotation=45)\n", "plt.grid()\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "7057fa47-8044-4e63-9dfd-b3ee7dd4d1f7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "3d89aecb-4d44-46a2-8700-c6817b1cdf39", "metadata": {}, "source": ["## Homework\n", "\n", "1. 使用不同的大模型运行多智能体，对比结果并评选 `gpt-4o` 之下最好的大模型，将所有的大模型和最终结果生成一张表格；\n", "2. 将 `Chart_Generator` 替换为其他功能智能体（如 `table_generator`），为其设计提示词，然后运行查看生成结果。\n", "3. [**可选**]优化研究智能体 `Researcher` 提示词和路由函数 `route` 跳转逻辑，提升图表生成的成功率。"]}, {"cell_type": "code", "execution_count": null, "id": "439f65c8-53ac-46ae-b9d6-aa5cafa93fc6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7ae7de09-9232-4383-b10b-382e7a7a6e1d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a2ac3a66-fa05-464f-955b-f558f179fda2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "313ac070-993e-4917-ad13-1bd274b5b73d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7827953b-f16a-4d23-929a-73a8aca40d57", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b341ec25-8f83-4d28-b052-a35cc2aea2a1", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}