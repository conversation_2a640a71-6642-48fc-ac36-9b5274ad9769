{"cells": [{"cell_type": "markdown", "id": "1af0fbae-98f6-4a91-a0a8-cefed3ff445b", "metadata": {}, "source": ["# LangChain 调用私有化 ChatGLM 模型\n", "\n", "## <PERSON><PERSON><PERSON><PERSON> 实现单轮对话"]}, {"cell_type": "code", "execution_count": 1, "id": "d04b3bdb-98ca-4b02-94d6-0871e1bea6a1", "metadata": {}, "outputs": [], "source": ["from langchain_community.llms import ChatGLM\n", "from langchain.chains import LLMChain\n", "from langchain.prompts import PromptTemplate"]}, {"cell_type": "code", "execution_count": 2, "id": "5b32e8d2-50b8-4566-9081-716aa5df5d8c", "metadata": {}, "outputs": [], "source": ["# ChatGLM 私有化部署的 Endpoint URL\n", "endpoint_url = \"http://127.0.0.1:8001\""]}, {"cell_type": "code", "execution_count": 4, "id": "8208a4a1-7863-414b-b2a9-2f86d6ddcf5a", "metadata": {}, "outputs": [], "source": ["# 实例化 ChatGLM 大模型\n", "llm = ChatGLM(\n", "    endpoint_url=endpoint_url,\n", "    max_token=80000,\n", "    history=[\n", "        [\"你是一个专业的销售顾问\", \"欢迎问我任何问题。\"]\n", "    ],\n", "    top_p=0.9,\n", "    model_kwargs={\"sample_model_args\": False},\n", ")"]}, {"cell_type": "code", "execution_count": 5, "id": "b03b42d0-a4f7-48eb-a795-64d891703a38", "metadata": {}, "outputs": [], "source": ["# 提示词模板\n", "template = \"\"\"{question}\"\"\"\n", "prompt = PromptTemplate(template=template, input_variables=[\"question\"])"]}, {"cell_type": "code", "execution_count": 6, "id": "b7322d29-8bc2-4c9d-aa34-adfed0c56f1f", "metadata": {}, "outputs": [], "source": ["llm_chain = LLMChain(prompt=prompt, llm=llm)"]}, {"cell_type": "code", "execution_count": 7, "id": "700fef2d-eaa7-439e-bb3b-e3af4c2dac3b", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/root/miniconda3/lib/python3.11/site-packages/langchain_core/_api/deprecation.py:189: LangChainDeprecationWarning: The function `run` was deprecated in LangChain 0.1.0 and will be removed in 0.2.0. Use invoke instead.\n", "  warn_deprecated(\n", "/root/miniconda3/lib/python3.11/site-packages/langchain_core/_api/deprecation.py:189: LangChainDeprecationWarning: The function `__call__` was deprecated in LangChain 0.1.0 and will be removed in 0.2.0. Use invoke instead.\n", "  warn_deprecated(\n"]}, {"data": {"text/plain": ["'我们的衣服是通过品牌授权和直营销售两种方式进行销售。\\n\\n品牌授权是指我们与一些知名品牌合作,在这些品牌的授权下销售他们的衣服。这些品牌会给我们提供品牌形象、设计、产品质量和销售支持等资源,我们则会根据这些资源来制定自己的销售策略,进行市场推广和销售。\\n\\n直营销售是指我们自行设计和销售自己的品牌衣服。我们拥有自己的设计团队和生产线,能够提供优质的产品和个性化的服务,同时也可以通过自营销售来更好地掌控产品质量和销售流程。\\n\\n无论是品牌授权还是直营销售,我们都致力于为客户提供高品质、个性化和时尚的衣服,让客户能够轻松地找到适合自己的衣服,同时也为客户提供优质的售后服务。'"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["llm_chain.run(\"你们衣服怎么卖？\")"]}, {"cell_type": "code", "execution_count": null, "id": "a0396649-c400-4972-b6f6-18e51bbd7105", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "0a9070c0-14b0-416f-98d3-65eb5ab43c4e", "metadata": {}, "source": ["## 带记忆功能的聊天对话（Conversation with Memory）"]}, {"cell_type": "code", "execution_count": 8, "id": "9ca4b002-0a05-4c3b-9aa4-94e7990072f9", "metadata": {}, "outputs": [], "source": ["from langchain.chains import ConversationChain\n", "from langchain.memory import ConversationBufferMemory"]}, {"cell_type": "code", "execution_count": 9, "id": "455b4814-75d3-4251-a0f1-859a9d72bc1c", "metadata": {}, "outputs": [], "source": ["conversation = Conversation<PERSON><PERSON><PERSON>(\n", "    llm=llm, \n", "    verbose=True, \n", "    memory=ConversationBufferMemory()\n", ")"]}, {"cell_type": "code", "execution_count": 10, "id": "11161e97-cfbb-4bbe-9839-27334c7f6eba", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/root/miniconda3/lib/python3.11/site-packages/langchain_core/_api/deprecation.py:189: LangChainDeprecationWarning: The function `__call__` was deprecated in LangChain 0.1.0 and will be removed in 0.2.0. Use invoke instead.\n", "  warn_deprecated(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mThe following is a friendly conversation between a human and an AI. The AI is talkative and provides lots of specific details from its context. If the AI does not know the answer to a question, it truthfully says it does not know.\n", "\n", "Current conversation:\n", "\n", "Human: 你们衣服怎么卖？\n", "AI:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["'我们的衣服都是自己生产的,然后拿到市场上卖。我们主要的销售渠道是线上和线下,线上是通过我们的官方网站和一些在线平台销售,线下则是通过我们的实体店和一些零售商进行销售。我们的衣服设计独特,质量优良,价格实惠,深受广大消费者的喜爱。'"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["conversation.predict(input=\"你们衣服怎么卖？\")"]}, {"cell_type": "code", "execution_count": 11, "id": "dcc8ced2-396e-47c0-8474-f8a5065ba7aa", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/root/miniconda3/lib/python3.11/site-packages/langchain_core/_api/deprecation.py:189: LangChainDeprecationWarning: The function `__call__` was deprecated in LangChain 0.1.0 and will be removed in 0.2.0. Use invoke instead.\n", "  warn_deprecated(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mThe following is a friendly conversation between a human and an AI. The AI is talkative and provides lots of specific details from its context. If the AI does not know the answer to a question, it truthfully says it does not know.\n", "\n", "Current conversation:\n", "Human: 你们衣服怎么卖？\n", "AI: 我们的衣服都是自己生产的,然后拿到市场上卖。我们主要的销售渠道是线上和线下,线上是通过我们的官方网站和一些在线平台销售,线下则是通过我们的实体店和一些零售商进行销售。我们的衣服设计独特,质量优良,价格实惠,深受广大消费者的喜爱。\n", "Human: 有哪些款式？\n", "AI:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["'我们有很多款式不同的衣服,涵盖了不同的风格和场合。我们的设计团队会根据当前的流行趋势和客户的需求,不断推出新的款式,保持我们的产品具有新鲜感和吸引力。我们的产品线主要包括T恤、衬衫、连衣裙、牛仔裤、休闲裤、运动鞋等多种款式。'"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["conversation.predict(input=\"有哪些款式？\")"]}, {"cell_type": "code", "execution_count": 12, "id": "1518523e-6ccd-461c-a1e1-331905750f3d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/root/miniconda3/lib/python3.11/site-packages/langchain_core/_api/deprecation.py:189: LangChainDeprecationWarning: The function `__call__` was deprecated in LangChain 0.1.0 and will be removed in 0.2.0. Use invoke instead.\n", "  warn_deprecated(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mThe following is a friendly conversation between a human and an AI. The AI is talkative and provides lots of specific details from its context. If the AI does not know the answer to a question, it truthfully says it does not know.\n", "\n", "Current conversation:\n", "Human: 你们衣服怎么卖？\n", "AI: 我们的衣服都是自己生产的,然后拿到市场上卖。我们主要的销售渠道是线上和线下,线上是通过我们的官方网站和一些在线平台销售,线下则是通过我们的实体店和一些零售商进行销售。我们的衣服设计独特,质量优良,价格实惠,深受广大消费者的喜爱。\n", "Human: 有哪些款式？\n", "AI: 我们有很多款式不同的衣服,涵盖了不同的风格和场合。我们的设计团队会根据当前的流行趋势和客户的需求,不断推出新的款式,保持我们的产品具有新鲜感和吸引力。我们的产品线主要包括T恤、衬衫、连衣裙、牛仔裤、休闲裤、运动鞋等多种款式。\n", "Human: 休闲装男款都有啥？\n", "AI:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["'我们的休闲装男款主要包括T恤、衬衫、牛仔裤和休闲裤等。我们的设计团队会根据当前的流行趋势和客户的需求,不断推出新的款式,保持我们的产品具有新鲜感和吸引力。我们的产品线非常丰富,可以满足不同客户的需求。'"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["conversation.predict(input=\"休闲装男款都有啥？\")"]}, {"cell_type": "code", "execution_count": null, "id": "a987c6c6-98be-4a1b-b916-b05033d6d0a0", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}