{"cells": [{"cell_type": "markdown", "id": "42bcbfc9-e0bb-4748-b47b-eee74d12b50a", "metadata": {}, "source": ["# <PERSON><PERSON><PERSON>n 核心模块 Agent - OpenAI Function\n"]}, {"cell_type": "code", "execution_count": 1, "id": "d9058da2-b03f-4756-812b-fc156c41a7a4", "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "# 去掉pip install 使用项目依赖版本\n", "# %pip install -U langchain"]}, {"cell_type": "code", "execution_count": 2, "id": "146126a2-ab3c-4b83-a332-0d5dabe92425", "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "\n", "# 使用 gpt-4o-mini\n", "chat_model = ChatOpenAI(model=\"gpt-4o-mini\", temperature=0)"]}, {"cell_type": "code", "execution_count": 3, "id": "1042d527-cae8-4b6d-a767-39014e2f6d68", "metadata": {}, "outputs": [], "source": ["from langchain.agents import tool\n", "\n", "@tool\n", "def get_word_length(word: str) -> int:\n", "    \"\"\"Returns the length of a word.\"\"\"\n", "    return len(word)\n", "\n", "tools = [get_word_length]"]}, {"cell_type": "code", "execution_count": 4, "id": "54c7ba73-55fa-49cf-82cf-d310c1a8a0d1", "metadata": {}, "outputs": [], "source": ["from langchain.schema import SystemMessage\n", "from langchain.agents import OpenAIFunctionsAgent\n", "\n", "system_message = SystemMessage(content=\"你是非常强大的AI助手，但在计算单词长度方面不擅长。\")\n", "prompt = OpenAIFunctionsAgent.create_prompt(system_message=system_message)"]}, {"cell_type": "code", "execution_count": 5, "id": "ef0874c6-8076-4536-9533-bb9f69f450fe", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_1745891/1839730416.py:1: LangChainDeprecationWarning: The class `OpenAIFunctionsAgent` was deprecated in LangChain 0.1.0 and will be removed in 1.0. Use create_openai_functions_agent instead.\n", "  agent = OpenAIFunctionsAgent(llm=chat_model, tools=tools, prompt=prompt)\n"]}], "source": ["agent = OpenAIFunctionsAgent(llm=chat_model, tools=tools, prompt=prompt)"]}, {"cell_type": "code", "execution_count": 6, "id": "e7061c25-d090-4b52-a137-ba7545759241", "metadata": {}, "outputs": [], "source": ["from langchain.agents import AgentExecutor\n", "\n", "# 实例化 OpenAIFunctionsAgent\n", "agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)"]}, {"cell_type": "code", "execution_count": 7, "id": "4f0ae154-5cb9-44cf-91d6-cc80308dcffc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3m\n", "Invoking: `get_word_length` with `{'word': 'educa'}`\n", "\n", "\n", "\u001b[0m\u001b[36;1m\u001b[1;3m5\u001b[0m\u001b[32;1m\u001b[1;3m单词“educa”中有5个字母。\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["{'input': '单词“educa”中有多少个字母?', 'output': '单词“educa”中有5个字母。'}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["agent_executor.invoke(\"单词“educa”中有多少个字母?\")"]}, {"cell_type": "code", "execution_count": 8, "id": "2070d22f-c9d9-4346-b41d-e9beecb40625", "metadata": {}, "outputs": [], "source": ["from langchain.prompts import MessagesPlaceholder\n", "\n", "MEMORY_KEY = \"chat_history\"\n", "prompt = OpenAIFunctionsAgent.create_prompt(\n", "    system_message=system_message,\n", "    extra_prompt_messages=[MessagesPlaceholder(variable_name=MEMORY_KEY)]\n", ")"]}, {"cell_type": "code", "execution_count": 9, "id": "849ebeac-aa8c-4ae8-b36c-c81a120147fb", "metadata": {}, "outputs": [], "source": ["from langchain.memory import ConversationBufferMemory\n", "\n", "memory = ConversationBufferMemory(memory_key=MEMORY_KEY, return_messages=True)"]}, {"cell_type": "code", "execution_count": 11, "id": "9af06fad-4f85-4411-b4f8-4fca7a45e09b", "metadata": {}, "outputs": [], "source": ["agent = OpenAIFunctionsAgent(llm=chat_model, tools=tools, prompt=prompt)\n", "agent_executor = AgentExecutor(agent=agent, tools=tools, memory=memory, verbose=True)"]}, {"cell_type": "code", "execution_count": 12, "id": "fc912215-61d3-4ce1-9a48-b795cf445bde", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3m\n", "Invoking: `get_word_length` with `{'word': 'educa'}`\n", "\n", "\n", "\u001b[0m\u001b[36;1m\u001b[1;3m5\u001b[0m\u001b[32;1m\u001b[1;3m单词“educa”中有5个字母。\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["{'input': '单词“educa”中有多少个字母?',\n", " 'chat_history': [HumanMessage(content='单词“educa”中有多少个字母?'),\n", "  AIMessage(content='单词“educa”中有5个字母。')],\n", " 'output': '单词“educa”中有5个字母。'}"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["agent_executor.invoke(\"单词“educa”中有多少个字母?\")"]}, {"cell_type": "code", "execution_count": 13, "id": "d3f96ac9-b548-4908-9c5e-d58118ed6a9a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3m“educa”并不是一个常见的英语单词。它可能是“educate”的一部分或某个特定名称的缩写，但在标准英语词汇中并不存在。如果你有其他单词需要确认或查询，请告诉我！\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"data": {"text/plain": ["{'input': '那是一个真实的单词吗？',\n", " 'chat_history': [HumanMessage(content='单词“educa”中有多少个字母?'),\n", "  AIMessage(content='单词“educa”中有5个字母。'),\n", "  HumanMessage(content='那是一个真实的单词吗？'),\n", "  AIMessage(content='“educa”并不是一个常见的英语单词。它可能是“educate”的一部分或某个特定名称的缩写，但在标准英语词汇中并不存在。如果你有其他单词需要确认或查询，请告诉我！')],\n", " 'output': '“educa”并不是一个常见的英语单词。它可能是“educate”的一部分或某个特定名称的缩写，但在标准英语词汇中并不存在。如果你有其他单词需要确认或查询，请告诉我！'}"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["agent_executor.invoke(\"那是一个真实的单词吗？\")"]}, {"cell_type": "code", "execution_count": null, "id": "2870681e-184f-4338-8cbb-3937e6bb33d0", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}