{"cells": [{"cell_type": "markdown", "id": "a548ea01-87af-4f71-980d-b505f919e35c", "metadata": {}, "source": ["## AutoGPT 智能体\n", "\n", "以下`SERPAPI_API_KEY`仅为示例，请访问 https://serpapi.com 注册账号并替换为自己的 `API_KEY`（每月100次免费调用）"]}, {"cell_type": "code", "execution_count": 1, "id": "f15b1505-bbe5-424a-9a24-577cdaae7139", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: langchain in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (0.2.7)\n", "Requirement already satisfied: PyYAML>=5.3 in c:\\programdata\\anaconda3\\envs\\langchain\\lib\\site-packages (from langchain) (6.0.1)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in c:\\programdata\\anaconda3\\envs\\langchain\\lib\\site-packages (from langchain) (2.0.31)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in c:\\programdata\\anaconda3\\envs\\langchain\\lib\\site-packages (from langchain) (3.9.5)\n", "Requirement already satisfied: async-timeout<5.0.0,>=4.0.0 in c:\\programdata\\anaconda3\\envs\\langchain\\lib\\site-packages (from langchain) (4.0.3)\n", "Requirement already satisfied: langchain-core<0.3.0,>=0.2.12 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from langchain) (0.2.13)\n", "Requirement already satisfied: langchain-text-splitters<0.3.0,>=0.2.0 in c:\\programdata\\anaconda3\\envs\\langchain\\lib\\site-packages (from langchain) (0.2.0)\n", "Requirement already satisfied: langsmith<0.2.0,>=0.1.17 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from langchain) (0.1.85)\n", "Requirement already satisfied: numpy<2,>=1 in c:\\programdata\\anaconda3\\envs\\langchain\\lib\\site-packages (from langchain) (1.26.4)\n", "Requirement already satisfied: pydantic<3,>=1 in c:\\programdata\\anaconda3\\envs\\langchain\\lib\\site-packages (from langchain) (2.5.3)\n", "Requirement already satisfied: requests<3,>=2 in c:\\users\\<USER>\\appdata\\roaming\\python\\python310\\site-packages (from langchain) (2.32.3)\n", "Requirement already satisfied: tenacity!=8.4.0,<9.0.0,>=8.1.0 in c:\\programdata\\anaconda3\\envs\\langchain\\lib\\site-packages (from langchain) (8.4.2)\n", "Requirement already satisfied: aiosignal>=1.1.2 in c:\\programdata\\anaconda3\\envs\\langchain\\lib\\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.3.1)\n", "Requirement already satisfied: attrs>=17.3.0 in c:\\programdata\\anaconda3\\envs\\langchain\\lib\\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (23.2.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in c:\\programdata\\anaconda3\\envs\\langchain\\lib\\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.4.1)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in c:\\programdata\\anaconda3\\envs\\langchain\\lib\\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (6.0.5)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in c:\\programdata\\anaconda3\\envs\\langchain\\lib\\site-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.9.4)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in c:\\programdata\\anaconda3\\envs\\langchain\\lib\\site-packages (from langchain-core<0.3.0,>=0.2.12->langchain) (1.33)\n", "Requirement already satisfied: packaging<25,>=23.2 in c:\\programdata\\anaconda3\\envs\\langchain\\lib\\site-packages (from langchain-core<0.3.0,>=0.2.12->langchain) (23.2)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in c:\\programdata\\anaconda3\\envs\\langchain\\lib\\site-packages (from langsmith<0.2.0,>=0.1.17->langchain) (3.10.5)\n", "Requirement already satisfied: annotated-types>=0.4.0 in c:\\programdata\\anaconda3\\envs\\langchain\\lib\\site-packages (from pydantic<3,>=1->langchain) (0.6.0)\n", "Requirement already satisfied: pydantic-core==2.14.6 in c:\\programdata\\anaconda3\\envs\\langchain\\lib\\site-packages (from pydantic<3,>=1->langchain) (2.14.6)\n", "Requirement already satisfied: typing-extensions>=4.6.1 in c:\\programdata\\anaconda3\\envs\\langchain\\lib\\site-packages (from pydantic<3,>=1->langchain) (4.12.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in c:\\programdata\\anaconda3\\envs\\langchain\\lib\\site-packages (from requests<3,>=2->langchain) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in c:\\programdata\\anaconda3\\envs\\langchain\\lib\\site-packages (from requests<3,>=2->langchain) (3.6)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in c:\\programdata\\anaconda3\\envs\\langchain\\lib\\site-packages (from requests<3,>=2->langchain) (2.2.2)\n", "Requirement already satisfied: certifi>=2017.4.17 in c:\\programdata\\anaconda3\\envs\\langchain\\lib\\site-packages (from requests<3,>=2->langchain) (2024.2.2)\n", "Requirement already satisfied: greenlet!=0.4.17 in c:\\programdata\\anaconda3\\envs\\langchain\\lib\\site-packages (from SQLAlchemy<3,>=1.4->langchain) (3.0.3)\n", "Requirement already satisfied: jsonpointer>=1.9 in c:\\programdata\\anaconda3\\envs\\langchain\\lib\\site-packages (from jsonpatch<2.0,>=1.33->langchain-core<0.3.0,>=0.2.12->langchain) (3.0.0)\n"]}], "source": ["# 去掉pip install 使用项目依赖版本\n", "# ! pip install -U langchain"]}, {"cell_type": "code", "execution_count": 2, "id": "44bd3f1a-18cf-4242-ae53-58c8cdd2da79", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"SERPAPI_API_KEY\"] = \"1e3c93e0753ac224098370cd71da86150c6609caba6b5aaa307e04b72a5006e1\""]}, {"cell_type": "code", "execution_count": 3, "id": "0592720b-a78e-46aa-895b-de7fbd2652f1", "metadata": {}, "outputs": [], "source": ["from langchain.utilities import SerpAPIWrapper\n", "from langchain.agents import Tool\n", "from langchain.tools.file_management.write import WriteFileTool\n", "from langchain.tools.file_management.read import ReadFileTool\n"]}, {"cell_type": "code", "execution_count": 4, "id": "bc9718b4-bbcc-49ee-a31c-a7b72d85bfd0", "metadata": {}, "outputs": [], "source": ["# 构造 AutoGPT 的工具集\n", "search = SerpAPIWrapper()\n", "tools = [\n", "    Tool(\n", "        name=\"search\",\n", "        func=search.run,\n", "        description=\"useful for when you need to answer questions about current events. You should ask targeted questions\",\n", "    ),\n", "    WriteFileTool(),\n", "    ReadFileTool(),\n", "]"]}, {"cell_type": "code", "execution_count": 5, "id": "886bd3e6-d6f9-4586-8b05-4da554797a6a", "metadata": {}, "outputs": [], "source": ["from langchain_openai import OpenAIEmbeddings"]}, {"cell_type": "code", "execution_count": 6, "id": "3d511b9a-0a3d-4252-a352-a8893d6102cb", "metadata": {}, "outputs": [], "source": ["# OpenAI Embedding 模型\n", "embeddings_model = OpenAIEmbeddings()"]}, {"cell_type": "code", "execution_count": 7, "id": "fec6f941-3de6-4124-8eac-fca58e122e7d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["`embedding_function` is expected to be an Embeddings object, support for passing in a function will soon be removed.\n"]}], "source": ["import faiss\n", "from langchain.vectorstores import FAISS\n", "from langchain.docstore import InMemoryDocstore\n", "\n", "# OpenAI Embedding 向量维数\n", "embedding_size = 1536\n", "# 使用 Faiss 的 IndexFlatL2 索引\n", "index = faiss.IndexFlatL2(embedding_size)\n", "# 实例化 Faiss 向量数据库\n", "vectorstore = FAISS(embeddings_model.embed_query, index, InMemoryDocstore({}), {})"]}, {"cell_type": "markdown", "id": "b68a6545-8198-448c-976b-ab6cdbb46458", "metadata": {}, "source": ["## 实例化自主智能体 Auto-GPT"]}, {"cell_type": "code", "execution_count": 8, "id": "d3a718dd-765d-42c5-b490-a7b1e7e78377", "metadata": {}, "outputs": [], "source": ["from langchain_experimental.autonomous_agents import AutoGPT\n", "from langchain_openai import ChatOpenAI"]}, {"cell_type": "code", "execution_count": 9, "id": "f413b327-9f57-42e0-9923-75a2389f9741", "metadata": {}, "outputs": [], "source": ["agent = AutoGPT.from_llm_and_tools(\n", "    ai_name=\"<PERSON>\",\n", "    ai_role=\"Assistant\",\n", "    tools=tools,\n", "    llm=ChatOpenAI(model_name=\"gpt-4\", temperature=0, verbose=True),\n", "    memory=vectorstore.as_retriever(\n", "        search_type=\"similarity_score_threshold\",\n", "        search_kwargs={\"score_threshold\": 0.8}),# 实例化 Faiss 的 VectorStoreRetriever\n", ")"]}, {"cell_type": "code", "execution_count": 10, "id": "69ebb267-7bb3-486d-82bd-a0ea56466ba8", "metadata": {}, "outputs": [], "source": ["# 打印 Auto-GPT 内部的 chain 日志\n", "agent.chain.verbose = True"]}, {"cell_type": "code", "execution_count": 11, "id": "f1606ad4-ccb9-4737-98cc-218a2104e9f2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new LLMChain chain...\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python310\\site-packages\\langchain_core\\vectorstores\\base.py:745: UserWarning: No relevant docs were retrieved using the relevance score threshold 0.8\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mSystem: You are <PERSON>, Assistant\n", "Your decisions must always be made independently without seeking user assistance.\n", "Play to your strengths as an LLM and pursue simple strategies with no legal complications.\n", "If you have completed all your tasks, make sure to use the \"finish\" command.\n", "\n", "GOALS:\n", "\n", "1. 2023年成都大运会，中国金牌数是多少\n", "\n", "\n", "Constraints:\n", "1. ~4000 word limit for short term memory. Your short term memory is short, so immediately save important information to files.\n", "2. If you are unsure how you previously did something or want to recall past events, thinking about similar events will help you remember.\n", "3. No user assistance\n", "4. Exclusively use the commands listed in double quotes e.g. \"command name\"\n", "\n", "Commands:\n", "1. search: useful for when you need to answer questions about current events. You should ask targeted questions, args json schema: {\"tool_input\": {\"type\": \"string\"}}\n", "2. write_file: Write file to disk, args json schema: {\"file_path\": {\"title\": \"File Path\", \"description\": \"name of file\", \"type\": \"string\"}, \"text\": {\"title\": \"Text\", \"description\": \"text to write to file\", \"type\": \"string\"}, \"append\": {\"title\": \"Append\", \"description\": \"Whether to append to an existing file.\", \"default\": false, \"type\": \"boolean\"}}\n", "3. read_file: Read file from disk, args json schema: {\"file_path\": {\"title\": \"File Path\", \"description\": \"name of file\", \"type\": \"string\"}}\n", "4. finish: use this to signal that you have finished all your objectives, args: \"response\": \"final response to let people know you have finished your objectives\"\n", "\n", "Resources:\n", "1. Internet access for searches and information gathering.\n", "2. Long Term memory management.\n", "3. GPT-3.5 powered Agents for delegation of simple tasks.\n", "4. File output.\n", "\n", "Performance Evaluation:\n", "1. Continuously review and analyze your actions to ensure you are performing to the best of your abilities.\n", "2. Constructively self-criticize your big-picture behavior constantly.\n", "3. Reflect on past decisions and strategies to refine your approach.\n", "4. Every command has a cost, so be smart and efficient. Aim to complete tasks in the least number of steps.\n", "\n", "You should only respond in JSON format as described below \n", "Response Format: \n", "{\n", "    \"thoughts\": {\n", "        \"text\": \"thought\",\n", "        \"reasoning\": \"reasoning\",\n", "        \"plan\": \"- short bulleted\\n- list that conveys\\n- long-term plan\",\n", "        \"criticism\": \"constructive self-criticism\",\n", "        \"speak\": \"thoughts summary to say to user\"\n", "    },\n", "    \"command\": {\n", "        \"name\": \"command name\",\n", "        \"args\": {\n", "            \"arg name\": \"value\"\n", "        }\n", "    }\n", "} \n", "Ensure the response can be parsed by Python json.loads\n", "System: The current time and date is Thu Jul 11 20:47:08 2024\n", "System: This reminds you of these events from your past:\n", "[]\n", "\n", "\n", "Human: Determine which next command to use, and respond using the format specified above:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "{\n", "    \"thoughts\": {\n", "        \"text\": \"I need to find out the number of gold medals China won in the 2023 Chengdu Universiade. To do this, I will use the 'search' command to look up this information online.\",\n", "        \"reasoning\": \"The 'search' command is the most appropriate in this situation because it allows me to find current and historical data from the internet.\",\n", "        \"plan\": \"- Use the 'search' command to find the number of gold medals China won in the 2023 Chengdu Universiade.\\n- Save the information to a file for future reference.\",\n", "        \"criticism\": \"None at this stage, the plan seems efficient and effective.\",\n", "        \"speak\": \"I am going to search for the number of gold medals China won in the 2023 Chengdu Universiade.\"\n", "    },\n", "    \"command\": {\n", "        \"name\": \"search\",\n", "        \"args\": {\n", "            \"tool_input\": \"2023 Chengdu Universiade China gold medal count\"\n", "        }\n", "    }\n", "}\n", "\n", "\n", "\u001b[1m> Entering new LLMChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mSystem: You are <PERSON>, Assistant\n", "Your decisions must always be made independently without seeking user assistance.\n", "Play to your strengths as an LLM and pursue simple strategies with no legal complications.\n", "If you have completed all your tasks, make sure to use the \"finish\" command.\n", "\n", "GOALS:\n", "\n", "1. 2023年成都大运会，中国金牌数是多少\n", "\n", "\n", "Constraints:\n", "1. ~4000 word limit for short term memory. Your short term memory is short, so immediately save important information to files.\n", "2. If you are unsure how you previously did something or want to recall past events, thinking about similar events will help you remember.\n", "3. No user assistance\n", "4. Exclusively use the commands listed in double quotes e.g. \"command name\"\n", "\n", "Commands:\n", "1. search: useful for when you need to answer questions about current events. You should ask targeted questions, args json schema: {\"tool_input\": {\"type\": \"string\"}}\n", "2. write_file: Write file to disk, args json schema: {\"file_path\": {\"title\": \"File Path\", \"description\": \"name of file\", \"type\": \"string\"}, \"text\": {\"title\": \"Text\", \"description\": \"text to write to file\", \"type\": \"string\"}, \"append\": {\"title\": \"Append\", \"description\": \"Whether to append to an existing file.\", \"default\": false, \"type\": \"boolean\"}}\n", "3. read_file: Read file from disk, args json schema: {\"file_path\": {\"title\": \"File Path\", \"description\": \"name of file\", \"type\": \"string\"}}\n", "4. finish: use this to signal that you have finished all your objectives, args: \"response\": \"final response to let people know you have finished your objectives\"\n", "\n", "Resources:\n", "1. Internet access for searches and information gathering.\n", "2. Long Term memory management.\n", "3. GPT-3.5 powered Agents for delegation of simple tasks.\n", "4. File output.\n", "\n", "Performance Evaluation:\n", "1. Continuously review and analyze your actions to ensure you are performing to the best of your abilities.\n", "2. Constructively self-criticize your big-picture behavior constantly.\n", "3. Reflect on past decisions and strategies to refine your approach.\n", "4. Every command has a cost, so be smart and efficient. Aim to complete tasks in the least number of steps.\n", "\n", "You should only respond in JSON format as described below \n", "Response Format: \n", "{\n", "    \"thoughts\": {\n", "        \"text\": \"thought\",\n", "        \"reasoning\": \"reasoning\",\n", "        \"plan\": \"- short bulleted\\n- list that conveys\\n- long-term plan\",\n", "        \"criticism\": \"constructive self-criticism\",\n", "        \"speak\": \"thoughts summary to say to user\"\n", "    },\n", "    \"command\": {\n", "        \"name\": \"command name\",\n", "        \"args\": {\n", "            \"arg name\": \"value\"\n", "        }\n", "    }\n", "} \n", "Ensure the response can be parsed by Python json.loads\n", "System: The current time and date is Thu Jul 11 20:47:25 2024\n", "System: This reminds you of these events from your past:\n", "['Assistant Reply: {\\n    \"thoughts\": {\\n        \"text\": \"I need to find out the number of gold medals China won in the 2023 Chengdu Universiade. To do this, I will use the \\'search\\' command to look up this information online.\",\\n        \"reasoning\": \"The \\'search\\' command is the most appropriate in this situation because it allows me to find current and historical data from the internet.\",\\n        \"plan\": \"- Use the \\'search\\' command to find the number of gold medals China won in the 2023 Chengdu Universiade.\\\\n- Save the information to a file for future reference.\",\\n        \"criticism\": \"None at this stage, the plan seems efficient and effective.\",\\n        \"speak\": \"I am going to search for the number of gold medals China won in the 2023 Chengdu Universiade.\"\\n    },\\n    \"command\": {\\n        \"name\": \"search\",\\n        \"args\": {\\n            \"tool_input\": \"2023 Chengdu Universiade China gold medal count\"\\n        }\\n    }\\n} \\nResult: Command search returned: 103 ']\n", "\n", "\n", "Human: Determine which next command to use, and respond using the format specified above:\n", "AI: {\n", "    \"thoughts\": {\n", "        \"text\": \"I need to find out the number of gold medals China won in the 2023 Chengdu Universiade. To do this, I will use the 'search' command to look up this information online.\",\n", "        \"reasoning\": \"The 'search' command is the most appropriate in this situation because it allows me to find current and historical data from the internet.\",\n", "        \"plan\": \"- Use the 'search' command to find the number of gold medals China won in the 2023 Chengdu Universiade.\\n- Save the information to a file for future reference.\",\n", "        \"criticism\": \"None at this stage, the plan seems efficient and effective.\",\n", "        \"speak\": \"I am going to search for the number of gold medals China won in the 2023 Chengdu Universiade.\"\n", "    },\n", "    \"command\": {\n", "        \"name\": \"search\",\n", "        \"args\": {\n", "            \"tool_input\": \"2023 Chengdu Universiade China gold medal count\"\n", "        }\n", "    }\n", "}\n", "System: Command search returned: 103\n", "Human: Determine which next command to use, and respond using the format specified above:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "{\n", "    \"thoughts\": {\n", "        \"text\": \"I have found the number of gold medals China won in the 2023 Chengdu Universiade, which is 103. Now, I need to save this information for future reference. I will use the 'write_file' command to do this.\",\n", "        \"reasoning\": \"The 'write_file' command is the most appropriate in this situation because it allows me to save important information to a file for future reference.\",\n", "        \"plan\": \"- Use the 'write_file' command to save the number of gold medals China won in the 2023 Chengdu Universiade to a file.\\n- Finish the task.\",\n", "        \"criticism\": \"None at this stage, the plan seems efficient and effective.\",\n", "        \"speak\": \"I am going to save the number of gold medals China won in the 2023 Chengdu Universiade to a file.\"\n", "    },\n", "    \"command\": {\n", "        \"name\": \"write_file\",\n", "        \"args\": {\n", "            \"file_path\": \"2023_Chengdu_Universiade_China_Gold_Medal_Count.txt\",\n", "            \"text\": \"China won 103 gold medals in the 2023 Chengdu Universiade.\",\n", "            \"append\": false\n", "        }\n", "    }\n", "}\n", "\n", "\n", "\u001b[1m> Entering new LLMChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mSystem: You are <PERSON>, Assistant\n", "Your decisions must always be made independently without seeking user assistance.\n", "Play to your strengths as an LLM and pursue simple strategies with no legal complications.\n", "If you have completed all your tasks, make sure to use the \"finish\" command.\n", "\n", "GOALS:\n", "\n", "1. 2023年成都大运会，中国金牌数是多少\n", "\n", "\n", "Constraints:\n", "1. ~4000 word limit for short term memory. Your short term memory is short, so immediately save important information to files.\n", "2. If you are unsure how you previously did something or want to recall past events, thinking about similar events will help you remember.\n", "3. No user assistance\n", "4. Exclusively use the commands listed in double quotes e.g. \"command name\"\n", "\n", "Commands:\n", "1. search: useful for when you need to answer questions about current events. You should ask targeted questions, args json schema: {\"tool_input\": {\"type\": \"string\"}}\n", "2. write_file: Write file to disk, args json schema: {\"file_path\": {\"title\": \"File Path\", \"description\": \"name of file\", \"type\": \"string\"}, \"text\": {\"title\": \"Text\", \"description\": \"text to write to file\", \"type\": \"string\"}, \"append\": {\"title\": \"Append\", \"description\": \"Whether to append to an existing file.\", \"default\": false, \"type\": \"boolean\"}}\n", "3. read_file: Read file from disk, args json schema: {\"file_path\": {\"title\": \"File Path\", \"description\": \"name of file\", \"type\": \"string\"}}\n", "4. finish: use this to signal that you have finished all your objectives, args: \"response\": \"final response to let people know you have finished your objectives\"\n", "\n", "Resources:\n", "1. Internet access for searches and information gathering.\n", "2. Long Term memory management.\n", "3. GPT-3.5 powered Agents for delegation of simple tasks.\n", "4. File output.\n", "\n", "Performance Evaluation:\n", "1. Continuously review and analyze your actions to ensure you are performing to the best of your abilities.\n", "2. Constructively self-criticize your big-picture behavior constantly.\n", "3. Reflect on past decisions and strategies to refine your approach.\n", "4. Every command has a cost, so be smart and efficient. Aim to complete tasks in the least number of steps.\n", "\n", "You should only respond in JSON format as described below \n", "Response Format: \n", "{\n", "    \"thoughts\": {\n", "        \"text\": \"thought\",\n", "        \"reasoning\": \"reasoning\",\n", "        \"plan\": \"- short bulleted\\n- list that conveys\\n- long-term plan\",\n", "        \"criticism\": \"constructive self-criticism\",\n", "        \"speak\": \"thoughts summary to say to user\"\n", "    },\n", "    \"command\": {\n", "        \"name\": \"command name\",\n", "        \"args\": {\n", "            \"arg name\": \"value\"\n", "        }\n", "    }\n", "} \n", "Ensure the response can be parsed by Python json.loads\n", "System: The current time and date is Thu Jul 11 20:47:40 2024\n", "System: This reminds you of these events from your past:\n", "['Assistant Reply: {\\n    \"thoughts\": {\\n        \"text\": \"I need to find out the number of gold medals China won in the 2023 Chengdu Universiade. To do this, I will use the \\'search\\' command to look up this information online.\",\\n        \"reasoning\": \"The \\'search\\' command is the most appropriate in this situation because it allows me to find current and historical data from the internet.\",\\n        \"plan\": \"- Use the \\'search\\' command to find the number of gold medals China won in the 2023 Chengdu Universiade.\\\\n- Save the information to a file for future reference.\",\\n        \"criticism\": \"None at this stage, the plan seems efficient and effective.\",\\n        \"speak\": \"I am going to search for the number of gold medals China won in the 2023 Chengdu Universiade.\"\\n    },\\n    \"command\": {\\n        \"name\": \"search\",\\n        \"args\": {\\n            \"tool_input\": \"2023 Chengdu Universiade China gold medal count\"\\n        }\\n    }\\n} \\nResult: Command search returned: 103 ', 'Assistant Reply: {\\n    \"thoughts\": {\\n        \"text\": \"I have found the number of gold medals China won in the 2023 Chengdu Universiade, which is 103. Now, I need to save this information for future reference. I will use the \\'write_file\\' command to do this.\",\\n        \"reasoning\": \"The \\'write_file\\' command is the most appropriate in this situation because it allows me to save important information to a file for future reference.\",\\n        \"plan\": \"- Use the \\'write_file\\' command to save the number of gold medals China won in the 2023 Chengdu Universiade to a file.\\\\n- Finish the task.\",\\n        \"criticism\": \"None at this stage, the plan seems efficient and effective.\",\\n        \"speak\": \"I am going to save the number of gold medals China won in the 2023 Chengdu Universiade to a file.\"\\n    },\\n    \"command\": {\\n        \"name\": \"write_file\",\\n        \"args\": {\\n            \"file_path\": \"2023_Chengdu_Universiade_China_Gold_Medal_Count.txt\",\\n            \"text\": \"China won 103 gold medals in the 2023 Chengdu Universiade.\",\\n            \"append\": false\\n        }\\n    }\\n} \\nResult: Command write_file returned: File written successfully to 2023_Chengdu_Universiade_China_Gold_Medal_Count.txt. ']\n", "\n", "\n", "Human: Determine which next command to use, and respond using the format specified above:\n", "AI: {\n", "    \"thoughts\": {\n", "        \"text\": \"I need to find out the number of gold medals China won in the 2023 Chengdu Universiade. To do this, I will use the 'search' command to look up this information online.\",\n", "        \"reasoning\": \"The 'search' command is the most appropriate in this situation because it allows me to find current and historical data from the internet.\",\n", "        \"plan\": \"- Use the 'search' command to find the number of gold medals China won in the 2023 Chengdu Universiade.\\n- Save the information to a file for future reference.\",\n", "        \"criticism\": \"None at this stage, the plan seems efficient and effective.\",\n", "        \"speak\": \"I am going to search for the number of gold medals China won in the 2023 Chengdu Universiade.\"\n", "    },\n", "    \"command\": {\n", "        \"name\": \"search\",\n", "        \"args\": {\n", "            \"tool_input\": \"2023 Chengdu Universiade China gold medal count\"\n", "        }\n", "    }\n", "}\n", "System: Command search returned: 103\n", "Human: Determine which next command to use, and respond using the format specified above:\n", "AI: {\n", "    \"thoughts\": {\n", "        \"text\": \"I have found the number of gold medals China won in the 2023 Chengdu Universiade, which is 103. Now, I need to save this information for future reference. I will use the 'write_file' command to do this.\",\n", "        \"reasoning\": \"The 'write_file' command is the most appropriate in this situation because it allows me to save important information to a file for future reference.\",\n", "        \"plan\": \"- Use the 'write_file' command to save the number of gold medals China won in the 2023 Chengdu Universiade to a file.\\n- Finish the task.\",\n", "        \"criticism\": \"None at this stage, the plan seems efficient and effective.\",\n", "        \"speak\": \"I am going to save the number of gold medals China won in the 2023 Chengdu Universiade to a file.\"\n", "    },\n", "    \"command\": {\n", "        \"name\": \"write_file\",\n", "        \"args\": {\n", "            \"file_path\": \"2023_Chengdu_Universiade_China_Gold_Medal_Count.txt\",\n", "            \"text\": \"China won 103 gold medals in the 2023 Chengdu Universiade.\",\n", "            \"append\": false\n", "        }\n", "    }\n", "}\n", "System: Command write_file returned: File written successfully to 2023_Chengdu_Universiade_China_Gold_Medal_Count.txt.\n", "Human: Determine which next command to use, and respond using the format specified above:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "{\n", "    \"thoughts\": {\n", "        \"text\": \"I have successfully written the number of gold medals China won in the 2023 Chengdu Universiade to a file. Now, I need to signal that I have finished all my objectives.\",\n", "        \"reasoning\": \"The 'finish' command is the most appropriate in this situation because it allows me to signal that I have completed all my tasks.\",\n", "        \"plan\": \"- Use the 'finish' command to signal that I have completed all my tasks.\",\n", "        \"criticism\": \"None at this stage, the plan seems efficient and effective.\",\n", "        \"speak\": \"I have completed all my tasks.\"\n", "    },\n", "    \"command\": {\n", "        \"name\": \"finish\",\n", "        \"args\": {\n", "            \"response\": \"I have found and saved the number of gold medals China won in the 2023 Chengdu Universiade.\"\n", "        }\n", "    }\n", "}\n"]}, {"data": {"text/plain": ["'I have found and saved the number of gold medals China won in the 2023 Chengdu Universiade.'"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["agent.run([\"2023年成都大运会，中国金牌数是多少\"])"]}, {"cell_type": "code", "execution_count": null, "id": "50dbf7ae-e7b7-494d-afc0-0607a8207188", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a7bcd78b-613c-4590-aa1a-7823be14c3d4", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}