{"cells": [{"cell_type": "markdown", "id": "5abe2121-5381-46d7-a849-66f921883972", "metadata": {}, "source": ["# <PERSON><PERSON><PERSON>n 核心模块：Data Conneciton - Document Transformers\n", "\n", "一旦加载了文档，通常会希望对其进行转换以更好地适应您的应用程序。\n", "\n", "最简单的例子是，您可能希望将长文档拆分为较小的块，以适应模型的上下文窗口。LangChain具有许多内置的文档转换器，可以轻松地拆分、合并、过滤和其他操作文档。\n", "\n", "\n", "![](https://python.langchain.com/assets/images/data_connection-95ff2033a8faa5f3ba41376c0f6dd32a.jpg)"]}, {"cell_type": "markdown", "id": "df8d7408-b14f-4cfb-84c0-9c0bae958cce", "metadata": {}, "source": ["### Document 类\n", "\n", "这段代码定义了一个名为`Document`的类，允许用户与文档的内容进行交互，可以查看文档的段落、摘要，以及使用查找功能来查询文档中的特定字符串。\n", "\n", "```python\n", "# 基于BaseModel定义的文档类。\n", "class Document(BaseModel):\n", "    \"\"\"接口，用于与文档进行交互。\"\"\"\n", "\n", "    # 文档的主要内容。\n", "    page_content: str\n", "    # 用于查找的字符串。\n", "    lookup_str: str = \"\"\n", "    # 查找的索引，初次默认为0。\n", "    lookup_index = 0\n", "    # 用于存储任何与文档相关的元数据。\n", "    metadata: dict = Field(default_factory=dict)\n", "\n", "    @property\n", "    def paragraphs(self) -> List[str]:\n", "        \"\"\"页面的段落列表。\"\"\"\n", "        # 使用\"\\n\\n\"将内容分割为多个段落。\n", "        return self.page_content.split(\"\\n\\n\")\n", "\n", "    @property\n", "    def summary(self) -> str:\n", "        \"\"\"页面的摘要（即第一段）。\"\"\"\n", "        # 返回第一个段落作为摘要。\n", "        return self.paragraphs[0]\n", "\n", "    # 这个方法模仿命令行中的查找功能。\n", "    def lookup(self, string: str) -> str:\n", "        \"\"\"在页面中查找一个词，模仿cmd-F功能。\"\"\"\n", "        # 如果输入的字符串与当前的查找字符串不同，则重置查找字符串和索引。\n", "        if string.lower() != self.lookup_str:\n", "            self.lookup_str = string.lower()\n", "            self.lookup_index = 0\n", "        else:\n", "            # 如果输入的字符串与当前的查找字符串相同，则查找索引加1。\n", "            self.lookup_index += 1\n", "        # 找出所有包含查找字符串的段落。\n", "        lookups = [p for p in self.paragraphs if self.lookup_str in p.lower()]\n", "        # 根据查找结果返回相应的信息。\n", "        if len(lookups) == 0:\n", "            return \"No Results\"\n", "        elif self.lookup_index >= len(lookups):\n", "            return \"No More Results\"\n", "        else:\n", "            result_prefix = f\"(Result {self.lookup_index + 1}/{len(lookups)})\"\n", "            return f\"{result_prefix} {lookups[self.lookup_index]}\"\n", "```\n"]}, {"cell_type": "markdown", "id": "b68fdbcb-b60d-441f-91fc-d8cac24ba3e1", "metadata": {}, "source": ["## Text Splitters 文本分割器\n", "\n", "当你想处理长篇文本时，有必要将文本分成块。听起来很简单，但这里存在着潜在的复杂性。理想情况下，你希望将语义相关的文本片段放在一起。\"语义相关\"的含义可能取决于文本类型。这个笔记本展示了几种实现方式。\n", "\n", "从高层次上看，文本分割器的工作原理如下：\n", "\n", "1. 将文本分成小而有意义的块（通常是句子）。\n", "2. 开始将这些小块组合成较大的块，直到达到某个大小（通过某个函数进行测量）。\n", "3. 一旦达到该大小，使该块成为自己独立的一部分，并开始创建一个具有一定重叠（以保持上下文关系）的新文本块。\n", "\n", "这意味着您可以沿两个不同轴向定制您的文本分割器：\n", "\n", "1. 如何拆分文字\n", "2. 如何测量块大小\n", "\n", "### 使用 `RecursiveCharacterTextSplitter` 文本分割器\n", "\n", "该文本分割器接受一个字符列表作为参数，根据第一个字符进行切块，但如果任何切块太大，则会继续移动到下一个字符，并以此类推。默认情况下，它尝试进行切割的字符包括 `[\"\\n\\n\", \"\\n\", \" \", \"\"]`\n", "\n", "除了控制可以进行切割的字符外，您还可以控制其他一些内容：\n", "\n", "- length_function：用于计算切块长度的方法。默认只计算字符数，但通常在这里传递一个令牌计数器。\n", "- chunk_size：您的切块的最大大小（由长度函数测量）。\n", "- chunk_overlap：切块之间的最大重叠部分。保持一定程度的重叠可以使得各个切块之间保持连贯性（例如滑动窗口）。\n", "- add_start_index：是否在元数据中包含每个切块在原始文档中的起始位置。\n", "\n", "#### 分割文本"]}, {"cell_type": "code", "execution_count": 1, "id": "8dadd89b-6a13-4391-9102-acde028b61d5", "metadata": {}, "outputs": [], "source": ["# 加载待分割长文本\n", "with open('../tests/state_of_the_union.txt', encoding='utf-8') as f:\n", "    state_of_the_union = f.read()"]}, {"cell_type": "code", "execution_count": 2, "id": "9cd4c3b2-3249-4e24-ab18-14c9d735083b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Madam Speaker, <PERSON>am Vice President, our First Lady and Second Gentleman. Members of Congress and the Cabinet. Justices of the Supreme Court. My fellow Americans.  \n", "\n", "Last year COVID-19 kept us apart. This year we are finally together again. \n", "\n", "Tonight, we meet as Democrats Republicans and Independents. But most importantly as Americans. \n", "\n", "With a duty to one another to the American people to the Constitution. \n", "\n", "And with an unwavering resolve that freedom will always triumph over tyranny. \n", "\n", "Six days ago, Russia’s <PERSON> sought to shake the foundations of the free world thinking he could make it bend to his menacing ways. But he badly miscalculated. \n", "\n", "He thought he could roll into Ukraine and the world would roll over. Instead he met a wall of strength he never imagined. \n", "\n", "He met the Ukrainian people. \n", "\n", "From President <PERSON><PERSON><PERSON><PERSON> to every Ukrainian, their fearlessness, their courage, their determination, inspires the world. \n", "\n", "Groups of citizens blocking tanks with their bodies. Everyone from students to retirees teachers turned soldiers defending their homeland. \n", "\n", "In this struggle as President <PERSON><PERSON><PERSON><PERSON> said in his speech to the European Parliament “Light will win over darkness.” The Ukrainian Ambassador to the United States is here tonight. \n", "\n", "Let each of us here tonight in this Chamber send an unmistakable signal to Ukraine and to the world. \n", "\n", "Please rise if you are able and show that, Yes, we the United States of America stand with the Ukrainian people. \n", "\n", "Throughout our history we’ve learned this lesson when dictators do not pay a price for their aggression they cause more chaos.   \n", "\n", "They keep moving.   \n", "\n", "And the costs and the threats to America and the world keep rising.   \n", "\n", "That’s why the NATO Alliance was created to secure peace and stability in Europe after World War 2. \n", "\n", "The United States is a member along with 29 other nations. \n", "\n", "It matters. American diplomacy matters. American resolve matters. \n", "\n", "<PERSON>’s latest attack on Ukraine was premeditated and unprovoked. \n", "\n", "He rejected repeated efforts at diplomacy. \n", "\n", "He thought the West and NATO wouldn’t respond. And he thought he could divide us at home. Putin was wrong. We were ready.  Here is what we did.   \n", "\n", "We prepared extensively and carefully. \n", "\n", "We spent months building a coalition of other freedom-loving nations from Europe and the Americas to Asia and Africa to confront <PERSON>. \n", "\n", "I spent countless hours unifying our European allies. We shared with the world in advance what we knew <PERSON> was planning and precisely how he would try to falsely justify his aggression.  \n", "\n", "We countered Russia’s lies with truth.   \n", "\n", "And now that he has acted the free world is holding him accountable. \n", "\n", "Along with twenty-seven members of the European Union including France, Germany, Italy, as well as countries like the United Kingdom, Canada, Japan, Korea, Australia, New Zealand, and many others, even Switzerland. \n", "\n", "We are inflicting pain on Russia and supporting the people of Ukraine. <PERSON> is now isolated from the world more than ever. \n", "\n", "Together with our allies –we are right now enforcing powerful economic sanctions. \n", "\n", "We are cutting off Russia’s largest banks from the international financial system.  \n", "\n", "Preventing Russia’s central bank from defending the Russian Ruble making <PERSON>’s $630 Billion “war fund” worthless.   \n", "\n", "We are choking off Russia’s access to technology that will sap its economic strength and weaken its military for years to come.  \n", "\n", "Tonight I say to the Russian oligarchs and corrupt leaders who have bilked billions of dollars off this violent regime no more. \n", "\n", "The U.S. Department of Justice is assembling a dedicated task force to go after the crimes of Russian oligarchs.  \n", "\n", "We are joining with our European allies to find and seize your yachts your luxury apartments your private jets. We are coming for your ill-begotten gains. \n", "\n", "And tonight I am announcing that we will join our allies in closing off American air space to all Russian flights – further isolating Russia – and adding an additional squeeze –on their economy. The Ruble has lost 30% of its value. \n", "\n", "The Russian stock market has lost 40% of its value and trading remains suspended. Russia’s economy is reeling and Putin alone is to blame. \n", "\n", "Together with our allies we are providing support to the Ukrainians in their fight for freedom. Military assistance. Economic assistance. Humanitarian assistance. \n", "\n", "We are giving more than $1 Billion in direct assistance to Ukraine. \n", "\n", "And we will continue to aid the Ukrainian people as they defend their country and to help ease their suffering.  \n", "\n", "Let me be clear, our forces are not engaged and will not engage in conflict with Russian forces in Ukraine.  \n", "\n", "Our forces are not going to Europe to fight in Ukraine, but to defend our NATO Allies – in the event that <PERSON> decides to keep moving west.  \n", "\n", "For that purpose we’ve mobilized American ground forces, air squadrons, and ship deployments to protect NATO countries including Poland, Romania, Latvia, Lithuania, and Estonia. \n", "\n", "As I have made crystal clear the United States and our Allies will defend every inch of territory of NATO countries with the full force of our collective power.  \n", "\n", "And we remain clear-eyed. The Ukrainians are fighting back with pure courage. But the next few days weeks, months, will be hard on them.  \n", "\n", "<PERSON> has unleashed violence and chaos.  But while he may make gains on the battlefield – he will pay a continuing high price over the long run. \n", "\n", "And a proud Ukrainian people, who have known 30 years  of independence, have repeatedly shown that they will not tolerate anyone who tries to take their country backwards.  \n", "\n", "To all Americans, I will be honest with you, as I’ve always promised. A Russian dictator, invading a foreign country, has costs around the world. \n", "\n", "And I’m taking robust action to make sure the pain of our sanctions  is targeted at Russia’s economy. And I will use every tool at our disposal to protect American businesses and consumers. \n", "\n", "Tonight, I can announce that the United States has worked with 30 other countries to release 60 Million barrels of oil from reserves around the world.  \n", "\n", "America will lead that effort, releasing 30 Million barrels from our own Strategic Petroleum Reserve. And we stand ready to do more if necessary, unified with our allies.  \n", "\n", "These steps will help blunt gas prices here at home. And I know the news about what’s happening can seem alarming. \n", "\n", "But I want you to know that we are going to be okay. \n", "\n", "When the history of this era is written <PERSON>’s war on Ukraine will have left Russia weaker and the rest of the world stronger. \n", "\n", "While it shouldn’t have taken something so terrible for people around the world to see what’s at stake now everyone sees it clearly. \n", "\n", "We see the unity among leaders of nations and a more unified Europe a more unified West. And we see unity among the people who are gathering in cities in large crowds around the world even in Russia to demonstrate their support for Ukraine.  \n", "\n", "In the battle between democracy and autocracy, democracies are rising to the moment, and the world is clearly choosing the side of peace and security. \n", "\n", "This is a real test. It’s going to take time. So let us continue to draw inspiration from the iron will of the Ukrainian people. \n", "\n", "To our fellow Ukrainian Americans who forge a deep bond that connects our two nations we stand with you. \n", "\n", "<PERSON> may circle Kyiv with tanks, but he will never gain the hearts and souls of the Ukrainian people. \n", "\n", "He will never extinguish their love of freedom. He will never weaken the resolve of the free world. \n", "\n", "We meet tonight in an America that has lived through two of the hardest years this nation has ever faced. \n", "\n", "The pandemic has been punishing. \n", "\n", "And so many families are living paycheck to paycheck, struggling to keep up with the rising cost of food, gas, housing, and so much more. \n", "\n", "I understand. \n", "\n", "I remember when my Dad had to leave our home in Scranton, Pennsylvania to find work. I grew up in a family where if the price of food went up, you felt it. \n", "\n", "That’s why one of the first things I did as President was fight to pass the American Rescue Plan.  \n", "\n", "Because people were hurting. We needed to act, and we did. \n", "\n", "Few pieces of legislation have done more in a critical moment in our history to lift us out of crisis. \n", "\n", "It fueled our efforts to vaccinate the nation and combat COVID-19. It delivered immediate economic relief for tens of millions of Americans.  \n", "\n", "Helped put food on their table, keep a roof over their heads, and cut the cost of health insurance. \n", "\n", "And as my Dad used to say, it gave people a little breathing room. \n", "\n", "And unlike the $2 Trillion tax cut passed in the previous administration that benefitted the top 1% of Americans, the American Rescue Plan helped working people—and left no one behind. \n", "\n", "And it worked. It created jobs. Lots of jobs. \n", "\n", "In fact—our economy created over 6.5 Million new jobs just last year, more jobs created in one year  \n", "than ever before in the history of America. \n", "\n", "Our economy grew at a rate of 5.7% last year, the strongest growth in nearly 40 years, the first step in bringing fundamental change to an economy that hasn’t worked for the working people of this nation for too long.  \n", "\n", "For the past 40 years we were told that if we gave tax breaks to those at the very top, the benefits would trickle down to everyone else. \n", "\n", "But that trickle-down theory led to weaker economic growth, lower wages, bigger deficits, and the widest gap between those at the top and everyone else in nearly a century. \n", "\n", "Vice President <PERSON> and <PERSON> ran for office with a new economic vision for America. \n", "\n", "Invest in America. Educate Americans. Grow the workforce. Build the economy from the bottom up  \n", "and the middle out, not from the top down.  \n", "\n", "Because we know that when the middle class grows, the poor have a ladder up and the wealthy do very well. \n", "\n", "America used to have the best roads, bridges, and airports on Earth. \n", "\n", "Now our infrastructure is ranked 13th in the world. \n", "\n", "We won’t be able to compete for the jobs of the 21st Century if we don’t fix that. \n", "\n", "That’s why it was so important to pass the Bipartisan Infrastructure Law—the most sweeping investment to rebuild America in history. \n", "\n", "This was a bipartisan effort, and I want to thank the members of both parties who worked to make it happen. \n", "\n", "We’re done talking about infrastructure weeks. \n", "\n", "We’re going to have an infrastructure decade. \n", "\n", "It is going to transform America and put us on a path to win the economic competition of the 21st Century that we face with the rest of the world—particularly with China.  \n", "\n", "As I’ve told <PERSON>, it is never a good bet to bet against the American people. \n", "\n", "We’ll create good jobs for millions of Americans, modernizing roads, airports, ports, and waterways all across America. \n", "\n", "And we’ll do it all to withstand the devastating effects of the climate crisis and promote environmental justice. \n", "\n", "We’ll build a national network of 500,000 electric vehicle charging stations, begin to replace poisonous lead pipes—so every child—and every American—has clean water to drink at home and at school, provide affordable high-speed internet for every American—urban, suburban, rural, and tribal communities. \n", "\n", "4,000 projects have already been announced. \n", "\n", "And tonight, I’m announcing that this year we will start fixing over 65,000 miles of highway and 1,500 bridges in disrepair. \n", "\n", "When we use taxpayer dollars to rebuild America – we are going to Buy American: buy American products to support American jobs. \n", "\n", "The federal government spends about $600 Billion a year to keep the country safe and secure. \n", "\n", "There’s been a law on the books for almost a century \n", "to make sure taxpayers’ dollars support American jobs and businesses. \n", "\n", "Every Administration says they’ll do it, but we are actually doing it. \n", "\n", "We will buy American to make sure everything from the deck of an aircraft carrier to the steel on highway guardrails are made in America. \n", "\n", "But to compete for the best jobs of the future, we also need to level the playing field with China and other competitors. \n", "\n", "That’s why it is so important to pass the Bipartisan Innovation Act sitting in Congress that will make record investments in emerging technologies and American manufacturing. \n", "\n", "Let me give you one example of why it’s so important to pass it. \n", "\n", "If you travel 20 miles east of Columbus, Ohio, you’ll find 1,000 empty acres of land. \n", "\n", "It won’t look like much, but if you stop and look closely, you’ll see a “Field of dreams,” the ground on which America’s future will be built. \n", "\n", "This is where Intel, the American company that helped build Silicon Valley, is going to build its $20 billion semiconductor “mega site”. \n", "\n", "Up to eight state-of-the-art factories in one place. 10,000 new good-paying jobs. \n", "\n", "Some of the most sophisticated manufacturing in the world to make computer chips the size of a fingertip that power the world and our everyday lives. \n", "\n", "Smartphones. The Internet. Technology we have yet to invent. \n", "\n", "But that’s just the beginning. \n", "\n", "Intel’s CEO, <PERSON>, who is here tonight, told me they are ready to increase their investment from  \n", "$20 billion to $100 billion. \n", "\n", "That would be one of the biggest investments in manufacturing in American history. \n", "\n", "And all they’re waiting for is for you to pass this bill. \n", "\n", "So let’s not wait any longer. Send it to my desk. I’ll sign it.  \n", "\n", "And we will really take off. \n", "\n", "And Intel is not alone. \n", "\n", "There’s something happening in America. \n", "\n", "Just look around and you’ll see an amazing story. \n", "\n", "The rebirth of the pride that comes from stamping products “Made In America.” The revitalization of American manufacturing.   \n", "\n", "Companies are choosing to build new factories here, when just a few years ago, they would have built them overseas. \n", "\n", "That’s what is happening. Ford is investing $11 billion to build electric vehicles, creating 11,000 jobs across the country. \n", "\n", "GM is making the largest investment in its history—$7 billion to build electric vehicles, creating 4,000 jobs in Michigan. \n", "\n", "All told, we created 369,000 new manufacturing jobs in America just last year. \n", "\n", "Powered by people I’ve met like <PERSON><PERSON><PERSON>, from generations of union steelworkers from Pittsburgh, who’s here with us tonight. \n", "\n", "As Ohio Senator <PERSON><PERSON><PERSON> says, “It’s time to bury the label “Rust Belt.” \n", "\n", "It’s time. \n", "\n", "But with all the bright spots in our economy, record job growth and higher wages, too many families are struggling to keep up with the bills.  \n", "\n", "Inflation is robbing them of the gains they might otherwise feel. \n", "\n", "I get it. That’s why my top priority is getting prices under control. \n", "\n", "Look, our economy roared back faster than most predicted, but the pandemic meant that businesses had a hard time hiring enough workers to keep up production in their factories. \n", "\n", "The pandemic also disrupted global supply chains. \n", "\n", "When factories close, it takes longer to make goods and get them from the warehouse to the store, and prices go up. \n", "\n", "Look at cars. \n", "\n", "Last year, there weren’t enough semiconductors to make all the cars that people wanted to buy. \n", "\n", "And guess what, prices of automobiles went up. \n", "\n", "So—we have a choice. \n", "\n", "One way to fight inflation is to drive down wages and make Americans poorer.  \n", "\n", "I have a better plan to fight inflation. \n", "\n", "Lower your costs, not your wages. \n", "\n", "Make more cars and semiconductors in America. \n", "\n", "More infrastructure and innovation in America. \n", "\n", "More goods moving faster and cheaper in America. \n", "\n", "More jobs where you can earn a good living in America. \n", "\n", "And instead of relying on foreign supply chains, let’s make it in America. \n", "\n", "Economists call it “increasing the productive capacity of our economy.” \n", "\n", "I call it building a better America. \n", "\n", "My plan to fight inflation will lower your costs and lower the deficit. \n", "\n", "17 Nobel laureates in economics say my plan will ease long-term inflationary pressures. Top business leaders and most Americans support my plan. And here’s the plan: \n", "\n", "First – cut the cost of prescription drugs. Just look at insulin. One in ten Americans has diabetes. In Virginia, I met a 13-year-old boy named <PERSON>.  \n", "\n", "He and his Dad both have Type 1 diabetes, which means they need insulin every day. Insulin costs about $10 a vial to make.  \n", "\n", "But drug companies charge families like <PERSON> and his Dad up to 30 times more. I spoke with <PERSON>’s mom. \n", "\n", "Imagine what it’s like to look at your child who needs insulin and have no idea how you’re going to pay for it.  \n", "\n", "What it does to your dignity, your ability to look your child in the eye, to be the parent you expect to be. \n", "\n", "<PERSON> is here with us tonight. Yesterday was his birthday. Happy birthday, buddy.  \n", "\n", "For <PERSON>, and for the 200,000 other young people with Type 1 diabetes, let’s cap the cost of insulin at $35 a month so everyone can afford it.  \n", "\n", "Drug companies will still do very well. And while we’re at it let Medicare negotiate lower prices for prescription drugs, like the VA already does. \n", "\n", "Look, the American Rescue Plan is helping millions of families on Affordable Care Act plans save $2,400 a year on their health care premiums. Let’s close the coverage gap and make those savings permanent. \n", "\n", "Second – cut energy costs for families an average of $500 a year by combatting climate change.  \n", "\n", "Let’s provide investments and tax credits to weatherize your homes and businesses to be energy efficient and you get a tax credit; double America’s clean energy production in solar, wind, and so much more;  lower the price of electric vehicles, saving you another $80 a month because you’ll never have to pay at the gas pump again. \n", "\n", "Third – cut the cost of child care. Many families pay up to $14,000 a year for child care per child.  \n", "\n", "Middle-class and working families shouldn’t have to pay more than 7% of their income for care of young children.  \n", "\n", "My plan will cut the cost in half for most families and help parents, including millions of women, who left the workforce during the pandemic because they couldn’t afford child care, to be able to get back to work. \n", "\n", "My plan doesn’t stop there. It also includes home and long-term care. More affordable housing. And Pre-K for every 3- and 4-year-old.  \n", "\n", "All of these will lower costs. \n", "\n", "And under my plan, nobody earning less than $400,000 a year will pay an additional penny in new taxes. Nobody.  \n", "\n", "The one thing all Americans agree on is that the tax system is not fair. We have to fix it.  \n", "\n", "I’m not looking to punish anyone. But let’s make sure corporations and the wealthiest Americans start paying their fair share. \n", "\n", "Just last year, 55 Fortune 500 corporations earned $40 billion in profits and paid zero dollars in federal income tax.  \n", "\n", "That’s simply not fair. That’s why I’ve proposed a 15% minimum tax rate for corporations. \n", "\n", "We got more than 130 countries to agree on a global minimum tax rate so companies can’t get out of paying their taxes at home by shipping jobs and factories overseas. \n", "\n", "That’s why I’ve proposed closing loopholes so the very wealthy don’t pay a lower tax rate than a teacher or a firefighter.  \n", "\n", "So that’s my plan. It will grow the economy and lower costs for families. \n", "\n", "So what are we waiting for? Let’s get this done. And while you’re at it, confirm my nominees to the Federal Reserve, which plays a critical role in fighting inflation.  \n", "\n", "My plan will not only lower costs to give families a fair shot, it will lower the deficit. \n", "\n", "The previous Administration not only ballooned the deficit with tax cuts for the very wealthy and corporations, it undermined the watchdogs whose job was to keep pandemic relief funds from being wasted. \n", "\n", "But in my administration, the watchdogs have been welcomed back. \n", "\n", "We’re going after the criminals who stole billions in relief money meant for small businesses and millions of Americans.  \n", "\n", "And tonight, I’m announcing that the Justice Department will name a chief prosecutor for pandemic fraud. \n", "\n", "By the end of this year, the deficit will be down to less than half what it was before I took office.  \n", "\n", "The only president ever to cut the deficit by more than one trillion dollars in a single year. \n", "\n", "Lowering your costs also means demanding more competition. \n", "\n", "I’m a capitalist, but capitalism without competition isn’t capitalism. \n", "\n", "It’s exploitation—and it drives up prices. \n", "\n", "When corporations don’t have to compete, their profits go up, your prices go up, and small businesses and family farmers and ranchers go under. \n", "\n", "We see it happening with ocean carriers moving goods in and out of America. \n", "\n", "During the pandemic, these foreign-owned companies raised prices by as much as 1,000% and made record profits. \n", "\n", "Tonight, I’m announcing a crackdown on these companies overcharging American businesses and consumers. \n", "\n", "And as Wall Street firms take over more nursing homes, quality in those homes has gone down and costs have gone up.  \n", "\n", "That ends on my watch. \n", "\n", "Medicare is going to set higher standards for nursing homes and make sure your loved ones get the care they deserve and expect. \n", "\n", "We’ll also cut costs and keep the economy going strong by giving workers a fair shot, provide more training and apprenticeships, hire them based on their skills not degrees. \n", "\n", "Let’s pass the Paycheck Fairness Act and paid leave.  \n", "\n", "Raise the minimum wage to $15 an hour and extend the Child Tax Credit, so no one has to raise a family in poverty. \n", "\n", "Let’s increase Pell Grants and increase our historic support of HBCUs, and invest in what <PERSON>—our First Lady who teaches full-time—calls America’s best-kept secret: community colleges. \n", "\n", "And let’s pass the PRO Act when a majority of workers want to form a union—they shouldn’t be stopped.  \n", "\n", "When we invest in our workers, when we build the economy from the bottom up and the middle out together, we can do something we haven’t done in a long time: build a better America. \n", "\n", "For more than two years, COVID-19 has impacted every decision in our lives and the life of the nation. \n", "\n", "And I know you’re tired, frustrated, and exhausted. \n", "\n", "But I also know this. \n", "\n", "Because of the progress we’ve made, because of your resilience and the tools we have, tonight I can say  \n", "we are moving forward safely, back to more normal routines.  \n", "\n", "We’ve reached a new moment in the fight against COVID-19, with severe cases down to a level not seen since last July.  \n", "\n", "Just a few days ago, the Centers for Disease Control and Prevention—the CDC—issued new mask guidelines. \n", "\n", "Under these new guidelines, most Americans in most of the country can now be mask free.   \n", "\n", "And based on the projections, more of the country will reach that point across the next couple of weeks. \n", "\n", "Thanks to the progress we have made this past year, COVID-19 need no longer control our lives.  \n", "\n", "I know some are talking about “living with COVID-19”. Tonight – I say that we will never just accept living with COVID-19. \n", "\n", "We will continue to combat the virus as we do other diseases. And because this is a virus that mutates and spreads, we will stay on guard. \n", "\n", "Here are four common sense steps as we move forward safely.  \n", "\n", "First, stay protected with vaccines and treatments. We know how incredibly effective vaccines are. If you’re vaccinated and boosted you have the highest degree of protection. \n", "\n", "We will never give up on vaccinating more Americans. Now, I know parents with kids under 5 are eager to see a vaccine authorized for their children. \n", "\n", "The scientists are working hard to get that done and we’ll be ready with plenty of vaccines when they do. \n", "\n", "We’re also ready with anti-viral treatments. If you get COVID-19, the Pfizer pill reduces your chances of ending up in the hospital by 90%.  \n", "\n", "We’ve ordered more of these pills than anyone in the world. And <PERSON><PERSON><PERSON> is working overtime to get us 1 Million pills this month and more than double that next month.  \n", "\n", "And we’re launching the “Test to Treat” initiative so people can get tested at a pharmacy, and if they’re positive, receive antiviral pills on the spot at no cost.  \n", "\n", "If you’re immunocompromised or have some other vulnerability, we have treatments and free high-quality masks. \n", "\n", "We’re leaving no one behind or ignoring anyone’s needs as we move forward. \n", "\n", "And on testing, we have made hundreds of millions of tests available for you to order for free.   \n", "\n", "Even if you already ordered free tests tonight, I am announcing that you can order more from covidtests.gov starting next week. \n", "\n", "Second – we must prepare for new variants. Over the past year, we’ve gotten much better at detecting new variants. \n", "\n", "If necessary, we’ll be able to deploy new vaccines within 100 days instead of many more months or years.  \n", "\n", "And, if Congress provides the funds we need, we’ll have new stockpiles of tests, masks, and pills ready if needed. \n", "\n", "I cannot promise a new variant won’t come. But I can promise you we’ll do everything within our power to be ready if it does.  \n", "\n", "Third – we can end the shutdown of schools and businesses. We have the tools we need. \n", "\n", "It’s time for Americans to get back to work and fill our great downtowns again.  People working from home can feel safe to begin to return to the office.   \n", "\n", "We’re doing that here in the federal government. The vast majority of federal workers will once again work in person. \n", "\n", "Our schools are open. Let’s keep it that way. Our kids need to be in school. \n", "\n", "And with 75% of adult Americans fully vaccinated and hospitalizations down by 77%, most Americans can remove their masks, return to work, stay in the classroom, and move forward safely. \n", "\n", "We achieved this because we provided free vaccines, treatments, tests, and masks. \n", "\n", "Of course, continuing this costs money. \n", "\n", "I will soon send Congress a request. \n", "\n", "The vast majority of Americans have used these tools and may want to again, so I expect Congress to pass it quickly.   \n", "\n", "Fourth, we will continue vaccinating the world.     \n", "\n", "We’ve sent 475 Million vaccine doses to 112 countries, more than any other nation. \n", "\n", "And we won’t stop. \n", "\n", "We have lost so much to COVID-19. Time with one another. And worst of all, so much loss of life. \n", "\n", "Let’s use this moment to reset. Let’s stop looking at COVID-19 as a partisan dividing line and see it for what it is: A God-awful disease.  \n", "\n", "Let’s stop seeing each other as enemies, and start seeing each other for who we really are: Fellow Americans.  \n", "\n", "We can’t change how divided we’ve been. But we can change how we move forward—on COVID-19 and other issues we must face together. \n", "\n", "I recently visited the New York City Police Department days after the funerals of Officer <PERSON><PERSON><PERSON> and his partner, Officer <PERSON>. \n", "\n", "They were responding to a 9-1-1 call when a man shot and killed them with a stolen gun. \n", "\n", "Officer <PERSON><PERSON> was 27 years old. \n", "\n", "Officer <PERSON> was 22. \n", "\n", "Both Dominican Americans who’d grown up on the same streets they later chose to patrol as police officers. \n", "\n", "I spoke with their families and told them that we are forever in debt for their sacrifice, and we will carry on their mission to restore the trust and safety every community deserves. \n", "\n", "I’ve worked on these issues a long time. \n", "\n", "I know what works: Investing in crime preventionand community police officers who’ll walk the beat, who’ll know the neighborhood, and who can restore trust and safety. \n", "\n", "So let’s not abandon our streets. Or choose between safety and equal justice. \n", "\n", "Let’s come together to protect our communities, restore trust, and hold law enforcement accountable. \n", "\n", "That’s why the Justice Department required body cameras, banned chokeholds, and restricted no-knock warrants for its officers. \n", "\n", "That’s why the American Rescue Plan provided $350 Billion that cities, states, and counties can use to hire more police and invest in proven strategies like community violence interruption—trusted messengers breaking the cycle of violence and trauma and giving young people hope.  \n", "\n", "We should all agree: The answer is not to Defund the police. The answer is to FUND the police with the resources and training they need to protect our communities. \n", "\n", "I ask Democrats and Republicans alike: Pass my budget and keep our neighborhoods safe.  \n", "\n", "And I will keep doing everything in my power to crack down on gun trafficking and ghost guns you can buy online and make at home—they have no serial numbers and can’t be traced. \n", "\n", "And I ask Congress to pass proven measures to reduce gun violence. Pass universal background checks. Why should anyone on a terrorist list be able to purchase a weapon? \n", "\n", "Ban assault weapons and high-capacity magazines. \n", "\n", "Repeal the liability shield that makes gun manufacturers the only industry in America that can’t be sued. \n", "\n", "These laws don’t infringe on the Second Amendment. They save lives. \n", "\n", "The most fundamental right in America is the right to vote – and to have it counted. And it’s under assault. \n", "\n", "In state after state, new laws have been passed, not only to suppress the vote, but to subvert entire elections. \n", "\n", "We cannot let this happen. \n", "\n", "Tonight. I call on the Senate to: Pass the Freedom to Vote Act. Pass the John Lewis Voting Rights Act. And while you’re at it, pass the Disclose Act so Americans can know who is funding our elections. \n", "\n", "Tonight, I’d like to honor someone who has dedicated his life to serve this country: Justice <PERSON>—an Army veteran, Constitutional scholar, and retiring Justice of the United States Supreme Court. Justice <PERSON>, thank you for your service. \n", "\n", "One of the most serious constitutional responsibilities a President has is nominating someone to serve on the United States Supreme Court. \n", "\n", "And I did that 4 days ago, when I nominated Circuit Court of Appeals Judge <PERSON><PERSON><PERSON>. One of our nation’s top legal minds, who will continue <PERSON>’s legacy of excellence. \n", "\n", "A former top litigator in private practice. A former federal public defender. And from a family of public school educators and police officers. A consensus builder. Since she’s been nominated, she’s received a broad range of support—from the Fraternal Order of Police to former judges appointed by Democrats and Republicans. \n", "\n", "And if we are to advance liberty and justice, we need to secure the Border and fix the immigration system. \n", "\n", "We can do both. At our border, we’ve installed new technology like cutting-edge scanners to better detect drug smuggling.  \n", "\n", "We’ve set up joint patrols with Mexico and Guatemala to catch more human traffickers.  \n", "\n", "We’re putting in place dedicated immigration judges so families fleeing persecution and violence can have their cases heard faster. \n", "\n", "We’re securing commitments and supporting partners in South and Central America to host more refugees and secure their own borders. \n", "\n", "We can do all this while keeping lit the torch of liberty that has led generations of immigrants to this land—my forefathers and so many of yours. \n", "\n", "Provide a pathway to citizenship for Dream<PERSON>, those on temporary status, farm workers, and essential workers. \n", "\n", "Revise our laws so businesses have the workers they need and families don’t wait decades to reunite. \n", "\n", "It’s not only the right thing to do—it’s the economically smart thing to do. \n", "\n", "That’s why immigration reform is supported by everyone from labor unions to religious leaders to the U.S. Chamber of Commerce. \n", "\n", "Let’s get it done once and for all. \n", "\n", "Advancing liberty and justice also requires protecting the rights of women. \n", "\n", "The constitutional right affirmed in <PERSON> v<PERSON>—standing precedent for half a century—is under attack as never before. \n", "\n", "If we want to go forward—not backward—we must protect access to health care. Preserve a woman’s right to choose. And let’s continue to advance maternal health care in America. \n", "\n", "And for our LGBTQ+ Americans, let’s finally get the bipartisan Equality Act to my desk. The onslaught of state laws targeting transgender Americans and their families is wrong. \n", "\n", "As I said last year, especially to our younger transgender Americans, I will always have your back as your President, so you can be yourself and reach your God-given potential. \n", "\n", "While it often appears that we never agree, that isn’t true. I signed 80 bipartisan bills into law last year. From preventing government shutdowns to protecting Asian-Americans from still-too-common hate crimes to reforming military justice. \n", "\n", "And soon, we’ll strengthen the Violence Against Women Act that I first wrote three decades ago. It is important for us to show the nation that we can come together and do big things. \n", "\n", "So tonight I’m offering a Unity Agenda for the Nation. Four big things we can do together.  \n", "\n", "First, beat the opioid epidemic. \n", "\n", "There is so much we can do. Increase funding for prevention, treatment, harm reduction, and recovery.  \n", "\n", "Get rid of outdated rules that stop doctors from prescribing treatments. And stop the flow of illicit drugs by working with state and local law enforcement to go after traffickers. \n", "\n", "If you’re suffering from addiction, know you are not alone. I believe in recovery, and I celebrate the 23 million Americans in recovery. \n", "\n", "Second, let’s take on mental health. Especially among our children, whose lives and education have been turned upside down.  \n", "\n", "The American Rescue Plan gave schools money to hire teachers and help students make up for lost learning.  \n", "\n", "I urge every parent to make sure your school does just that. And we can all play a part—sign up to be a tutor or a mentor. \n", "\n", "Children were also struggling before the pandemic. Bullying, violence, trauma, and the harms of social media. \n", "\n", "As <PERSON>, who is here with us tonight, has shown, we must hold social media platforms accountable for the national experiment they’re conducting on our children for profit. \n", "\n", "It’s time to strengthen privacy protections, ban targeted advertising to children, demand tech companies stop collecting personal data on our children. \n", "\n", "And let’s get all Americans the mental health services they need. More people they can turn to for help, and full parity between physical and mental health care. \n", "\n", "Third, support our veterans. \n", "\n", "Veterans are the best of us. \n", "\n", "I’ve always believed that we have a sacred obligation to equip all those we send to war and care for them and their families when they come home. \n", "\n", "My administration is providing assistance with job training and housing, and now helping lower-income veterans get VA care debt-free.  \n", "\n", "Our troops in Iraq and Afghanistan faced many dangers. \n", "\n", "One was stationed at bases and breathing in toxic smoke from “burn pits” that incinerated wastes of war—medical and hazard material, jet fuel, and more. \n", "\n", "When they came home, many of the world’s fittest and best trained warriors were never the same. \n", "\n", "Headaches. Numbness. Dizziness. \n", "\n", "A cancer that would put them in a flag-draped coffin. \n", "\n", "I know. \n", "\n", "One of those soldiers was my son Major <PERSON>. \n", "\n", "We don’t know for sure if a burn pit was the cause of his brain cancer, or the diseases of so many of our troops. \n", "\n", "But I’m committed to finding out everything we can. \n", "\n", "Committed to military families like <PERSON> from Ohio. \n", "\n", "The widow of Sergeant First Class <PERSON>.  \n", "\n", "He was born a soldier. Army National Guard. Combat medic in Kosovo and Iraq. \n", "\n", "Stationed near Baghdad, just yards from burn pits the size of football fields. \n", "\n", "<PERSON>’s widow <PERSON> is here with us tonight. They loved going to Ohio State football games. He loved building Legos with their daughter. \n", "\n", "But cancer from prolonged exposure to burn pits ravaged <PERSON>’s lungs and body. \n", "\n", "<PERSON> says <PERSON> was a fighter to the very end. \n", "\n", "He didn’t know how to stop fighting, and neither did she. \n", "\n", "Through her pain she found purpose to demand we do better. \n", "\n", "Tonight, <PERSON>—we are. \n", "\n", "The VA is pioneering new ways of linking toxic exposures to diseases, already helping more veterans get benefits. \n", "\n", "And tonight, I’m announcing we’re expanding eligibility to veterans suffering from nine respiratory cancers. \n", "\n", "I’m also calling on Congress: pass a law to make sure veterans devastated by toxic exposures in Iraq and Afghanistan finally get the benefits and comprehensive health care they deserve. \n", "\n", "And fourth, let’s end cancer as we know it. \n", "\n", "This is personal to me and <PERSON>, to <PERSON><PERSON>, and to so many of you. \n", "\n", "Cancer is the #2 cause of death in America–second only to heart disease. \n", "\n", "Last month, I announced our plan to supercharge  \n", "the Cancer Moonshot that President <PERSON> asked me to lead six years ago. \n", "\n", "Our goal is to cut the cancer death rate by at least 50% over the next 25 years, turn more cancers from death sentences into treatable diseases.  \n", "\n", "More support for patients and families. \n", "\n", "To get there, I call on Congress to fund ARPA-H, the Advanced Research Projects Agency for Health. \n", "\n", "It’s based on DARPA—the Defense Department project that led to the Internet, GPS, and so much more.  \n", "\n", "ARPA-H will have a singular purpose—to drive breakthroughs in cancer, Alzheimer’s, diabetes, and more. \n", "\n", "A unity agenda for the nation. \n", "\n", "We can do this. \n", "\n", "My fellow Americans—tonight , we have gathered in a sacred space—the citadel of our democracy. \n", "\n", "In this Capitol, generation after generation, Americans have debated great questions amid great strife, and have done great things. \n", "\n", "We have fought for freedom, expanded liberty, defeated totalitarianism and terror. \n", "\n", "And built the strongest, freest, and most prosperous nation the world has ever known. \n", "\n", "Now is the hour. \n", "\n", "Our moment of responsibility. \n", "\n", "Our test of resolve and conscience, of history itself. \n", "\n", "It is in this moment that our character is formed. Our purpose is found. Our future is forged. \n", "\n", "Well I know this nation.  \n", "\n", "We will meet the test. \n", "\n", "To protect freedom and liberty, to expand fairness and opportunity. \n", "\n", "We will save democracy. \n", "\n", "As hard as these times have been, I am more optimistic about America today than I have been my whole life. \n", "\n", "Because I see the future that is within our grasp. \n", "\n", "Because I know there is simply nothing beyond our capacity. \n", "\n", "We are the only nation on Earth that has always turned every crisis we have faced into an opportunity. \n", "\n", "The only nation that can be defined by a single word: possibilities. \n", "\n", "So on this night, in our 245th year as a nation, I have come to report on the State of the Union. \n", "\n", "And my report is this: the State of the Union is strong—because you, the American people, are strong. \n", "\n", "We are stronger today than we were a year ago. \n", "\n", "And we will be stronger a year from now than we are today. \n", "\n", "Now is our moment to meet and overcome the challenges of our time. \n", "\n", "And we will, as one people. \n", "\n", "One America. \n", "\n", "The United States of America. \n", "\n", "May God bless you all. May God protect our troops.\n"]}], "source": ["print(state_of_the_union)"]}, {"cell_type": "code", "execution_count": 3, "id": "24f9c721-dfd3-4632-a89e-92d2fa9b3594", "metadata": {}, "outputs": [], "source": ["from langchain.text_splitter import RecursiveCharacterTextSplitter"]}, {"cell_type": "code", "execution_count": 4, "id": "068debcb-a045-4231-aa8b-7f662f236222", "metadata": {}, "outputs": [], "source": ["text_splitter = RecursiveCharacterTextSplitter(\n", "    chunk_size = 100,\n", "    chunk_overlap  = 20,\n", "    length_function = len,\n", "    add_start_index = True,\n", ")"]}, {"cell_type": "code", "execution_count": 5, "id": "1a394020-88ce-4343-ad88-833aabdbdfab", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["page_content='<PERSON>am Speaker, <PERSON>am Vice President, our First Lady and Second Gentleman. Members of Congress and' metadata={'start_index': 0}\n", "page_content='of Congress and the Cabinet. Justices of the Supreme Court. My fellow Americans.' metadata={'start_index': 82}\n"]}], "source": ["docs = text_splitter.create_documents([state_of_the_union])\n", "print(docs[0])\n", "print(docs[1])"]}, {"cell_type": "code", "execution_count": 6, "id": "203ae505-a06b-4e46-945e-2605c31f5e8e", "metadata": {}, "outputs": [{"data": {"text/plain": ["list"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["type(docs)"]}, {"cell_type": "code", "execution_count": 7, "id": "7762afc5-ee27-40d2-b20e-883c6227e38e", "metadata": {}, "outputs": [{"data": {"text/plain": ["langchain_core.documents.base.Document"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["type(docs[0])"]}, {"cell_type": "code", "execution_count": 8, "id": "53f6d36e-e8e8-4aca-b3dd-7b4dc4c87a91", "metadata": {}, "outputs": [{"data": {"text/plain": ["'<PERSON>am Speaker, <PERSON>am Vice President, our First Lady and Second Gentleman. Members of Congress and the Cabinet. Justices of the Supreme Court. My fellow Americans.  \\n\\nLast year COVID-19 kept us apart. This year we are finally together again. \\n\\nTonight, we meet as Democrats Republicans and Independents. But most importantly as Americans. \\n\\nWith a duty to one another to the American people to the Constitution. \\n\\nAnd with an unwavering resolve that freedom will always triumph over tyranny. \\n\\nSix days ago, Russia’s <PERSON> sought to shake the foundations of the free world thinking he could make it bend to his menacing ways. But he badly miscalculated. \\n\\nHe thought he could roll into Ukraine and the world would roll over. Instead he met a wall of strength he never imagined. \\n\\nHe met the Ukrainian people. \\n\\nFrom President <PERSON><PERSON><PERSON><PERSON> to every Ukrainian, their fearlessness, their courage, their determination, inspires the world. \\n\\nGroups of citizens blocking tanks with their bodies. Every'"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["state_of_the_union[:1000]"]}, {"cell_type": "code", "execution_count": 9, "id": "f9ead93c-3a9d-4e92-8ae1-6b95007438fc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["page_content='<PERSON>am Speaker, <PERSON>am Vice President, our First Lady and Second Gentleman. Members of Congress and' metadata={'document': 1, 'start_index': 0}\n"]}], "source": ["metadatas = [{\"document\": 1}, {\"document\": 2}]\n", "documents = text_splitter.create_documents([state_of_the_union, state_of_the_union], metadatas=metadatas)\n", "print(documents[0])"]}, {"cell_type": "markdown", "id": "d1fbb869-a6ce-40b9-afaf-488d1705e8b0", "metadata": {}, "source": ["#### 分割代码"]}, {"cell_type": "code", "execution_count": 10, "id": "fb9ed458-43d2-449a-b02f-cc2158ef180e", "metadata": {}, "outputs": [], "source": ["from langchain.text_splitter import Language"]}, {"cell_type": "code", "execution_count": 11, "id": "747c97ba-63bf-4135-8e5a-3a0cc6c577e2", "metadata": {}, "outputs": [{"data": {"text/plain": ["['cpp',\n", " 'go',\n", " 'java',\n", " 'kotlin',\n", " 'js',\n", " 'ts',\n", " 'php',\n", " 'proto',\n", " 'python',\n", " 'rst',\n", " 'ruby',\n", " 'rust',\n", " 'scala',\n", " 'swift',\n", " 'markdown',\n", " 'latex',\n", " 'html',\n", " 'sol',\n", " 'csharp',\n", " 'cobol',\n", " 'c',\n", " 'lua',\n", " 'perl',\n", " 'haskell']"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# 支持编程语言的完整列表\n", "[e.value for e in Language]"]}, {"cell_type": "code", "execution_count": 12, "id": "fcc5eb9e-9a3f-477e-8d93-53e785c46157", "metadata": {}, "outputs": [], "source": ["html_text = \"\"\"\n", "<!DOCTYPE html>\n", "<html>\n", "    <head>\n", "        <title>🦜️🔗 <PERSON><PERSON><PERSON><PERSON></title>\n", "        <style>\n", "            body {\n", "                font-family: <PERSON><PERSON>, sans-serif;\n", "            }\n", "            h1 {\n", "                color: darkblue;\n", "            }\n", "        </style>\n", "    </head>\n", "    <body>\n", "        <div>\n", "            <h1>🦜️🔗 Lang<PERSON>hain</h1>\n", "            <p>⚡ Building applications with LLMs through composability ⚡</p>\n", "        </div>\n", "        <div>\n", "            As an open source project in a rapidly developing field, we are extremely open to contributions.\n", "        </div>\n", "    </body>\n", "</html>\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 13, "id": "178b3975-29ac-4635-8a54-efba33c07db1", "metadata": {}, "outputs": [], "source": ["html_splitter = RecursiveCharacterTextSplitter.from_language(\n", "    language=Language.HTML, chunk_size=60, chunk_overlap=0\n", ")\n", "html_docs = html_splitter.create_documents([html_text])"]}, {"cell_type": "code", "execution_count": 14, "id": "0a24d2ac-d87e-4bb5-9307-51977747dc2e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["13\n"]}], "source": ["print(len(html_docs))"]}, {"cell_type": "code", "execution_count": 15, "id": "ba6a33c9-bfe7-4113-b856-87b7b4578b09", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(page_content='<!DOCTYPE html>\\n<html>'),\n", " Document(page_content='<head>\\n        <title>🦜️🔗 Lang<PERSON>hain</title>'),\n", " Document(page_content='<style>\\n            body {\\n                font-family: Aria'),\n", " Document(page_content='l, sans-serif;\\n            }\\n            h1 {'),\n", " Document(page_content='color: darkblue;\\n            }\\n        </style>\\n    </head'),\n", " Document(page_content='>'),\n", " Document(page_content='<body>'),\n", " Document(page_content='<div>\\n            <h1>🦜️🔗 Lang<PERSON>hain</h1>'),\n", " Document(page_content='<p>⚡ Building applications with LLMs through composability ⚡'),\n", " Document(page_content='</p>\\n        </div>'),\n", " Document(page_content='<div>\\n            As an open source project in a rapidly dev'),\n", " Document(page_content='eloping field, we are extremely open to contributions.'),\n", " Document(page_content='</div>\\n    </body>\\n</html>')]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["html_docs"]}, {"cell_type": "code", "execution_count": null, "id": "7a8568e7-c723-4f15-879d-1770b95881e5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}