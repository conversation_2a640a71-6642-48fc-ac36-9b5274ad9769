## 单项选择题：

1. “表示学习”主要是指什么？
   - A. 学习数据的原始表示
   - B. 学习数据的有用表示
   - C. 学习数据的随机表示
   - D. 学习数据的复杂表示

2. 在NLP中，最常用的数据表示方法是什么？
   - A. 图像
   - B. 视频
   - C. 文本
   - D. 音频

3. Word2Vec是一个什么样的模型？
   - A. 用于语音识别的模型
   - B. 用于图像识别的模型
   - C. 用于词表示学习的模型
   - D. 用于视频处理的模型

4. GloVe模型的主要目标是什么？
   - A. 在给定的文本中找到最频繁出现的词
   - B. 在给定的文本中找到最少出现的词
   - C. 在给定的文本中找到相关性最强的词
   - D. 在给定的文本中找到最不相关的词

5. 表示学习在自然语言处理中的主要应用是什么？
   - A. 语音识别
   - B. 语义理解
   - C. 图像识别
   - D. 视频处理

6. Word2Vec和GloVe有什么共同点？
   - A. 都是图像识别模型
   - B. 都是词表示学习模型
   - C. 都是语音识别模型
   - D. 都是视频处理模型

7. 在OpenAI Embeddings中，一般采用什么方法对词进行表示？
   - A. 用一个唯一的ID表示
   - B. 用一个独热向量表示
   - C. 用一个实数向量表示
   - D. 用一个复数向量表示

8. 在词向量中，词的相似度通常用什么衡量？
   - A. 欧氏距离
   - B. 余弦相似度
   - C. 曼哈顿距离
   - D. 切比雪夫距离

9. 在表示学习中，为什么要用非监督学习？
   - A. 因为监督学习太复杂
   - B. 因为监督学习无法处理大数据
   - C. 因为监督学习需要太多的标签数据
   - D. 因为监督学习效果不好

10. 在表示学习中，主要使用哪种方法来优化词向量？
   - A. 用反向传播优化
   - B. 用随机梯度下降优化
   - C. 用动量法优化
   - D. 用牛顿法优化
