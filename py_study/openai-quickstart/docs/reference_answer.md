## 参考答案

### Homework_01

单项选择题答案：

1. C. 选择重要的信息并忽略不相关的信息
2. C. 注意力机制（Attention）
3. C. GPT使用了单向自注意力，而BERT使用了双向自注意力
4. A. 查询、密钥和值
5. C. 通过注意力机制（Attention）
6. C. 生成任务
7. B. 使用了双向自注意力机制
8. B. 识别输入中的关键信息
9. B. 自然语言处理
10. C. 自然语言处理

多项选择题答案：

11. A. 递归神经网络（RNN） B. 卷积神经网络（CNN） C. 注意力机制（Attention）
12. A. BERT B. GPT
13. A. GPT B. BERT
14. B. 它可以用来挑选出重要的信息并忽略不相关的信息 C. 它可以用来生成高质量的词嵌入
15. A. BERT模型是基于Transformer的 B. BERT模型使用了双向自注意力机制

### Homework_02

单项选择题答案：

1. B. 改进预训练方法。GPT-1的主要目标是通过生成式预训练来提升语言理解能力。

2. A. 模型规模。GPT-2在模型规模上进行了提升，以提高其处理复杂任务的能力。

3. C. 可以生成更自然的文本。相比于GPT-1，GPT-2在生成长篇文章时，其文本的自然度和连贯性都有所提高。

4. D. 引入了少样本学习能力。GPT-3的主要创新是引入了在少量样本上表现优秀的能力，这也是GPT-3标题中"Few-Shot Learners"的来源。

5. C. 在少量样本上表现优秀。GPT-3的"少样本学习"是指该模型在处理少量样本时，依然可以展现出很好的性能。

6. C. 一种激励大型语言模型进行推理的提示方法。"思维链"是一种新的提示学习方法，旨在通过特定的方式，激励大型语言模型进行推理。

7. B. 提高模型的推理能力。自洽性在语言模型中的主要作用是提高模型的推理能力，使模型在生成文本时能保持内容的一致性。

8. D. 一种激励大型语言模型解决问题的提示方法。"思维树"是一种新的提示学习方法，可以用于引导大型语言模型解决复杂问题。

9. B. 生成自然且连贯的对话。ChatGPT的主要优势是能生成自然且连贯的对话，这也是它在对话系统中广受欢迎的原因。

10. D. 引入了图像识别能力。GPT-4的主要创新是在语言模型的基础上，引入了图像识别的能力，使其可以处理多模态任务。

多项选择题答案：

1. A, D. GPT-1提出了生成式预训练的概念，并且在微调后，在特定任务上取得了很好的效果。它并没有特别关注模型架构的优化，也没有引入图像识别的能力。

2. A, B. GPT-2增大了模型规模，从而提升了处理复杂任务的能力。同时，其在处理长篇文章的能力也有所提升。但GPT-2并没有引入新的训练方法，也没有尝试实现图像识别。

3. A, D. GPT-3的主要创新是引入了在少量样本上表现优秀的能力。它并没有改进预训练方法，也没有引入新的模型架构。

4. A, B, D. 思维链是一种激励大型语言模型进行推理的提示方法，自洽性是一种提高模型推理能力的技术，而思维树则是一种引导模型解决问题的提示方法。它们都是为了提升模型的推理能力。

5. A, D. GPT模型家族的发展确实可以看到模型规模的不断增大，而GPT-4是GPT家族中首个引入图像识别能力的模型。但GPT模型家族并不总是在优化模型架构，也没有一直在改进预训练方法。

### Homework_03

单项选择题答案：

1. B. 学习数据的有用表示
    - 解释：表示学习的主要目标是找到一种方式来转换原始数据，使得转换后的表示能更好地支持后续的任务。例如，在图像识别任务中，原始像素值可能不是一个好的数据表示，而通过某种方法学习到的特征（例如边缘、颜色、形状等）可能会提供更好的性能。

2. C. 文本
    - 解释：自然语言处理（NLP）中的数据通常是文本。一种有效的处理文本数据的方法是将其转化为数值或者向量形式，这种转化过程就是数据表示。例如，一个最简单的方法是one-hot编码，每个词被编码为一个很长的向量，这个向量的维度是词汇表的大小，向量的所有元素都是0，除了表示该词的索引位置的元素是1。

3. C. 用于词表示学习的模型
    - 解释：Word2Vec是一种用于学习词向量的模型，它通过训练神经网络模型，将语料库中的每个词映射到一个向量，以便使语义上相似的词在向量空间中靠近。

4. C. 在给定的文本中找到相关性最强的词
    - 解释：GloVe模型的目标是学习词向量，这些向量可以捕捉到词的共现信息，即在给定的文本中，哪些词经常在一起出现。例如，在"GloVe模型"这个短语中，"GloVe"和"模型"就是共现的词。

5. B. 语义理解
    - 解释：表示学习的一个重要应用是语义理解。通过学习到的表示，我们可以更好地理解和解析文本数据。例如，通过学习词的向量表示，我们可以量化词的相似性，并使用这种相似性来理解和生成文本。

6. B. 都是词表示学习模型
    - 解释：Word2Vec和GloVe都是用于学习词向量的模型，它们都能将文本转换为实数向量，以便于机器进行处理。这两种模型的目标都是把词语映射到一个多维空间，使得语义上相似的词在这个空间中靠近。

7. C. 用一个实数向量表示
    - 解释：在OpenAI Embeddings中，每个词被表示为一个实数向量。这种表示可以捕获词语的语义，使得语义上相似的词在向量空间中靠近。例如，"猫"和"狗"这两个词，在向量空间中应该比"猫"和"汽车"更靠近。

8. B. 余弦相似度
    - 解释：在词向量中，词的相似度通常通过计算它们向量之间的余弦相似度来衡量。余弦相似度可以捕捉到向量的夹角，如果两个向量的方向相似（即夹角小），那么它们的余弦相似度就会高，表示这两个词在语义上是相似的。

9. C. 因为监督学习需要太多的标签数据
    - 解释：在监督学习中，我们需要大量的标签数据，这些数据往往难以获得。而在非监督学习中，我们可以利用大量的未标记数据。因此，在表示学习中，非监督学习是一种常见的方法。

10. B. 用随机梯度下降优化
    - 解释：在表示学习中，我们通常使用随机梯度下降（SGD）方法来优化词向量。SGD是一种迭代方法，每次只用一个（或者一小批）样本来更新参数，这样可以显著降低计算量，加快优化的速度。
