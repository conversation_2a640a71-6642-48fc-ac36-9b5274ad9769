# 大模型（LLMs）应用开发快速入门指南

## 课程表

| 课表 | 描述                                                                                                                                                                                                        | 课程资料                                                                           | 任务                                                                   |
|----------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------|
| 第1节   | 大模型基础：理论与技术的演进 <br/> - 初探大模型：起源与发展 <br/> - 预热篇：解码注意力机制 <br/> - 变革里程碑：Transformer的崛起 <br/> - 走向不同：GPT与BERT的选择 | 建议阅读：<br/>- [Attention Mechanism: Neural Machine Translation by Jointly Learning to Align and Translate](https://arxiv.org/abs/1409.0473)<br/>- [An Attentive Survey of Attention Models](https://arxiv.org/abs/1904.02874)<br/>- [Transformer：Attention is All you Need](https://arxiv.org/abs/1706.03762)<br/>- [BERT：Pre-training of Deep Bidirectional Transformers for Language Understanding(https://arxiv.org/abs/1810.04805) | [[作业](docs/homework_01.md)] |
| 第2节   | GPT 模型家族：从始至今 <br/> - 从GPT-1到GPT-3.5：一路的风云变幻 <br/> - ChatGPT：赢在哪里 <br/> - GPT-4：一个新的开始 <br/>提示学习（Prompt Learning） <br/> - 思维链（Chain-of-Thought, CoT）：开山之作 <br/> - 自洽性（Self-Consistency）：多路径推理 <br/> - 思维树（Tree-of-Thoughts, ToT）：续写佳话 | 建议阅读：<br/>- [GPT-1: Improving Language Understanding by Generative Pre-training](https://s3-us-west-2.amazonaws.com/openai-assets/research-covers/language-unsupervised/language_understanding_paper.pdf)<br/>- [GPT-2: Language Models are Unsupervised Multitask Learners](https://cdn.openai.com/better-language-models/language_models_are_unsupervised_multitask_learners.pdf)<br/>- [GPT-3: Language Models are Few-Shot Learners](https://arxiv.org/abs/2005.14165)<br/><br/><br/>额外阅读：<br/>- [GPT-4: Architecture, Infrastructure, Training Dataset, Costs, Vision, MoE](https://www.semianalysis.com/p/gpt-4-architecture-infrastructure)<br/>- [GPTs are GPTs: An Early Look at the Labor Market Impact Potential of Large Language Models](https://arxiv.org/abs/2303.10130)<br/>- [Sparks of Artificial General Intelligence: Early experiments with GPT-4](https://arxiv.org/abs/2303.12712)<br/><br/> | [[作业](docs/homework_02.md)] |
| 第3节   | 大模型开发基础：OpenAI Embedding <br/> - 通用人工智能的前夜 <br/> - "三个世界"和"图灵测试" <br/> - 计算机数据表示 <br/> - 表示学习和嵌入 <br/> Embeddings Dev 101 <br/> - 课程项目：GitHub openai-quickstart <br/> - 快速上手 OpenAI Embeddings                     | 建议阅读：<br/>- [Representation Learning: A Review and New Perspectives](https://arxiv.org/abs/1206.5538)<br/>- [Word2Vec: Efficient Estimation of Word Representations in Vector Space](https://arxiv.org/abs/1301.3781)<br/>- [GloVe: Global Vectors for Word Representation](https://nlp.stanford.edu/pubs/glove.pdf)<br/><br/>额外阅读：<br/><br/>- [Improving Distributional Similarity with Lessons Learned from Word Embeddings](http://www.aclweb.org/anthology/Q15-1016)<br/>- [Evaluation methods for unsupervised word embeddings](http://www.aclweb.org/anthology/D15-1036) | [[作业](docs/homework_03.md)]<br/>代码：<br/>[[embedding](openai_api/embedding.ipynb)] |
| 第4节   | OpenAI 大模型开发与应用实践 <br/> - OpenAI大型模型开发指南 <br/> - OpenAI 语言模型总览 <br/> - OpenAI GPT-4, GPT-3.5, GPT-3, Moderation <br/> - OpenAI Token 计费与计算 <br/>OpenAI API 入门与实战 <br/> - OpenAI Models API <br/> - OpenAI Completions API  <br/> - OpenAI Chat Completions API <br/> - Completions vs Chat Completions <br/>OpenAI 大模型应用实践 <br/> - 文本内容补全初探（Text Completion） <br/> - 聊天机器人初探（Chat Completion） | 建议阅读：<br/><br/>- [OpenAI Models](https://platform.openai.com/docs/models)<br/>- [OpenAI Completions API](https://platform.openai.com/docs/guides/gpt/completions-api)<br/>- [OpenAI Chat Completions API](https://platform.openai.com/docs/guides/gpt/chat-completions-api) | 代码：<br/>[[models](openai_api/models.ipynb)] <br/>[[tiktoken](openai_api/count_tokens_with_tiktoken.ipynb)] |
| 第5节   | AI大模型应用最佳实践 <br/> - 如何提升GPT模型使用效率与质量 <br/> - AI大模型应用最佳实践 <br/>   - 文本创作与生成<br/>   - 文章摘要和总结 <br/>    - 小说生成与内容监管 <br/>    - 分步骤执行复杂任务 <br/>    - 评估模型输出质量 <br/>    - 构造训练标注数据 <br/>    - 代码调试助手 <br/> - 新特性： Function Calling 介绍与实战 | 建议阅读 <br/> - [GPT Best Practices](https://platform.openai.com/docs/guides/gpt-best-practices) <br/> - [Function Calling](https://platform.openai.com/docs/guides/gpt/function-calling) | 代码： <br/> [Function Calling](openai_api/function_call.ipynb) |
| 第6节   | 实战：OpenAI-Translator <br/> - OpenAI-Translator 市场需求分析 <br/> - OpenAI-Translator 产品定义与功能规划 <br/> - OpenAI-Translator 技术方案与架构设计 <br/> - OpenAI 模块设计 <br/> - OpenAI-Translator 实战 <br/>  |  | 代码： <br/> [pdfplumber](openai-translator/jupyter/pdfplumber.ipynb) |
| 第7节   | 实战：ChatGPT Plugin 开发 <br/> - ChatGPT Plugin 开发指南 <br/> - ChatGPT Plugin 介绍 <br/> - ChatGPT Plugin 介绍 <br/> - 样例项目：待办（Todo）管理插件 <br/> - 实战样例部署与测试 <br/> - ChatGPT 开发者模式 <br/> - 实战：天气预报（Weather Forecast）插件开发 <br/> - Weather Forecast Plugin 设计与定义 <br/> - 天气预报函数服务化 <br/> - 第三方天气查询平台对接 <br/> - 实战 Weather Forecast Plugin <br/> - Function Calling vs ChatGPT plugin <br/>  | | 代码： <br/> [[todo list](chatgpt-plugins/todo-list)]  <br/> [[Weather Forecast](chatgpt-plugins/weather-forecast)] |
| 第8节   | 大模型应用开发框架 LangChain (上) <br/> - LangChain 101  <br/> - LangChain 是什么 <br/> - 为什么需要 LangChain <br/> - LangChain 典型使用场景 <br/> - LangChain 基础概念与模块化设计 <br/> - LangChain 核心模块入门与实战 <br/> - 标准化的大模型抽象：Mode I/O <br/> -  模板化输入：Prompts <br/> -  语言模型：Models <br/> - 规范化输出：Output Parsers  | | 代码： <br/> [[model io](langchain/jupyter/model_io)] |
| 第9节   | 大模型应用开发框架 LangChain (中) <br/> - 大模型应用的最佳实践 Chains <br/> - 上手你的第一个链：LLM Chain <br/> - 串联式编排调用链：Sequential Chain <br/> - 处理超长文本的转换链：Transform Chain <br/> - 实现条件判断的路由链：Router Chain <br/> - 赋予应用记忆的能力： Memory <br/> - Momory System 与 Chain 的关系 <br/> - 记忆基类 BaseMemory 与 BaseChatMessageMemory <br/> - 服务聊天对话的记忆系统 <br/> - ConversationBufferMemory <br/> - ConversationBufferWindowMemory <br/> - ConversationSummaryBufferMemory |  | 代码： <br/> [[chains](langchain/jupyter/chains)] <br/> [[memory](langchain/jupyter/memory)] |
| 第10节  | 大模型应用开发框架 LangChain (下) <br/> - 框架原生的数据处理流 Data Connection <br/> - 文档加载器（Document Loaders） <br/> - 文档转换器（Document Transformers） <br/> - 文本向量模型（Text Embedding Models） <br/> - 向量数据库（Vector Stores） <br/> - 检索器（Retrievers） <br/> - 构建复杂应用的代理系统 Agents <br/> - Agent 理论基础：ReAct <br/> -  LLM 推理能力：CoT, ToT <br/> -  LLM 操作能力：WebGPT, SayCan <br/> - LangChain Agents 模块设计与原理剖析 <br/> -  Module: Agent, Tools, Toolkits, <br/> -  Runtime: AgentExecutor, PlanAndExecute , AutoGPT, <br/> - 上手第一个Agent：Google Search + LLM <br/> - 实战 ReAct：SerpAPI + LLM-MATH |  | 代码： <br/> [[data connection](langchain/jupyter/data_connection)] <br/> [[agents](langchain/jupyter/agents)] |
| 第11节  | 实战： LangChain 版 OpenAI-Translator v2.0 <br/> - 深入理解 Chat Model 和 Chat Prompt Template <br/> - 温故：LangChain Chat Model 使用方法和流程 <br/> - 使用 Chat Prompt Template 设计翻译提示模板 <br/> - 使用 Chat Model 实现双语翻译 <br/> - 使用 LLMChain 简化构造 Chat Prompt <br/> - 基于 LangChain 优化 OpenAI-Translator 架构设计 <br/> - 由 LangChain 框架接手大模型管理 <br/> - 聚焦应用自身的 Prompt 设计 <br/> - 使用 TranslationChain 实现翻译接口 <br/> - 更简洁统一的配置管理 <br/> - OpenAI-Translator v2.0 功能特性研发 <br/> - 基于Gradio的图形化界面设计与实现 <br/> - 基于 Flask 的 Web Server 设计与实现 |  | 代码： <br/> [[openai-translator](langchain/openai-translator)] |
| 第12节  | 实战： LangChain 版Auto-GPT  <br/> - Auto-GPT 项目定位与价值解读 <br/> - Auto-GPT 开源项目介绍 <br/> - Auto-GPT 定位：一个自主的 GPT-4 实验 <br/> - Auto-GPT 价值：一种基于 Agent 的 AGI 尝试 <br/> - LangChain 版 Auto-GPT 技术方案与架构设计 <br/> - 深入理解 LangChain Agents <br/> - LangChain Experimental 模块 <br/> - Auto-GPT 自主智能体设计 <br/> - Auto-GPT Prompt 设计 <br/> - Auto-GPT Memory 设计 <br/> - 深入理解 LangChain VectorStore <br/> - Auto-GPT OutputParser 设计 <br/> - 实战 LangChain 版 Auto-GPT |    | 代码： <br/> [[autogpt](langchain/jupyter/autogpt)] |
| 第13节  | Sales-Consultant 业务流程与价值分析 <br/> - Sales-Consultant 技术方案与架构设计 <br/> - 使用 GPT-4 生成销售话术 <br/> - 使用 FAISS 向量数据库存储销售问答话术 <br/> - 使用 RetrievalQA 检索销售话术数据 <br/> - 使用 Gradio 实现聊天机器人的图形化界面 <br/> - 实战 LangChain 版 Sales-Consultant | | 代码： <br/> [[sales_chatbot](langchain/sales_chatbot)] |
| 第14节  | 大模型时代的开源与数据协议 <br/> - 什么是开源？ <br/> - 广泛使用的开源协议和数据协议 <br/> - Llama 是不是伪开源？ <br/> - ChatGLM2-6B 的开源协议 <br/> 大语言模型的可解释性 <br/> - 提高模型决策过程的透明度 <br/> - Stanford Alpaca 的相关研究 <br/> 大语言模型应用的法规合规性 <br/> - 中国大陆：生成式人工智能服务备案 <br/> - 国际化：数据隐私与保护（以 GDPR 为例） <br/> - 企业合规性应对要点 | | |
| 第15节  | 大模型时代的Github：Hugging Face <br/> - Hugging Face 是什么？ <br/> - Hugging Face Transformers 库 <br/> - Hugging Face 开源社区：Models, Datasets, Spaces, Docs <br/> - 大模型横向对比 <br/> - Open LLM Leaderboard（大模型天梯榜） <br/> 显卡选型推荐指南 <br/> - GPU vs 显卡 <br/> - GPU Core vs AMD CU <br/> - CUDA Core vs Tensor Core <br/> - N卡的架构变迁 <br/> - 显卡性能天梯榜 | | |
| 第16节  | 清华 GLM 大模型家族 <br/> - 最强基座模型 GLM-130B  <br/> - 增强对话能力 ChatGLM <br/> - 开源聊天模型 ChatGLM2-6B <br/> - 联网检索能力 WebGLM <br/> - 初探多模态 VisualGLM-6B <br/> - 代码生成模型 CodeGeex2 <br/> ChatGLM2-6B 大模型应用开发 <br/> - ChatGLM2-6B 私有化部署 <br/> - HF Transformers Tokenizer <br/> - HF Transformers Model <br/> - 将模型同步至 Hugging Face <br/> - 使用 Gradio 赋能 ChatGLM2-6B 图形化界面 <


## Schedule

| Lesson     | Description                                                                                                                                                                                                        | Course Materials                                                                          | Events                                                                    |
|------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------|
| Lesson 1   | Fundamentals of Large Models: Evolution of Theory and Technology <br/> - An Initial Exploration of Large Models: Origin and Development <br/> - Warm-up: Decoding Attention Mechanism <br/> - Milestone of Transformation: The Rise of Transformer <br/> - Taking Different Paths: The Choices of GPT and Bert | Suggested Readings:<br/>- [Attention Mechanism: Neural Machine Translation by Jointly Learning to Align and Translate](https://arxiv.org/abs/1409.0473)<br/>- [An Attentive Survey of Attention Models](https://arxiv.org/abs/1904.02874)<br/>- [Transformer: Attention is All you Need](https://arxiv.org/abs/1706.03762)<br/>- [BERT: Pre-training of Deep Bidirectional Transformers for Language Understanding](https://arxiv.org/abs/1810.04805) | [[Homework](docs/homework_01.md)] |
| Lesson 2   | The GPT Model Family: From Start to Present <br/> - From GPT-1 to GPT-3.5: The Evolution <br/> - ChatGPT: Where It Wins <br/> - GPT-4: A New Beginning <br/>Prompt Learning <br/> - Chain-of-Thought (CoT): The Pioneering Work <br/> - Self-Consistency: Multi-path Reasoning <br/> - Tree-of-Thoughts (ToT): Continuing the Story | Suggested Readings:<br/>- [GPT-1: Improving Language Understanding by Generative Pre-training](https://s3-us-west-2.amazonaws.com/openai-assets/research-covers/language-unsupervised/language_understanding_paper.pdf)<br/>- [GPT-2: Language Models are Unsupervised Multitask Learners](https://cdn.openai.com/better-language-models/language_models_are_unsupervised_multitask_learners.pdf)<br/>- [GPT-3: Language Models are Few-Shot Learners](https://arxiv.org/abs/2005.14165)<br/><br/><br/>Additional Readings:<br/>- [GPT-4: Architecture, Infrastructure, Training Dataset, Costs, Vision, MoE](https://www.semianalysis.com/p/gpt-4-architecture-infrastructure)<br/>- [GPTs are GPTs: An Early Look at the Labor Market Impact Potential of Large Language Models](https://arxiv.org/abs/2303.10130)<br/>- [Sparks of Artificial General Intelligence: Early experiments with GPT-4](https://arxiv.org/abs/2303.12712)<br/><br/> | [[Homework](docs/homework_02.md)] |
| Lesson 3   | Fundamentals of Large Model Development: OpenAI Embedding <br/> - The Eve of General Artificial Intelligence <br/> - "Three Worlds" and "Turing Test" <br/> - Computer Data Representation <br/> - Representation Learning and Embedding <br/> Embeddings Dev 101 <br/> - Course Project: GitHub openai-quickstart <br/> - Getting Started with OpenAI Embeddings                      | Suggested Readings:<br/>- [Representation Learning: A Review and New Perspectives](https://arxiv.org/abs/1206.5538)<br/>- [Word2Vec: Efficient Estimation of Word Representations in Vector Space](https://arxiv.org/abs/1301.3781)<br/>- [GloVe: Global Vectors for Word Representation](https://nlp.stanford.edu/pubs/glove.pdf)<br/><br/>Additional Readings:<br/><br/>- [Improving Distributional Similarity with Lessons Learned from Word Embeddings](http://www.aclweb.org/anthology/Q15-1016)<br/>- [Evaluation methods for unsupervised word embeddings](http://www.aclweb.org/anthology/D15-1036) | [[Homework](docs/homework_03.md)]<br/>Code:<br/>[[embedding](openai_api/embedding.ipynb)] |
| Lesson 4   | OpenAI Large Model Development and Application Practice <br/> - OpenAI Large Model Development Guide <br/> - Overview of OpenAI Language Models <br/> - OpenAI GPT-4, GPT-3.5, GPT-3, Moderation <br/> - OpenAI Token Billing and Calculation <br/>OpenAI API Introduction and Practice <br/> - OpenAI Models API <br/> - OpenAI Completions API  <br/> - OpenAI Chat Completions API <br/> - Completions vs Chat Completions <br/>OpenAI Large Model Application Practice <br/> - Initial Exploration of Text Completion <br/> - Initial Exploration of Chatbots | Suggested Readings:<br/><br/>- [OpenAI Models](https://platform.openai.com/docs/models)<br/>- [OpenAI Completions API](https://platform.openai.com/docs/guides/gpt/completions-api)<br/>- [OpenAI Chat Completions API](https://platform.openai.com/docs/guides/gpt/chat-completions-api) | Code:<br/>[[models](openai_api/models.ipynb)] <br/>[[tiktoken](openai_api/count_tokens_with_tiktoken.ipynb)] |
| Lesson 5   | Best Practices for Applying Large AI Models <br/> - How to Improve the Efficiency and Quality of GPT Model Use <br/> - Best Practices for Applying Large AI Models <br/>   - Text Creation and Generation<br/>   - Article Abstract and Summary <br/>    - Novel Generation and Content Supervision <br/>    - Executing Complex Tasks Step by Step <br/>    - Evaluating the Quality of Model Output <br/>    - Constructing Training Annotation Data <br/>    - Code Debugging Assistant <br/> - New Features： Function Calling Introduction and Practical Application | Suggested Readings <br/> - [GPT Best Practices](https://platform.openai.com/docs/guides/gpt-best-practices) <br/> - [Function Calling](https://platform.openai.com/docs/guides/gpt/function-calling) | Code： <br/> [Function Calling](openai_api/function_call.ipynb) |
| Lesson 6   | Practical: OpenAI-Translator <br/> - Market demand analysis for OpenAI-Translator <br/> - Product definition and feature planning for OpenAI-Translator <br/> - Technical solutions and architecture design for OpenAI-Translator <br/> - OpenAI module design <br/> - OpenAI-Translator practical application <br/> | | Code: <br/> [pdfplumber](openai-translator/jupyter/pdfplumber.ipynb) |
| Lesson 7   | ChatGPT Plugin Development Guide <br/> - Introduction to ChatGPT Plugin <br/> - Sample project: Todo management plugin <br/> - Deployment and testing of practical examples <br/> - ChatGPT developer mode <br/> - Practical: Weather Forecast plugin development <br/> - Weather Forecast Plugin design and definition <br/> - Weather Forecast function service <br/> - Integration with third-party weather query platform <br/> - Practical Weather Forecast Plugin | | Code: <br/> [[todo list](chatgpt-plugins/todo-list)] <br/> [[weather forecast](chatgpt-plugins/weather-forecast)]  |
| Lesson 8   | LLM Application Development Framework LangChain (Part 1) <br/> - LangChain 101  <br/> - What is LangChain <br/> - Why LangChain is Needed <br/> - Typical Use Cases of LangChain <br/> - Basic Concepts and Modular Design of LangChain <br/> - Introduction and Practice of LangChain Core Modules <br/> - Standardized Large-Scale Model Abstraction: Mode I/O <br/> -  Template Input: Prompts <br/> -  Language Model: Models <br/> - Standardized Output: Output Parsers  | | Code: <br/> [[model io](langchain/jupyter/model_io)] |
| Lesson 9   | LLM Application Development Framework LangChain (Part 2) <br/> - Best Practices for LLM Chains <br/> - Getting Started with Your First Chain: LLM Chain <br/> - Sequential Chain: A Chained Call with Sequential Arrangement <br/> - Transform Chain: A Chain for Processing Long Texts <br/> - Router Chain: A Chain for Implementing Conditional Judgments <br/> - Memory: Endowing Applications with Memory Capabilities <br/> - The Relationship between Memory System and Chain <br/> - BaseMemory and BaseChatMessageMemory: Memory Base Classes <br/> - Memory System for Service Chatting <br/> - ConversationBufferMemory <br/> - ConversationBufferWindowMemory <br/> - ConversationSummaryBufferMemory  |  | Code: <br/> [[chains](langchain/jupyter/chains)] <br/> [[memory](langchain/jupyter/memory)] |
| Lesson 10  | LLM Application Development Framework LangChain (Part 3)  <br/> - Native data processing flow of the framework: Data Connection <br/> - Document Loaders <br/> - Document Transformers <br/> - Text Embedding Models <br/> - Vector Stores <br/> - Retrievers <br/> - Agent Systems for Building Complex Applications: Agents <br/> - Theoretical Foundation of Agents: ReAct <br/> - LLM Reasoning Capabilities: CoT, ToT <br/> - LLM Operation Capabilities: WebGPT, SayCan <br/> - LangChain Agents Module Design and Principle Analysis <br/> - Module: Agent, Tools, Toolkits <br/> - Runtime: AgentExecutor, PlanAndExecute, AutoGPT <br/> - Getting Started with Your First Agent: Google Search + LLM <br/> - Practice with ReAct: SerpAPI + LLM-MATH |  | Code: <br/> [[data connection](langchain/jupyter/data_connection)] <br/> [[agents](langchain/jupyter/agents)] |
| Lesson 11  | Practical: LangChain version OpenAI-Translator v2.0 <br/> - In-depth understanding of Chat Model and Chat Prompt Template <br/> - Review: LangChain Chat Model usage and process <br/> - Design translation prompt templates using Chat Prompt Template <br/> - Implement bilingual translation using Chat Model <br/> - Simplify Chat Prompt construction using LLMChain <br/> - Optimize OpenAI-Translator architecture design based on LangChain <br/> - Hand over large model management to LangChain framework <br/> - Focus on application-specific Prompt design <br/> - Implement translation interface using TranslationChain <br/> - More concise and unified configuration management <br/> - Development of OpenAI-Translator v2.0 feature <br/> - Design and implementation of graphical interface based on Gradio <br/> - Design and implementation of Web Server based on Flask | | Code:  <br/> [[openai-translator](langchain/openai-translator)] |
| Lesson 12  | Practical: LangChain version Auto-GPT <br/> - Auto-GPT project positioning and value interpretation <br/> - Introduction to Auto-GPT open source project <br/> - Auto-GPT positioning: an independent GPT-4 experiment <br/> - Auto-GPT value: an attempt at AGI based on Agent <br/> - LangChain version Auto-GPT technical solution and architecture design <br/> - In-depth understanding of LangChain Agents <br/> - LangChain Experimental module <br/> - Auto-GPT autonomous agent design <br/> - Auto-GPT Prompt design <br/> - Auto-GPT Memory design <br/> - In-depth understanding of LangChain VectorStore <br/> - Auto-GPT OutputParser design <br/> - Practical LangChain version Auto-GPT | | Code: <br/> [[autogpt](langchain/jupyter/autogpt)] |
| Lesson 13  | Sales-Consultant business process and value analysis <br/> - Technical solution and architecture design of Sales-Consultant <br/> - Use GPT-4 to generate sales pitches <br/> - Store sales Q&A pitches in FAISS vector database <br/> - Retrieve sales pitches data using RetrievalQA <br/> - Implement chatbot graphical interface using Gradio <br/> - Practical LangChain version Sales-Consultant | | Code： <br/> [[sales_chatbot](langchain/sales_chatbot)] |
| Lesson 14  | Era of large models: Open source and data protocols <br/> - What is open source? <br/> - Widely used open source and data protocols <br/> - Is Llama pseudo-open source? <br/> - Open source protocol of ChatGLM2-6B <br/> Interpretability of large language models <br/> - Enhancing transparency in model decision-making <br/> - Related research of Stanford Alpaca <br/> Regulatory compliance of large language model applications <br/> - Mainland China: Registration of generative AI services <br/> - International: Data privacy and protection (taking GDPR as an example) <br/> - Key points of corporate compliance | | |
| Lesson 15  | Github in the era of large models: Hugging Face <br/> - What is Hugging Face? <br/> - Hugging Face Transformers library <br/> - Hugging Face open community: Models, Datasets, Spaces, Docs <br/> - Comparative analysis of large models <br/> - Open LLM Leaderboard (Large Model Ladder) <br/> Graphics card selection guide <br/> - GPU vs Graphics card <br/> - GPU Core vs AMD CU <br/> - CUDA Core vs Tensor Core <br/> - Evolution of Nvidia architectures <br/> - Graphics card performance ladder | | |
| Lesson 16  | Tsinghua GLM large model family <br/> - Strongest base model GLM-130B <br/> - Enhanced dialogue capability ChatGLM <br/> - Open source chat model ChatGLM2-6B <br/> - Internet search capability WebGLM <br/> - Initial exploration of multimodal VisualGLM-6B <br/> - Code generation model CodeGeex2 <br/> Application development of ChatGLM2-6B large model <br/> - Private deployment of ChatGLM2-6B <br/> - HF Transformers Tokenizer <br/> - HF Transformers Model <br/> - Synchronize the model to Hugging Face <br/> - Empower ChatGLM2-6B graphical interface using Gradio <br/> - Fine-tuning of ChatGLM2-6B model <br/> - Practical assignment: Implement graphical interface of openai-translator based on ChatGLM2-6B | | |

