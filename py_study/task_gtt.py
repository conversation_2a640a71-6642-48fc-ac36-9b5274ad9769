from bs4 import BeautifulSoup
import requests

task_ids = ['6407']

cookies = {'za': 'zouxin', 'zentaosid': '0f7c95fa3f2420c5a0df6254c2609649'}


def get_tasks(task_id: str):
    url = 'http://***************/zentao/task-view-{}.html'.format(task_id)
    response = requests.request("GET", url, cookies=cookies)

    soup = BeautifulSoup(response.text, 'html.parser')
    children_table = soup.find(id='childrenTable')
    t_bodies = children_table.select('tbody')

    ids = []
    for body in t_bodies:
        trs = body.select("tr")
        for tr in trs:
            id = tr.select('td')[0]
            ids.append(id.text)

    return ids


def get_subtask(subtask_id: str):
    url = 'http://***************/zentao/task-view-{}.html?onlybody=yes'.format(subtask_id)
    response = requests.request("GET", url, cookies=cookies)

    soup = BeautifulSoup(response.text, 'html.parser')

    name = soup.find(id='mainMenu').find(class_='text').text.split('/')[1].strip()

    legend_effort_table = soup.find(id='legendEffort').find('table')
    legend_effort_tds = legend_effort_table.find_all('td')
    start_time = legend_effort_tds[3].text.strip()
    end_time = legend_effort_tds[5].text.strip()

    basic_tds = soup.find(id='legendBasic').find('tbody').find_all('td')
    worker = basic_tds[2].text.split('于')[0].strip()
    status = basic_tds[4].text.strip()
    order = basic_tds[6].text.strip()

    return [name, order, status, worker, start_time, end_time]


if __name__ == '__main__':
    subtasks = []
    for task_id in task_ids:
        subtask_ids = get_tasks('10649')
        for subtask_id in subtask_ids:
            subtask = get_subtask(subtask_id)
            subtasks.append(subtask)

    for subtask in subtasks:
        for v in subtask:
            print(v, end="\t")
        print()