{"cells": [{"cell_type": "code", "execution_count": null, "id": "5fa5e5c3-284f-422d-ad2e-2415fb09ce64", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "8d0c3980-e0ac-41ea-ac1a-b69bf761e0ba", "metadata": {}, "source": ["### 设置 LangS<PERSON> 项目名称"]}, {"cell_type": "code", "execution_count": 1, "id": "59d9538c-a63d-4220-8a73-be6d35f2a163", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"LANGCHAIN_TRACING_V2\"] = \"true\"\n", "os.environ[\"LANGCHAIN_PROJECT\"] = \"ChatPPT Jupyter\""]}, {"cell_type": "markdown", "id": "0b310b2e-76d2-43e3-a2bc-518ce95b2885", "metadata": {}, "source": ["### Jupyter 环境设置（与命令行启动保持一致）\n", "\n", "1. 项目根目录（ChatPPT）为当前工作目录\n", "2. 源代码目录添加到 Python 路径"]}, {"cell_type": "code", "execution_count": 2, "id": "d36a92ae-a94a-42ab-8715-e62c22e93430", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Current working directory: /home/<USER>/projects/ChatPPT\n"]}], "source": ["# 切换到上一级目录，即项目根目录\n", "os.chdir(\"..\")\n", "\n", "# 验证当前工作目录\n", "print(\"Current working directory:\", os.getcwd())"]}, {"cell_type": "code", "execution_count": 3, "id": "b34f45b2-8d66-4c7b-abfc-4a98a53c221a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['/home/<USER>/miniconda3/envs/chatppt/lib/python310.zip', '/home/<USER>/miniconda3/envs/chatppt/lib/python3.10', '/home/<USER>/miniconda3/envs/chatppt/lib/python3.10/lib-dynload', '', '/home/<USER>/miniconda3/envs/chatppt/lib/python3.10/site-packages', '/home/<USER>/projects/ChatPPT/src']\n"]}], "source": ["import sys\n", "\n", "# 获取项目源代码目录 src\n", "srouce_code_path = os.path.abspath(\"./src\")\n", "\n", "# 将源代码目录 src 添加到 sys.path\n", "sys.path.append(srouce_code_path)\n", "\n", "# 打印当前 PYTHON 路径\n", "print(sys.path)"]}, {"cell_type": "markdown", "id": "74858849-3c93-45e9-810b-ac1f5734889c", "metadata": {}, "source": ["## 自动检索图像，提升 PowerPoint 表达能力\n", "\n", "### 1. Chat<PERSON>ot 生成纯文本"]}, {"cell_type": "code", "execution_count": 4, "id": "3874846e-f224-46d8-ad96-7dc4b4c5d45e", "metadata": {}, "outputs": [], "source": ["from config import Config\n", "from chatbot import ChatBot\n", "\n", "config = Config()\n", "chatbot = ChatBot(config.chatbot_prompt)"]}, {"cell_type": "code", "execution_count": 5, "id": "05a316d5-62b4-4c87-bce9-538a63aad335", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["**Role**: You are a knowledgeable Chatbot capable of answering a wide range of user questions.\n", "\n", "**Task**: When responding to user inquiries, format your answers in a presentation-friendly style suited for PowerPoint slides. Organize the content into a structured, slide-by-slide layout with **at least 10 slides**. Ensure each slide is rich in detail and elaboration.\n", "\n", "**Format**: Structure your responses as follows:\n", "\n", "```\n", "# [Presentation Theme]  // Only once, for the first slide as the presentation's theme\n", "\n", "## [Slide Title]\n", "- [Key point 1]: [Introduction or summary of the point]\n", "  - [Detailed explanation covering multiple aspects or subpoints]\n", "    - [Specific examples, case studies, or further insights]\n", "  - [Additional detail or secondary aspect]\n", "    - [Supporting data, quotes, or statistics]\n", "- [Key point 2]: [Brief introduction or summary]\n", "  - [Expanded description with step-by-step breakdown]\n", "    - [Practical application, scenarios, or research findings]\n", "\n", "## [Slide Title]\n", "- [Key point 1]: [Comprehensive explanation]\n", "  - [Second Level]: [Elaboration on critical points, providing context or rationale]\n", "  - [Second Level]: [Additional insight or perspective]\n", "- [Key point 2]: [Clear overview with actionable insights]\n", "  - [Second Level]: [Supporting data, strategies, or methods]\n", "    - [Third Level]: [Examples or further clarification]\n", "```\n", "\n", "**Guidelines**:\n", "- Each response should include **a minimum of 10 slides**.\n", "- Ensure that each slide has **multiple detailed points**, with second-level explanations providing thorough descriptions and third-level points adding examples or additional insights.\n", "- The **Presentation Theme** should appear only on the first slide, and no images or image URLs are needed.\n", "- Response in Chinese.\n"]}], "source": ["print(chatbot.prompt)"]}, {"cell_type": "code", "execution_count": 6, "id": "bcf53954-060e-47ef-bc79-3e73a89d2583", "metadata": {}, "outputs": [], "source": ["input_text = \"向没有做过股票投资的小白，介绍下纳斯达克\""]}, {"cell_type": "code", "execution_count": 7, "id": "5a5820cf-e1e6-4fc2-94c1-f983b195410f", "metadata": {}, "outputs": [], "source": ["user_requirement = \"需求如下:\\n\" + input_text"]}, {"cell_type": "code", "execution_count": 8, "id": "4afb26da-d08e-401b-95ef-9658b6b911ed", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2024-11-01 18:58:56 | DEBUG | chatbot:chat_with_history:76 - [ChatBot] # 纳斯达克简介\n", "\n", "## 纳斯达克概述\n", "- **什么是纳斯达克**: 纳斯达克是美国的一个股票交易市场。\n", "  - 纳斯达克全称为“全国证券交易商自动报价系统”，成立于1971年，是全球第一个电子股票市场。\n", "  - 它主要以科技公司为主，吸引了许多创新型企业上市。\n", "- **纳斯达克的功能**: 提供股票交易和信息服务。\n", "  - 交易者可以通过电子系统进行股票买卖，提升了交易效率。\n", "  - 纳斯达克还提供实时的市场数据和分析工具，帮助投资者做出决策。\n", "\n", "## 纳斯达克的市场结构\n", "- **市场类型**: 纳斯达克是一个场外交易市场（OTC）。\n", "  - 与传统的交易所不同，纳斯达克没有物理交易场所，所有交易通过电子系统进行。\n", "  - 这种结构使得纳斯达克能够提供更灵活的交易方式。\n", "- **上市公司**: 纳斯达克上市公司主要集中在高科技和生物科技领域。\n", "  - 例如，知名公司如苹果、亚马逊和谷歌母公司Alphabet均在纳斯达克上市。\n", "  - 这些公司通常具有较高的市场增长潜力，吸引了大量投资者。\n", "\n", "## 纳斯达克的指数\n", "- **纳斯达克综合指数**: 反映纳斯达克市场整体表现的主要指数。\n", "  - 该指数包含了纳斯达克上市的所有股票，广泛用于衡量市场的健康状况。\n", "  - 投资者可以通过观察该指数的变动来判断市场趋势。\n", "- **纳斯达克100指数**: 包含100家最大的非金融公司。\n", "  - 该指数侧重于大型科技公司，常被视为科技行业的风向标。\n", "  - 投资者可以通过该指数了解科技股的整体表现。\n", "\n", "## 纳斯达克的交易机制\n", "- **电子交易系统**: 纳斯达克的交易完全依赖电子系统。\n", "  - 交易者通过计算机进行订单的输入和执行，减少了人为错误。\n", "  - 该系统支持高频交易，能够快速响应市场变化。\n", "- **做市商制度**: 纳斯达克采用做市商制度来提供流动性。\n", "  - 做市商是为特定股票提供买卖报价的公司，确保市场有足够的流动性。\n", "  - 这种机制帮助投资者在需要时能够迅速买入或卖出股票。\n", "\n", "## 纳斯达克的投资机会\n", "- **高增长潜力**: 纳斯达克上市公司大多具有较高的增长潜力。\n", "  - 科技行业的快速发展使得许多公司在短时间内实现了巨额利润。\n", "  - 投资者可以通过选择优质科技股获得良好的投资回报。\n", "- **多样化投资**: 投资者可以通过纳斯达克实现投资组合的多样化。\n", "  - 纳斯达克包含多个行业的公司，投资者可以根据自己的风险偏好选择不同的股票。\n", "  - 例如，可以投资于生物科技、云计算、人工智能等领域的公司。\n", "\n", "## 纳斯达克的风险因素\n", "- **市场波动性**: 纳斯达克股票市场波动性较大。\n", "  - 科技股受到市场情绪和经济数据的影响，价格波动频繁。\n", "  - 投资者需要具备承受市场波动的心理准备。\n", "- **行业集中风险**: 纳斯达克以科技股为主，行业集中度较高。\n", "  - 如果科技行业出现整体下滑，可能会对纳斯达克产生重大影响。\n", "  - 投资者应关注行业动态，避免过于集中于某一行业。\n", "\n", "## 如何投资纳斯达克\n", "- **选择合适的券商**: 投资者需要选择合适的证券公司进行交易。\n", "  - 许多在线券商提供纳斯达克股票交易，投资者可以根据费用和服务选择。\n", "  - 一些券商还提供模拟交易功能，帮助新手熟悉交易流程。\n", "- **研究与分析**: 投资者应进行充分的市场研究与分析。\n", "  - 关注公司财务报告、行业趋势和市场新闻，做出明智的投资决策。\n", "  - 使用技术分析工具，帮助判断买卖时机。\n", "\n", "## 纳斯达克的未来展望\n", "- **科技创新的推动**: 随着科技的不断发展，纳斯达克将继续吸引新兴企业上市。\n", "  - 人工智能、区块链和生物科技等领域的创新将为市场带来新的投资机会。\n", "  - 投资者可以关注这些新兴领域的公司，寻找潜在的投资标的。\n", "- **全球化趋势**: 纳斯达克在全球范围内的影响力不断增强。\n", "  - 越来越多的外国公司选择在纳斯达克上市，扩大了投资者的选择范围。\n", "  - 投资者可以通过纳斯达克接触到全球市场的机会。\n", "\n", "## 总结与建议\n", "- **投资前要做好功课**: 对纳斯达克及其上市公司进行深入了解。\n", "  - 理解市场机制和行业动态，有助于做出明智的投资决策。\n", "- **保持理性与耐心**: 投资股市需保持理性，避免情绪化决策。\n", "  - 设定合理的投资目标和风险承受能力，制定适合自己的投资策略。\n"]}], "source": ["slides_content = chatbot.chat_with_history(user_requirement)"]}, {"cell_type": "markdown", "id": "0b31039f-b4e9-45f3-bc34-04c5d43cab9c", "metadata": {}, "source": ["### 2. 基于内容嵌入图像"]}, {"cell_type": "code", "execution_count": 10, "id": "9dc45c11-046c-47be-a4b1-64100c7ffab1", "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "from langchain_core.prompts import ChatPromptTemplate  # 导入提示模板相关类\n", "from langchain_core.messages import HumanMessage  # 导入消息类"]}, {"cell_type": "code", "execution_count": 11, "id": "aac0ade6-ec8e-4b6d-a602-33d0db61a458", "metadata": {}, "outputs": [], "source": ["with open(\"prompts/image_advisor.txt\", \"r\", encoding=\"utf-8\") as file:\n", "    system_prompt = file.read().strip()"]}, {"cell_type": "code", "execution_count": 12, "id": "1ec3193f-f850-4308-82c0-da31c561f9dc", "metadata": {}, "outputs": [], "source": ["chat_prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", system_prompt),  # 系统提示部分\n", "    (\"human\", \"**Content**:\\n\\n{input}\"),  # 消息占位符\n", "])"]}, {"cell_type": "code", "execution_count": 13, "id": "91b373ef-f1f6-4066-a3a0-1cd359bcc166", "metadata": {}, "outputs": [], "source": ["chat_model = ChatOpenAI(\n", "            model=\"gpt-4o-mini\",\n", "            temperature=0.7,\n", "            max_tokens=4096,\n", "        )"]}, {"cell_type": "code", "execution_count": 14, "id": "71f60a40-dff8-47a4-9c41-4a844fb29339", "metadata": {}, "outputs": [], "source": ["image_advisor = chat_prompt | chat_model"]}, {"cell_type": "code", "execution_count": 15, "id": "638ca646-a955-4d92-8d93-e850bbc9750d", "metadata": {}, "outputs": [], "source": ["response = image_advisor.invoke(\n", "    {\n", "        \"input\": slides_content\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": 16, "id": "e29d2465-1583-4550-bab1-d13988de2252", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[纳斯达克概述]: 股票市场电子交易  \n", "[纳斯达克的市场结构]: 高科技公司  \n", "[纳斯达克的未来展望]: 科技创新与投资机会  \n"]}], "source": ["print(response.content)"]}, {"cell_type": "markdown", "id": "4dde15dd-2440-4533-9283-966f730bd1d3", "metadata": {}, "source": ["### 3. 搜索引擎检索图像（关键词由大模型生成）"]}, {"cell_type": "code", "execution_count": 68, "id": "4d4eda9f-60b4-420a-b436-10627e1885cb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'纳斯达克概述': '股票市场电子交易', '纳斯达克的市场结构': '高科技公司', '纳斯达克的未来展望': '科技创新与投资机会'}\n"]}], "source": ["import re\n", "\n", "# 使用正则表达式提取方括号中的内容和后面的关键词\n", "pairs = re.findall(r'\\[(.+?)\\]:\\s*(.+)', response.content)\n", "\n", "# 将提取的结果转换为字典，并去除键和值前后的空格\n", "keywords = {key.strip(): value.strip() for key, value in pairs}\n", "\n", "\n", "# 打印提取出的关键词\n", "print(keywords)"]}, {"cell_type": "code", "execution_count": 71, "id": "b54e170e-5876-4efb-ad76-5d843af6dccf", "metadata": {}, "outputs": [], "source": ["import requests\n", "from bs4 import BeautifulSoup\n", "from PIL import Image\n", "from io import BytesIO\n", "\n", "def get_bing_images(slide_title, query, num_images=5):\n", "    # 构造 Bing 图像搜索 URL\n", "    url = f\"https://www.bing.com/images/search?q={query}\"\n", "\n", "    # 添加 HTTP 请求头，模拟浏览器请求\n", "    headers = {\n", "        \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36\"\n", "    }\n", "\n", "    # 发送请求\n", "    response = requests.get(url, headers=headers)\n", "\n", "    # 检查响应状态码\n", "    if response.status_code != 200:\n", "        print(\"Error fetching page:\", response.status_code)\n", "        return []\n", "\n", "    # 解析 HTML\n", "    soup = BeautifulSoup(response.text, \"html.parser\")\n", "\n", "    # 使用 CSS 选择器查找图像元素\n", "    image_elements = soup.select(\"a.iusc\")\n", "\n", "    # 提取图像链接\n", "    image_links = []\n", "    for img in image_elements:\n", "        m_data = img.get(\"m\")\n", "        if m_data:\n", "            m_json = eval(m_data)\n", "            if \"murl\" in m_json:\n", "                image_links.append(m_json[\"murl\"])\n", "        if len(image_links) >= num_images:\n", "            break\n", "\n", "    # 获取图像分辨率并存储为字典\n", "    image_data = []\n", "    for link in image_links:\n", "        try:\n", "            # 下载图像\n", "            img_data = requests.get(link, headers=headers)\n", "            img = Image.open(BytesIO(img_data.content))\n", "            # 将信息存储为字典\n", "            image_info = {\n", "                \"slide_title\": slide_title,\n", "                \"query\": query,\n", "                \"width\": img.width,\n", "                \"height\": img.height,\n", "                \"resolution\": img.width * img.height,  # 宽度 x 高度\n", "                \"obj\": img,\n", "            }\n", "            image_data.append(image_info)\n", "        except Exception as e:\n", "            print(f\"Could not retrieve image resolution for {link}: {e}\")\n", "\n", "    # 按分辨率从大到小排序\n", "    sorted_images = sorted(image_data, key=lambda x: x[\"resolution\"], reverse=True)\n", "\n", "    return sorted_images\n"]}, {"cell_type": "code", "execution_count": 72, "id": "3a6f522e-bb0c-4f7c-a6dc-180e8f73c8dd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["第一个键是: 纳斯达克概述\n", "对应的值是: 股票市场电子交易\n"]}], "source": ["# 获取字典中第一个键值对\n", "first_slide = next(iter(keywords))\n", "first_query = keywords[first_slide]\n", "\n", "print(f\"第一个键是: {first_slide}\")\n", "print(f\"对应的值是: {first_query}\")"]}, {"cell_type": "code", "execution_count": 54, "id": "003ba006-dbff-4aa5-95ee-2a16dd021b29", "metadata": {}, "outputs": [], "source": ["os.environ[\"LANGCHAIN_TRACING_V2\"] = \"true\"\n", "os.environ[\"LANGCHAIN_PROJECT\"] = \"ChatPPT Jupyter\""]}, {"cell_type": "code", "execution_count": 73, "id": "7821a7e1-b9e7-414c-bd54-a1c0c21f6dc9", "metadata": {}, "outputs": [], "source": ["# 搜索关键词\n", "images = get_bing_images(first_slide, first_query)"]}, {"cell_type": "markdown", "id": "1cbe57b4-2b0f-45d8-b478-4b508474b5f1", "metadata": {}, "source": ["### 4. 保存检索的图像，并查看图像文件"]}, {"cell_type": "code", "execution_count": 74, "id": "2065caaa-aa92-41d6-bcaa-a0e50733f5de", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Name: 纳斯达克概述, Query: 股票市场电子交易 分辨率：1920x1080\n", "Name: 纳斯达克概述, Query: 股票市场电子交易 分辨率：860x574\n", "Name: 纳斯达克概述, Query: 股票市场电子交易 分辨率：860x572\n", "Name: 纳斯达克概述, Query: 股票市场电子交易 分辨率：860x535\n", "Name: 纳斯达克概述, Query: 股票市场电子交易 分辨率：601x406\n"]}], "source": ["# 检查并输出图像链接和分辨率\n", "if images:\n", "    # 检查并输出图像数据\n", "    for image in images:\n", "        print(f\"Name: {image['slide_title']}, Query: {image['query']} 分辨率：{image['width']}x{image['height']}\")\n", "else:\n", "    print(\"No images found.\")"]}, {"cell_type": "code", "execution_count": 57, "id": "70d0f78d-a19c-4eeb-bda4-eec405ca7cd4", "metadata": {}, "outputs": [], "source": ["from PIL import Image\n", "import os\n", "\n", "def save_image(img, save_path, format=\"JPEG\", quality=85, max_size=1080):\n", "    try:\n", "        # 调整分辨率，保持长宽比\n", "        width, height = img.size\n", "        if max(width, height) > max_size:\n", "            scaling_factor = max_size / max(width, height)\n", "            new_width = int(width * scaling_factor)\n", "            new_height = int(height * scaling_factor)\n", "            img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)\n", "        \n", "        # 设置保存选项\n", "        save_options = {\n", "            \"quality\": quality,        # 设置图像质量\n", "            \"optimize\": True           # 启用优化以减小文件大小\n", "        }\n", "        \n", "        # 根据格式设置特定参数\n", "        if format == \"JPEG\":\n", "            save_options[\"progressive\"] = True  # 使用渐进式加载\n", "        elif format == \"PNG\":\n", "            # PNG 格式不支持 quality 参数，可选择使用压缩选项\n", "            save_options.pop(\"quality\", None)\n", "\n", "        # 保存图像\n", "        img.save(save_path, format=format, **save_options)\n", "        print(f\"Image saved as {save_path} in {format} format with quality {quality}.\")\n", "    except Exception as e:\n", "        print(f\"Failed to save image: {e}\")\n"]}, {"cell_type": "code", "execution_count": 60, "id": "19844e1e-d871-4ba5-a327-b0eeda83f22e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Image saved as test/纳斯达克概述_1.jpeg in JPEG format with quality 85.\n", "Image saved as test/纳斯达克概述_2.jpeg in JPEG format with quality 85.\n", "Image saved as test/纳斯达克概述_3.jpeg in JPEG format with quality 85.\n", "Image saved as test/纳斯达克概述_4.jpeg in JPEG format with quality 85.\n", "Image saved as test/纳斯达克概述_5.jpeg in JPEG format with quality 85.\n"]}], "source": ["# 保存图像到本地\n", "save_directory = \"test\"\n", "os.makedirs(save_directory, exist_ok=True)\n", "\n", "for idx, img in enumerate(images, start=1):\n", "    save_path = os.path.join(save_directory, f\"{img['slide_title']}_{idx}.jpeg\")\n", "    \n", "    # 默认保存为 JPEG 格式\n", "    save_image(img[\"obj\"], save_path)\n", "\n", "    img[\"filepath\"] = save_path\n", "    \n", "    # 如需保存为 PNG 格式，可以指定 format 参数\n", "    # save_path_png = os.path.join(save_directory, f\"image_{idx}.png\")\n", "    # save_image(img, save_path_png, format=\"PNG\")\n"]}, {"cell_type": "code", "execution_count": 65, "id": "1cc27318-fb73-4546-aa80-1eb8725a6560", "metadata": {}, "outputs": [{"data": {"image/jpeg": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "\n", "# 查看分辨率最高的图像\n", "display(Image(filename=images[0][\"filepath\"]))"]}, {"cell_type": "markdown", "id": "57d0953c-d23c-4207-8636-875721dc1d92", "metadata": {}, "source": ["### 5. 生成 slide-image 字典，并正确嵌入到原始 PowerPoint 内容中"]}, {"cell_type": "code", "execution_count": 66, "id": "ec8fc586-bb08-4e61-91e5-a7c064195cb5", "metadata": {}, "outputs": [], "source": ["image_pair = {}"]}, {"cell_type": "code", "execution_count": 75, "id": "6897e875-393b-4bc4-b8c9-bdddc3e63fd1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["纳斯达克概述 股票市场电子交易\n", "Name: 纳斯达克概述, Query: 股票市场电子交易 分辨率：1920x1080\n", "Name: 纳斯达克概述, Query: 股票市场电子交易 分辨率：860x572\n", "Name: 纳斯达克概述, Query: 股票市场电子交易 分辨率：860x535\n", "Image saved as test/纳斯达克概述_1.jpeg in JPEG format with quality 85.\n", "纳斯达克的市场结构 高科技公司\n", "Name: 纳斯达克的市场结构, Query: 高科技公司 分辨率：2048x1536\n", "Name: 纳斯达克的市场结构, Query: 高科技公司 分辨率：1024x681\n", "Name: 纳斯达克的市场结构, Query: 高科技公司 分辨率：799x529\n", "Image saved as test/纳斯达克的市场结构_1.jpeg in JPEG format with quality 85.\n", "纳斯达克的未来展望 科技创新与投资机会\n", "Name: 纳斯达克的未来展望, Query: 科技创新与投资机会 分辨率：820x1957\n", "Name: 纳斯达克的未来展望, Query: 科技创新与投资机会 分辨率：750x1125\n", "Name: 纳斯达克的未来展望, Query: 科技创新与投资机会 分辨率：700x1080\n", "Image saved as test/纳斯达克的未来展望_1.jpeg in JPEG format with quality 85.\n"]}], "source": ["for slide_title, query in keywords.items():\n", "    print(slide_title, query)\n", "    # 检索 3张 图像\n", "    images = get_bing_images(slide_title, query, 3)\n", "    if images:\n", "        # 检查并输出图像数据\n", "        for image in images:\n", "            print(f\"Name: {image['slide_title']}, Query: {image['query']} 分辨率：{image['width']}x{image['height']}\")\n", "    else:\n", "        print(f\"No images found for {slide_title}.\")\n", "        continue\n", "\n", "    # 仅处理分辨率最高的图像\n", "    img = images[0]\n", "    \n", "    # 保存图像到本地\n", "    save_directory = \"test\"\n", "    os.makedirs(save_directory, exist_ok=True)\n", "    save_path = os.path.join(save_directory, f\"{img['slide_title']}_1.jpeg\")\n", "    # 默认保存为 JPEG 格式\n", "    save_image(img[\"obj\"], save_path)\n", "\n", "    # 新增slide-image 到 image_pair\n", "    image_pair[img[\"slide_title\"]] = save_path"]}, {"cell_type": "code", "execution_count": 76, "id": "a3755a73-bd94-4a22-9ebe-0ff9954d37f5", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'纳斯达克概述': 'test/纳斯达克概述_1.jpeg',\n", " '纳斯达克的市场结构': 'test/纳斯达克的市场结构_1.jpeg',\n", " '纳斯达克的未来展望': 'test/纳斯达克的未来展望_1.jpeg'}"]}, "execution_count": 76, "metadata": {}, "output_type": "execute_result"}], "source": ["image_pair"]}, {"cell_type": "code", "execution_count": 77, "id": "2822cb31-9807-4835-a956-4dc220c8b4f8", "metadata": {}, "outputs": [], "source": ["def insert_images(markdown_content, image_pair):\n", "    # 将原始 Markdown 内容按行分割\n", "    lines = markdown_content.split('\\n')\n", "    new_lines = []\n", "    i = 0\n", "    while i < len(lines):\n", "        line = lines[i]\n", "        new_lines.append(line)\n", "        # 检查是否为幻灯片标题行（以 '## ' 开头）\n", "        if line.startswith('## '):\n", "            # 提取幻灯片标题\n", "            slide_title = line[3:].strip()\n", "            # 如果幻灯片标题在 image_pair 中，插入对应的图像\n", "            if slide_title in image_pair:\n", "                image_path = image_pair[slide_title]\n", "                # 按照 Markdown 图像格式插入\n", "                image_markdown = f'![{slide_title}]({image_path})'\n", "                new_lines.append(image_markdown)\n", "        i += 1\n", "    # 将修改后的内容重新组合为字符串\n", "    new_content = '\\n'.join(new_lines)\n", "    return new_content"]}, {"cell_type": "code", "execution_count": 81, "id": "f08eecca-0943-4143-baab-57bf0db54694", "metadata": {}, "outputs": [], "source": ["new_content = insert_images(slides_content, image_pair)"]}, {"cell_type": "code", "execution_count": 83, "id": "cec58fb0-a3b4-4670-ba4f-bb60382b80f5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["# 纳斯达克简介\n", "\n", "## 纳斯达克概述\n", "![纳斯达克概述](test/纳斯达克概述_1.jpeg)\n", "- **什么是纳斯达克**: 纳斯达克是美国的一个股票交易市场。\n", "  - 纳斯达克全称为“全国证券交易商自动报价系统”，成立于1971年，是全球第一个电子股票市场。\n", "  - 它主要以科技公司为主，吸引了许多创新型企业上市。\n", "- **纳斯达克的功能**: 提供股票交易和信息服务。\n", "  - 交易者可以通过电子系统进行股票买卖，提升了交易效率。\n", "  - 纳斯达克还提供实时的市场数据和分析工具，帮助投资者做出决策。\n", "\n", "## 纳斯达克的市场结构\n", "![纳斯达克的市场结构](test/纳斯达克的市场结构_1.jpeg)\n", "- **市场类型**: 纳斯达克是一个场外交易市场（OTC）。\n", "  - 与传统的交易所不同，纳斯达克没有物理交易场所，所有交易通过电子系统进行。\n", "  - 这种结构使得纳斯达克能够提供更灵活的交易方式。\n", "- **上市公司**: 纳斯达克上市公司主要集中在高科技和生物科技领域。\n", "  - 例如，知名公司如苹果、亚马逊和谷歌母公司Alphabet均在纳斯达克上市。\n", "  - 这些公司通常具有较高的市场增长潜力，吸引了大量投资者。\n", "\n", "## 纳斯达克的指数\n", "- **纳斯达克综合指数**: 反映纳斯达克市场整体表现的主要指数。\n", "  - 该指数包含了纳斯达克上市的所有股票，广泛用于衡量市场的健康状况。\n", "  - 投资者可以通过观察该指数的变动来判断市场趋势。\n", "- **纳斯达克100指数**: 包含100家最大的非金融公司。\n", "  - 该指数侧重于大型科技公司，常被视为科技行业的风向标。\n", "  - 投资者可以通过该指数了解科技股的整体表现。\n", "\n", "## 纳斯达克的交易机制\n", "- **电子交易系统**: 纳斯达克的交易完全依赖电子系统。\n", "  - 交易者通过计算机进行订单的输入和执行，减少了人为错误。\n", "  - 该系统支持高频交易，能够快速响应市场变化。\n", "- **做市商制度**: 纳斯达克采用做市商制度来提供流动性。\n", "  - 做市商是为特定股票提供买卖报价的公司，确保市场有足够的流动性。\n", "  - 这种机制帮助投资者在需要时能够迅速买入或卖出股票。\n", "\n", "## 纳斯达克的投资机会\n", "- **高增长潜力**: 纳斯达克上市公司大多具有较高的增长潜力。\n", "  - 科技行业的快速发展使得许多公司在短时间内实现了巨额利润。\n", "  - 投资者可以通过选择优质科技股获得良好的投资回报。\n", "- **多样化投资**: 投资者可以通过纳斯达克实现投资组合的多样化。\n", "  - 纳斯达克包含多个行业的公司，投资者可以根据自己的风险偏好选择不同的股票。\n", "  - 例如，可以投资于生物科技、云计算、人工智能等领域的公司。\n", "\n", "## 纳斯达克的风险因素\n", "- **市场波动性**: 纳斯达克股票市场波动性较大。\n", "  - 科技股受到市场情绪和经济数据的影响，价格波动频繁。\n", "  - 投资者需要具备承受市场波动的心理准备。\n", "- **行业集中风险**: 纳斯达克以科技股为主，行业集中度较高。\n", "  - 如果科技行业出现整体下滑，可能会对纳斯达克产生重大影响。\n", "  - 投资者应关注行业动态，避免过于集中于某一行业。\n", "\n", "## 如何投资纳斯达克\n", "- **选择合适的券商**: 投资者需要选择合适的证券公司进行交易。\n", "  - 许多在线券商提供纳斯达克股票交易，投资者可以根据费用和服务选择。\n", "  - 一些券商还提供模拟交易功能，帮助新手熟悉交易流程。\n", "- **研究与分析**: 投资者应进行充分的市场研究与分析。\n", "  - 关注公司财务报告、行业趋势和市场新闻，做出明智的投资决策。\n", "  - 使用技术分析工具，帮助判断买卖时机。\n", "\n", "## 纳斯达克的未来展望\n", "![纳斯达克的未来展望](test/纳斯达克的未来展望_1.jpeg)\n", "- **科技创新的推动**: 随着科技的不断发展，纳斯达克将继续吸引新兴企业上市。\n", "  - 人工智能、区块链和生物科技等领域的创新将为市场带来新的投资机会。\n", "  - 投资者可以关注这些新兴领域的公司，寻找潜在的投资标的。\n", "- **全球化趋势**: 纳斯达克在全球范围内的影响力不断增强。\n", "  - 越来越多的外国公司选择在纳斯达克上市，扩大了投资者的选择范围。\n", "  - 投资者可以通过纳斯达克接触到全球市场的机会。\n", "\n", "## 总结与建议\n", "- **投资前要做好功课**: 对纳斯达克及其上市公司进行深入了解。\n", "  - 理解市场机制和行业动态，有助于做出明智的投资决策。\n", "- **保持理性与耐心**: 投资股市需保持理性，避免情绪化决策。\n", "  - 设定合理的投资目标和风险承受能力，制定适合自己的投资策略。\n"]}], "source": ["print(new_content)"]}, {"cell_type": "code", "execution_count": null, "id": "3951353b-5cfb-42c8-b76a-945e2ac32e8d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 5}