# Introducing Canvas

## What is <PERSON><PERSON>?
- A new interface for working with ChatGPT on writing and coding projects
- Opens in a separate window, allowing collaboration on a project

## Better Collaboration with ChatGPT
- Limited by chat interface when working on editing and revisions
- Canvas offers a new interface for this kind of work

## How Does Canvas Work?
- You control the project, directly editing text or code
- Menu of shortcuts to ask ChatGPT to adjust writing length, debug code, etc.
- Can restore previous versions of your work using the back button in canvas

## Writing Shortcuts in Canvas
- Suggest edits: Inline suggestions and feedback
- Adjust the length: Edits document length to be shorter or longer
- Change reading level: Adjusts reading level from Kindergarten to Graduate School
- Add final polish: Checks for grammar, clarity, and consistency
- Add emojis: Adds relevant emojis for emphasis and color

## Coding in Canvas
- Easier to track and understand ChatGPT's changes
- Planned improvements to transparency into these kinds of edits
- Coding shortcuts:
  - Review code: Inline suggestions to improve your code
  - Add logs: Inserts print statements to help you debug and understand your code
  - Add comments: Adds comments to the code to make it easier to understand
  - Fix bugs: Detects and rewrites problematic code to resolve errors
  - Port to a language: Translates your code into JavaScript, TypeScript, Python, Java, C++, or PHP

## Training the Model for Collaboration
- Trained GPT-4o to collaborate as a creative partner
- Understands broader context to provide precise feedback and suggestions

## Core Behaviors of the Model
- Triggering the canvas for writing and coding
- Generating diverse content types
- Making targeted edits
- Rewriting documents
- Providing inline critique

## Improving Canvas Decision Boundary
- Improved correctly triggering the canvas decision boundary to 83% and 94% respectively for writing and coding tasks

## Canvas Edits Boundary - Writing & Coding
- GPT-4o with canvas performs better than a baseline prompted GPT-4o by 18%

## Training the Model to Generate High-Quality Comments
- Used human evaluations to assess comment quality and accuracy
- Outperforms zero-shot GPT-4o with prompted instructions by 30% in accuracy and 16% in quality

## What's Next?
- Rethinking how we interact with AI requires updates like Canvas
- Canvas is in early beta, and rapid improvements are planned