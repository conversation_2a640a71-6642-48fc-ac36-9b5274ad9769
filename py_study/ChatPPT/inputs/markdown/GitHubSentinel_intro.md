# GitHub Sentinel 概述

## 什么是 GitHub Sentinel?
- 专为大模型（LLMs）时代打造的智能信息检索和高价值内容挖掘 AI Agent
- 目标用户：开源爱好者、个人开发者和投资人等

## 主要功能
- **订阅管理**: 轻松管理和跟踪关注的 GitHub 仓库
- **更新检索**: 自动汇总订阅仓库的最新动态

## 通知系统与报告生成
- **通知系统**: 通过电子邮件等方式，实时通知项目最新进展
- **报告生成**: 生成详细的项目进展报告，支持多种格式和模板

## 多模型支持
- 结合 OpenAI 和 Ollama 模型
- 生成自然语言项目报告，提供智能、精准的信息服务

## 定时任务与图形化界面
- **定时任务**: 支持守护进程方式执行定时任务，确保信息及时获取
- **图形化界面**: 基于 Gradio 实现易用的 GUI，降低使用门槛

## 容器化与持续集成
- **容器化**: 支持 Docker 构建和容器化部署，便于不同环境下快速部署
- **持续集成**: 完备的单元测试，支持生产级 CI/CD 流程，确保项目稳定性

## 扩展能力
- 自动跟踪和分析 GitHub 开源项目动态
- 可扩展到其他信息渠道，如 Hacker News 的热门话题