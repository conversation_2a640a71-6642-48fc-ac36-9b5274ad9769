### **ChatPPT 输入文本格式说明**

此文档介绍如何编写输入文本以生成 PowerPoint 幻灯片。每个幻灯片包含标题、要点、图片等内容。输入文本将被解析并自动生成对应的 PowerPoint 文件，并且布局将由 `ChatPPT` 的 `LayoutManager` 统一管理，不需要手动输入布局名称。

`LayoutManager` 根据输入内容（如标题、要点、图片）自动分配合适的布局，具体布局设置通过 `config.json` 文件配置，并确保模板 (`template.pptx`) 中的母版名称与配置一致。

### **ChatPPT 输入文本格式**

```plaintext
# [主标题]

## [幻灯片标题]
- [要点内容1]
- [要点内容2]

## [幻灯片标题]
- [要点内容1]
![图片描述](图片路径)
```

#### **1. 主标题**
   - 格式：`# [主标题]`
   - 说明：主标题作为整个 PowerPoint 的标题，同时也将作为生成的 PowerPoint 文件名。
   - 示例：
     ```
     # 企业年度报告
     ```

#### **2. 幻灯片标题**
   - 格式：`## [幻灯片标题]`
   - 说明：每张幻灯片以 `##` 开头，后面跟随标题。布局将根据 `LayoutManager` 自动分配，无需手动指定布局名称。
   - 示例：
     ```
     ## 2024 业绩概述
     ```

#### **3. 幻灯片内容 - 要点列表**
   - 格式：`- [要点内容]`
   - 说明：每个要点以 `-` 开头，后跟要点的内容。该格式用于生成幻灯片中的项目符号列表。
   - 示例：
     ```
     - 总收入增长15%
     - 市场份额扩大至30%
     ```

#### **4. 幻灯片内容 - 图片**
   - 格式：`![图片描述](图片路径)`
   - 说明：使用 `![图片描述](图片路径)` 的格式插入图片。图片路径应该为相对路径或绝对路径，确保文件存在于指定路径下。
   - 示例：
     ```
     ![业绩图表](images/performance_chart.png)
     ```

### **完整输入文本示例**

以下是一个完整的输入文本示例，包含主标题、多个幻灯片、要点列表以及图片插入：

```plaintext
# 企业年度报告

## 2024 业绩概述
- 总收入增长15%
- 市场份额扩大至30%

## 新产品发布
- 产品A: 特色功能介绍
- 产品B: 市场定位

## 业绩图表
![业绩图表](images/performance_chart.png)
```

### **各部分说明**

1. **主标题**：
   - `# 企业年度报告`：该文本将用作生成的 PowerPoint 文件名，即 "企业年度报告.pptx"。

2. **幻灯片 1：2024 业绩概述**：
   - 标题：`2024 业绩概述`，布局将由 `LayoutManager` 自动分配。
   - 内容：包括两条要点，分别是 "总收入增长15%" 和 "市场份额扩大至30%"。

3. **幻灯片 2：新产品发布**：
   - 标题：`新产品发布`，布局将由 `LayoutManager` 自动分配。
   - 内容：包括两条要点，分别是 "产品A: 特色功能介绍" 和 "产品B: 市场定位"。

4. **幻灯片 3：业绩图表**：
   - 标题：`业绩图表`，布局将由 `LayoutManager` 自动分配。
   - 图片：插入路径为 `images/performance_chart.png` 的图片。

### **自动布局说明**

布局的分配不再需要在输入文本中手动指定，`ChatPPT` 的 `LayoutManager` 根据幻灯片的内容（如标题、要点、图片等）自动选择最合适的布局。布局设置在 `config.json` 中管理，模板 (`template.pptx`) 的布局名称必须与 `config.json` 中的名称匹配。

#### **布局规则示例**：

- 当幻灯片包含标题和多个要点时，自动使用 "Title and Content" 布局。
- 当幻灯片包含标题和图片时，自动使用 "Title and Picture" 布局。
- 当幻灯片同时包含标题、要点和图片时，自动使用 "Title, Content, and Picture" 布局。

### **配置布局**

所有布局映射在 `config.json` 中定义，确保模板文件 `template.pptx` 中的母版布局名称与 `config.json` 中的名称一致。例如：

```json
{
    "layout_mapping": {
        "Title Only": 1,
        "Title and Content": 2,
        "Title and Picture": 3,
        "Title, Content, and Picture": 4
    }
}
```

### **注意事项**

1. **布局映射**：`config.json` 中的 `layout_mapping` 字段定义了内容到布局的映射。布局名称必须与模板中的母版布局名称相对应。
   
2. **图片路径**：图片路径应为本地文件系统的相对路径或绝对路径。确保图片文件存在于指定位置。

3. **占位符**：确保模板中的布局包含文本和图片占位符，以便自动插入要点和图片。

### **总结**

本指南提供了 `ChatPPT` 输入文本的标准格式说明。您只需提供主标题、幻灯片标题、要点和图片路径，无需指定布局名称，`LayoutManager` 将根据内容自动为您选择最合适的布局。