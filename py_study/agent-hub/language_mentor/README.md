## LanguageMentor

LanguageMentor 是一款基于 LLaMA 3.1 或 GPT-4o-mini 的在线英语私教系统，提供英语对话练习和场景化学习训练。用户可以选择不同的场景，或直接与对话代理人进行自由对话，模拟真实生活中的英语交流场景，提升语言能力。

### 版本历史

| 版本号  | 主要特性                                                                                                   | 发布链接                                        |
|--------|---------------------------------------------------------------------------------------------------------|-----------------------------------------------|
| v0.1   | - **用户界面**：使用 Gradio 作为用户界面，分 tab 实现了*对话训练*和*场景体验*的UI布局。<br>- **对话训练功能**：通过 Ollama 托管的 `LLaMA 3.1 8B instruct 量化模型`驱动 ChatBot，使用 LangChain Runnables 模块记录对话历史，实现对话训练功能，。 | [访问 v0.1](https://github.com/DjangoPeng/LanguageMentor/tree/v0.1)   |
| v0.2   | - **新增模拟对话场景：酒店入住（Hotel Checkin）**：实现了完整的酒店多轮（最多20轮）对话，帮助学习者完成酒店入住流程并询问酒店设施。<br>- **新增模拟对话场景：求职面试（Job Interview）**：支持学习者练习自我介绍和回答常见面试问题。面试场景的对话效果还在持续优化中。 | [访问 v0.2](https://github.com/DjangoPeng/LanguageMentor/tree/v0.2)   |
| v0.3   | - **场景代理重构**：改进了场景代理的架构，提高了响应机制的稳定性和效率。<br>- **ChatBot 引导消息**：新增了每个场景开始时 ChatBot 的欢迎引导消息。<br>- **支持跨场景聊天历史**：为同一用户在不同场景间保持聊天历史，增强了练习的连贯性。 | [访问 v0.3](https://github.com/DjangoPeng/LanguageMentor/tree/v0.3)   |
| v0.4   | - **新增背单词（Vocabulary Study）功能**：基于对话学习新单词组的含义和用法，支持不断闯关。<br>- **重构 Gradio Tab 代码模块**：将不同 Tab 独立 Python 文件维护。<br>- **定义 Agent 基类**：使用 Python ABC 定义 AgentBase Class，所有的 Agent 基于此类扩展，便于 Agent 模块快速扩展。 | [访问 v0.4](https://github.com/DjangoPeng/LanguageMentor/tree/v0.4)   |
