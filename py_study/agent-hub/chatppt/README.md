# ChatPPT

ChatPPT 是一个基于多模态 AI 技术的智能助手，旨在提升企业办公自动化流程的效率。它能够处理语音、图像和文本等多种输入形式，通过精确的提示工程和强大的自然语言处理能力，为用户生成高质量的 PowerPoint 演示文稿。ChatPPT 不仅简化了信息收集和内容创作过程，还通过自动化的报告生成和分析功能，帮助企业快速、准确地完成各类汇报和展示任务，从而显著提升工作效率和业务价值。

### 主要功能

- **多模态输入支持**：支持语音、图像、文本等多种输入形式，灵活适应用户的使用需求。
- **自动生成演示文稿**：基于输入内容，自动生成结构化的 PowerPoint 演示文稿，支持多种布局和模板。
- **语音识别和文本转换**：自动将语音输入转化为文本，进行内容处理和文稿生成，降低用户的操作成本。
- **图像处理与嵌入**：支持将用户上传的图片自动嵌入演示文稿中，并根据内容智能选择合适的布局。
- **多语言支持**：结合 OpenAI 模型和其他语言模型，支持中英文等多语言的演示文稿生成和报告输出。
- **可视化界面**：通过 Gradio 实现简洁易用的图形化界面，让用户无需复杂配置即可快速生成演示文稿。

### 版本历史

| 版本号  | 主要特性                                                                                                           | 发布链接                                        |
|--------|---------------------------------------------------------------------------------------------------------|-----------------------------------------------|
| v0.1   | - **PowerPoint 内容解析**：引入了强大的输入解析系统，将结构化文本转化为 PowerPoint 幻灯片，包括标题、要点和图片。该系统使用高级正则表达式，将用户输入映射到相应的幻灯片布局。<br>- **基于模板的 PowerPoint 生成**：实现了基于主模板（`MasterTemplate.pptx`）的动态 PowerPoint 幻灯片生成，用户可以为不同的幻灯片定义不同的布局，如“标题与内容”和“标题与两列”。<br>- **图片与要点支持**：增加了图片和要点的自动插入功能，确保图片插入到指定的占位符中，并根据内容布局调整要点。<br>- **用户输入转换**：开发了将自然语言描述的幻灯片内容转换为标准输入格式的函数，用户可使用类似“Slide X: Title, Key Points, Notes”的格式进行描述。<br>- **自动文件命名**：根据输入文本的主标题（如“企业年度报告”）自动使用该标题作为生成 PowerPoint 文件的文件名。 | [访问 v0.1](https://github.com/DjangoPeng/ChatPPT/tree/v0.1)   |
| v0.2   | - **自动布局管理**：引入了 `LayoutManager`，根据幻灯片内容自动分配合适的布局，简化了输入文本格式，无需手动指定布局。<br>- **布局映射配置**：通过 `config.json` 进行布局映射配置管理，确保布局映射的灵活性，并与 `template.pptx` 模板文件中的布局保持一致。<br>- **简化输入格式**：输入格式去除了对布局名称的需求，用户只需提供标题、要点和图片，布局将由 `LayoutManager` 自动分配。<br>- **增强的 PowerPoint 解析**：使用 `SlideBuilder` 类封装了幻灯片生成与布局分配的逻辑，提高了代码的模块化与可维护性。<br>- **配置加载器集成**：新增 `Config` 类，自动从 `config.json` 文件加载系统配置，包括输入模式、PPT 模板和布局映射。 | [访问 v0.2](https://github.com/DjangoPeng/ChatPPT/tree/v0.2)   |
| v0.3   | - **基于内容类型权重的布局编码**：通过为不同的内容类型（如 `Title`、`Content`、`Picture`）分配权重，简化了布局匹配，确保顺序无关的内容类型组合被正确处理。<br>- **Gradio 和 ChatBot 集成**：引入了基于 Gradio 的界面，集成了由 GPT-4o-mini 驱动的 ChatBot，用户可以通过 ChatBot 生成 Markdown 内容，并将其无缝集成到 PowerPoint 生成中。<br>- **PowerPoint 生成集成**：将 Gradio 与 PowerPoint 生成系统无缝结合，用户可以通过 Gradio 提交内容，并自动生成 PowerPoint 幻灯片。<br>- **动态布局策略生成**：基于内容类型动态生成布局策略，进一步简化了配置，并提升了布局扩展的灵活性。<br>- **调试和日志改进**：增加了调试功能，包括为 `LayoutManager` 等类添加 `__str__` 方法，提供有关布局策略和编码的详细调试信息。 | [访问 v0.3](https://github.com/DjangoPeng/ChatPPT/tree/v0.3)   |
| v0.4   | - **集成 Whisper Large v3（ASR）**：引入 Whisper Large v3 自动语音识别（ASR）模型，显著提升了语音转文本的精度，支持用户通过语音直接生成结构化内容。<br>- **分层内容解析**：实现多级缩进的要点解析，支持根据缩进级别识别多层级的要点，保留文本结构，并提升内容在幻灯片中的组织性。<br>- **极简风格幻灯片母版**：引入极简风格的幻灯片母版，优化了多层级内容的呈现效果，符合极简主义设计的最佳实践，增强可读性和视觉效果。<br>- **支持缩进的要点层级解析**：增强了要点解析功能，可根据缩进级别识别多层次要点结构，确保内容层级在幻灯片中的准确展现。<br>- **优化布局一致性**：增强了系统对多层级结构数据的兼容性，使幻灯片布局在保持一致性的同时支持复杂结构和多级缩进内容。 | [访问 v0.4](https://github.com/DjangoPeng/ChatPPT/tree/v0.4)   |
| v0.5   | - **集成多模态模型 MiniCPM-v-2.6**：新增对 MiniCPM-v-2.6 模型的集成，支持识别和解释用户上传的 `.png`、`.jpg` 和 `.jpeg` 图像文件。<br>- **支持从 Word 文件一键生成 PowerPoint 文件**：实现了从 Word 文件内容直接生成 PowerPoint 的功能，包括：<br>  - **Docx 解析模块（docx_parser）**：将 Word 文档内容解析为 Markdown 格式的 `raw_content`；<br>  - **Content Formatter（Agent）**：由 GPT-4-mini 驱动，在不丢失内容的情况下，将 `raw_content` 转换为适配 ChatPPT 的 Markdown 格式；<br>  - **Content Assistant（Agent）**：在 `markdown_content` 的基础上，进一步扩展和完善内容，生成最终的 PowerPoint 内容。<br>- **图像自适应支持**：生成 PPT 文件时，支持图像尺寸自适应，确保插入到 `ImagePlaceholder` 时图像居中对齐并匹配尺寸。 | [访问 v0.5](https://github.com/DjangoPeng/ChatPPT/tree/v0.5) |
| v0.6   | - **引入 ImageAdvisor 类用于 Bing 图像检索和嵌入**：实现了自动关键词提取与图像检索功能，并增加了重试逻辑和 RGBA 图像处理。<br>- **集成 Jupyter Notebook 演示**：新增 Jupyter Notebook 展示 `ImageAdvisor` 功能，增强用户对图像检索与嵌入的理解。<br>- **增强的 save_image 函数**：更新 `save_image` 函数以灵活处理多种图像格式，自动转换 RGBA 图像为 PNG 格式。 | [访问 v0.6](https://github.com/DjangoPeng/ChatPPT/tree/v0.6)   |
| v0.7   | - **全面的单元测试覆盖**：为多个模块添加单元测试，确保代码的可靠性与可维护性，并提供详细的测试文档。<br>- **Docker 集成**：引入 Docker 支持，实现自动化单元测试验证，并通过环境变量安全配置 API 密钥，简化部署过程。<br>- **更新 README 文档**：增强 `README.md` 文件，包含 Docker 使用说明和测试指引。 | [访问 v0.7](https://github.com/DjangoPeng/ChatPPT/tree/v0.7)   |