from typing import Dict, Any, List
import requests
import time
import logging
from urllib.parse import urlparse

# --- 配置日志 ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


def get_proxies(page: int = 1, per_page: int = 20) -> List[Dict[str, Any]]:
    """
    从 API 获取代理列表。
    :param page: 页码
    :param per_page: 每页数量
    :return: 代理字典列表
    """
    api_url = f'https://proxy.scdn.io/api/proxy_list.php?page={page}&per_page={per_page}'
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    try:
        response = requests.get(api_url, headers=headers, timeout=10)
        response.raise_for_status()
        return response.json().get("proxies", [])
    except requests.exceptions.RequestException as e:
        logging.error(f"获取代理列表失败: {e}")
        return []
    except ValueError:  # JSONDecodeError
        logging.error("解析代理列表JSON失败")
        return []


def is_proxy_valid(proxy: Dict[str, Any], test_url: str = "http://httpbin.org/ip", timeout: int = 5) -> bool:
    """
    检测代理服务器是否可用。
    :param proxy: 代理信息字典 (包含 'ip', 'port', 'type')
    :param test_url: 测试URL (默认使用 httpbin.org/ip)
    :param timeout: 超时时间(秒)
    :return: True 如果代理可用，否则 False
    """
    proxy_type = proxy.get('type', '').lower()
    if proxy_type not in ['http', 'https']:
        logging.debug(f"跳过不支持的代理类型: {proxy_type.upper()}")
        return False

    proxy_url = f"{proxy_type}://{proxy['ip']}:{proxy['port']}"
    proxies = {
        'http': proxy_url,
        'https': proxy_url,
    }
    proxy_ip = proxy['ip']

    try:
        start_time = time.time()
        response = requests.get(
            test_url,
            proxies=proxies,
            timeout=timeout,
            verify=False  # 公共代理经常需要禁用SSL验证
        )
        response_time = time.time() - start_time
        response.raise_for_status()

        try:
            returned_ip = response.json().get('origin')
            if returned_ip == proxy_ip:
                logging.info(f"✅ 代理可用 | 响应时间: {response_time:.2f}s | 代理: {proxy_url}")
                return True
            else:
                logging.warning(f"⚠️ 代理工作不正常 | 代理IP: {proxy_ip} | 返回IP: {returned_ip}")
                return False
        except ValueError:
            logging.error(f"❌ 无法解析响应JSON: {response.text}")
            return False

    except requests.exceptions.ProxyError:
        logging.error(f"❌ 代理连接失败: {proxy_url}")
    except requests.exceptions.ConnectTimeout:
        logging.error(f"❌ 代理连接超时: {proxy_url}")
    except requests.exceptions.SSLError:
        logging.error(f"❌ SSL证书错误: {proxy_url}")
    except requests.exceptions.RequestException as e:
        logging.error(f"❌ 请求异常: {type(e).__name__} - {proxy_url}")

    return False


def main():
    """主函数"""
    logging.info("开始获取和检测代理...")
    proxies_to_check = get_proxies(per_page=20)

    if not proxies_to_check:
        logging.warning("未能获取到代理列表，程序退出。")
        return

    valid_proxies = []
    for proxy in proxies_to_check:
        if is_proxy_valid(proxy):
            valid_proxies.append(proxy)

    logging.info(f"检测完成。发现 {len(valid_proxies)} 个可用代理。")
    if valid_proxies:
        logging.info("可用代理详情:")
        for p in valid_proxies:
            logging.info(f"  - {p.get('type')}://{p.get('ip')}:{p.get('port')} ({p.get('country')}, {p.get('city')})")


if __name__ == "__main__":
    main()
