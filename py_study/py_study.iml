<?xml version="1.0" encoding="UTF-8"?>
<module type="PYTHON_MODULE" version="4">
  <component name="NewModuleRootManager" inherit-compiler-output="true">
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/openai-quickstart/langchain/openai-translator/ai_translator" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/GitHubSentinel/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/GitHubSentinel/tests" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/LanguageMentor/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/ChatPPT/src" isTestSource="false" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>