from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime, timedelta

import pandas as pd
import requests

AGENT_ID = "1000039"
DOC_ID = "dctm4CpKzyqpIePl4Ocar-D8lzPT9xmyfKtwjw0cvUJHvS0j4RGiuOscuCiRm9Q4IbRB885jrxo1dksD9FdhIT7w"
SHEET_ID = "q979lj"
# 需要输出的人员
PERSONS = []

# 预设的审核填写人
PRESET = {}

# 需要统计的企业
ENTERPRISES = [
    {
        "corp_id": "ww24afc21ef1097517",
        "corp_secret": "rynUFJpesJVPpIYNEGuhKCabTG4Dz61700nndiPMUBo"
    },
    {
        "corp_id": "ww7b5030278e2a0960",
        "corp_secret": "6y1OXssUXKjcKwrB-qqCCMsp6uNR_upz-baVCrGlgpQ"
    }
]

# 接收人配置（每个企业单独配置）
RECEIVERS = {
    "ww24afc21ef1097517": "TuWanZhen|DengLan_1"
}


def get_access_token(CORP_ID=None, CORP_SECRET=None):
    """获取访问令牌"""
    url = f"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={CORP_ID}&corpsecret={CORP_SECRET}"
    return requests.get(url).json()['access_token']


def upload_media(token, file_path):
    """上传临时素材"""
    url = f"https://qyapi.weixin.qq.com/cgi-bin/media/upload?access_token={token}&type=file"

    try:
        with open(file_path, 'rb') as f:
            files = {'media': f}
            response = requests.post(url, files=files, timeout=10)
        result = response.json()
        if result.get('errcode') == 0:
            return result['media_id']
        print(f"上传失败：{result.get('errmsg')}")
    except Exception as e:
        print(f"上传异常：{str(e)}")
    return None


def send_app_message(token, media_id, receiver):
    """发送应用消息"""
    url = f"https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={token}"

    payload = {
        "touser": receiver,
        "msgtype": "file",
        "agentid": AGENT_ID,
        "file": {
            "media_id": media_id
        }
    }

    try:
        response = requests.post(url, json=payload, timeout=10)
        result = response.json()
        if result.get('errcode') == 0:
            return True
        print(f"发送失败：{result.get('errmsg')}")
    except Exception as e:
        print(f"发送异常：{str(e)}")
    return False


def get_all_journaluuids(enterprise_config, start_time, end_time):
    token = get_access_token(enterprise_config['corp_id'], enterprise_config['corp_secret'])
    """获取全量日报记录（带分页处理）"""
    journaluuids = []
    cursor = 0
    while True:
        payload = {
            "starttime": start_time,
            "endtime": end_time,
            "cursor": cursor,
            "limit": 100
        }
        response = requests.post(
            "https://qyapi.weixin.qq.com/cgi-bin/oa/journal/get_record_list",
            params={"access_token": token},
            json=payload
        ).json()

        if response.get('errcode') != 0:
            break

        journaluuids.extend(response.get('journaluuid_list', []))

        if response.get('next_cursor', 0) == 0:
            break

        cursor = response['next_cursor']

    return token, journaluuids


def get_report_detail(token, journaluuid):
    """获取日报详情（含已读和评论信息）"""
    url = f"https://qyapi.weixin.qq.com/cgi-bin/oa/journal/get_record_detail?access_token={token}"
    response = requests.post(url, json={"journaluuid": journaluuid})
    return response.json()


def get_user_name(token, userid):
    """获取用户名"""
    try:
        url = f"https://qyapi.weixin.qq.com/cgi-bin/user/get?access_token={token}&userid={userid}"
        return requests.get(url, timeout=5).json().get('name', userid)
    except:
        return userid  # 网络异常时返回原始userid


def process_journaluuids(reader_dict, commenter_dict, token, journaluuids, report_dict):
    """多线程处理单个企业的记录"""
    with ThreadPoolExecutor(max_workers=50) as executor:
        futures = [executor.submit(process_single_journal, reader_dict, commenter_dict, token, journaluuid, report_dict)
                   for
                   journaluuid in journaluuids]
        for future in as_completed(futures):
            future.result()


def get_stat_list(token, template_id, starttime, endtime):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/oa/journal/get_stat_list?access_token={token}"
    response = requests.post(url, json={"template_id": template_id, "starttime": starttime, "endtime": endtime})
    res = response.json()
    return res


def process_single_journal(reader_dict, commenter_dict, token, journaluuid, report_dict):
    """处理单个日报记录"""
    detail = get_report_detail(token, journaluuid)
    if detail.get('errcode') != 0:
        return

    info = detail.get('info', {})
    submitter_name = get_user_name(token, info.get('submitter', {}).get('userid', ''))
    report_dict[submitter_name].add(submitter_name)
    # 处理阅读记录
    for receiver in info.get('readed_receivers', []):
        reader_name = get_user_name(token, receiver['userid'])
        if reader_name in PERSONS:
            reader_dict[reader_name].add(submitter_name)

    # 处理评论记录
    for comment in info.get('comments', []):
        commenter_name = get_user_name(token, comment['comment_userinfo']['userid'])
        if commenter_name in PERSONS:
            commenter_dict[commenter_name].add(submitter_name)


def process_preset():
    token = get_access_token('ww24afc21ef1097517', 'rynUFJpesJVPpIYNEGuhKCabTG4Dz61700nndiPMUBo')
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/get_records?access_token={token}"

    payload = {
        "docid": DOC_ID,
        "sheet_id": SHEET_ID
    }

    try:
        response = requests.post(url, json=payload, timeout=10)
        data = response.json()
        if data['errcode'] == 0:
            for record in data['records']:
                name = record['values']['日报审核人'][0]['text']
                PERSONS.append(name)
                PRESET[name] = set(record['values']['提交人'][0]['text'].split(","))

    except:
        print("获取预设人员失败")


def generate_excel_report():
    """生成Excel格式报告"""
    # 时间范围设置
    today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    start_time = int((today - timedelta(days=2)).replace(hour=17, minute=30).timestamp())
    end_time = int((today - timedelta(days=1)).replace(hour=12, minute=0).timestamp())
    # 初始化数据结构
    # {读者: 被读人集合}
    reader_dict = defaultdict(set)
    # {评论者: 被评人集合}
    commenter_dict = defaultdict(set)
    # 存储当日的缺报人员
    unreport_dict = defaultdict(set)
    # 当日的提交人员
    report_dict = defaultdict(set)
    # 获取预设的人员
    process_preset()
    # 遍历所有企业配置
    for enterprise in ENTERPRISES:
        try:
            token, journaluuids = get_all_journaluuids(enterprise, start_time, end_time)
            process_journaluuids(reader_dict, commenter_dict, token, journaluuids, report_dict)
        except Exception as e:
            print(f"处理企业 {enterprise['corp_id']} 时出错: {str(e)}")
            continue

    # 通过预设的人员，获取未提交日报的人员 使用PRESET里的人员
    for reviewer, names in PRESET.items():
        for name in names:
            if name not in report_dict:
                unreport_dict[name].add(name)

    # 构建结果数据
    result_data = []
    for user in PERSONS:
        read_targets = ", ".join(sorted(reader_dict.get(user, set())))
        comment_targets = ", ".join(sorted(commenter_dict.get(user, set())))
        predefined_targets = PRESET.get(user, set())
        read_targets_set = set(read_targets.split(", ")) if read_targets else set()
        unread_targets = predefined_targets.difference(read_targets_set)
        unread_targets = unread_targets.difference(unreport_dict.keys())
        # 判断是否是在需要输出的人员中
        result_data.append({
            "审核人": user,
            "已阅读的提交人": read_targets,
            "已评论的提交人": comment_targets,
            "未阅读的提交人": ", ".join(sorted(unread_targets))
        })

    # 创建DataFrame
    df = pd.DataFrame(result_data)

    # 生成文件名
    filename = f"人员阅读评论统计_{today.strftime('%Y%m%d')}.xlsx"

    # 写入Excel
    with pd.ExcelWriter(filename, engine='xlsxwriter') as writer:
        df.to_excel(writer, sheet_name='统计报表', index=False)

        # 获取工作表对象
        workbook = writer.book
        worksheet = writer.sheets['统计报表']

        # 设置格式
        header_format = workbook.add_format({
            'bold': True,
            'text_wrap': True,
            'valign': 'top',
            'fg_color': '#D7E4BC',
            'border': 1
        })

        # 应用格式
        for col_num, value in enumerate(df.columns.values):
            worksheet.write(0, col_num, value, header_format)
            worksheet.set_column(col_num, col_num, 30)  # 调整列宽

    print(f"\n报表已生成：{filename}")

    for enterprise in ENTERPRISES:
        corp_id = enterprise['corp_id']
        if corp_id in RECEIVERS:
            send_to_enterprise(
                corp_id=corp_id,
                secret=enterprise['corp_secret'],
                receiver=RECEIVERS[corp_id],
                file_path=filename
            )


def send_to_enterprise(corp_id, secret, receiver, file_path):
    """发送报表到指定企业用户"""
    token = get_access_token(corp_id, secret)
    # 上传文件
    media_id = upload_media(token, file_path)
    # 发送消息
    if media_id and send_app_message(token, media_id, receiver):
        print("文件发送成功")
    else:
        print("文件发送失败")


if __name__ == "__main__":
    generate_excel_report()
