# Generated by Django 2.2.13 on 2020-06-11 02:58

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Name',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.<PERSON>r<PERSON>ield(max_length=50)),
                ('author', models.Char<PERSON>ield(max_length=50)),
                ('stars', models.IntegerField()),
            ],
        ),
        migrations.CreateModel(
            name='Type',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('typename', models.<PERSON>r<PERSON><PERSON>(max_length=10)),
            ],
        ),
    ]
