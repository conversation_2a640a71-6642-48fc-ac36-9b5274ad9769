{% extends "base_layout.html" %} {% block title %}Flot{% endblock %} 
{% block head %}
    {{ super() }}
    <link rel="stylesheet" href="{{ url_for('serveStaticResource', resource='css/timeline.css') }}">
    <link rel="stylesheet" href="{{ url_for('serveStaticResource', resource='css/morris.css') }}">
{% endblock %} 
{% block content %}
    <div class="row">
        <div class="col-lg-12">
            <h1 class="page-header">Flot</h1>
        </div>
        <!-- /.col-lg-12 -->
    </div>
    <!-- /.row -->
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    Line Chart Example
                </div>
                <!-- /.panel-heading -->
                <div class="panel-body">
                    <div class="flot-chart">
                        <div class="flot-chart-content" id="flot-line-chart"></div>
                    </div>
                </div>
                <!-- /.panel-body -->
            </div>
            <!-- /.panel -->
        </div>
        <!-- /.col-lg-12 -->
        <div class="col-lg-6">
            <div class="panel panel-default">
                <div class="panel-heading">
                    Pie Chart Example
                </div>
                <!-- /.panel-heading -->
                <div class="panel-body">
                    <div class="flot-chart">
                        <div class="flot-chart-content" id="flot-pie-chart"></div>
                    </div>
                </div>
                <!-- /.panel-body -->
            </div>
            <!-- /.panel -->
        </div>
        <!-- /.col-lg-6 -->
        <div class="col-lg-6">
            <div class="panel panel-default">
                <div class="panel-heading">
                    Multiple Axes Line Chart Example
                </div>
                <!-- /.panel-heading -->
                <div class="panel-body">
                    <div class="flot-chart">
                        <div class="flot-chart-content" id="flot-line-chart-multi"></div>
                    </div>
                </div>
                <!-- /.panel-body -->
            </div>
            <!-- /.panel -->
        </div>
        <!-- /.col-lg-6 -->
        <div class="col-lg-6">
            <div class="panel panel-default">
                <div class="panel-heading">
                    Moving Line Chart Example
                </div>
                <!-- /.panel-heading -->
                <div class="panel-body">
                    <div class="flot-chart">
                        <div class="flot-chart-content" id="flot-line-chart-moving"></div>
                    </div>
                </div>
                <!-- /.panel-body -->
            </div>
            <!-- /.panel -->
        </div>
        <!-- /.col-lg-6 -->
        <div class="col-lg-6">
            <div class="panel panel-default">
                <div class="panel-heading">
                    Bar Chart Example
                </div>
                <!-- /.panel-heading -->
                <div class="panel-body">
                    <div class="flot-chart">
                        <div class="flot-chart-content" id="flot-bar-chart"></div>
                    </div>
                </div>
                <!-- /.panel-body -->
            </div>
            <!-- /.panel -->
        </div>
        <!-- /.col-lg-6 -->
        <div class="col-lg-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    Flot Charts Usage
                </div>
                <!-- /.panel-heading -->
                <div class="panel-body">
                    <p>Flot is a pure JavaScript plotting library for jQuery, with a focus on simple usage, attractive looks, and interactive features. In SB Admin, we are using the most recent version of Flot along with a few plugins to enhance the user experience. The Flot plugins being used are the tooltip plugin for hoverable tooltips, and the resize plugin for fully responsive charts. The documentation for Flot Charts is available on their website, <a target="_blank" href="http://www.flotcharts.org/">http://www.flotcharts.org/</a>.</p>
                    <a target="_blank" class="btn btn-default btn-lg btn-block" href="http://www.flotcharts.org/">View Flot Charts Documentation</a>
                </div>
                <!-- /.panel-body -->
            </div>
            <!-- /.panel -->
        </div>
        <!-- /.col-lg-6 -->
    </div>
    <!-- /.row -->
    
{% endblock %}
{% block js %}
    {{ super() }}
    <script src="{{ url_for('serveStaticResource', resource='js/excanvas.min.js')}}"></script>
    <script src="{{ url_for('serveStaticResource', resource='js/jquery.flot.js')}}"></script>
    <script src="{{ url_for('serveStaticResource', resource='js/jquery.flot.pie.js')}}"></script>
    <script src="{{ url_for('serveStaticResource', resource='js/jquery.flot.resize.js')}}"></script>
    <script src="{{ url_for('serveStaticResource', resource='js/jquery.flot.time.js')}}"></script>
    <script src="{{ url_for('serveStaticResource', resource='js/jquery.flot.tooltip.min.js')}}"></script>
    <script src="{{ url_for('serveStaticResource', resource='js/flot-data.js')}}"></script>
{% endblock %}